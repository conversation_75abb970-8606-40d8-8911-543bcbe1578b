[0m[[0m[0mdebug[0m] [0m[0mscalafmt: Adding repositories [https://repo1.maven.org/maven2/][0m
[0m[[0m[0mdebug[0m] [0m[0mscalafmt: Adding credentials [][0m
[0m[[0m[0mdebug[0m] [0m[0mscalafmt: considering all files (no git)[0m
[0m[[0m[0mdebug[0m] [0m[0mscalafmt: Change report:[0m
[0m[[0m[0mdebug[0m] [0m[0m	Checked: C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\test\scala\Arbitraries.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\test\scala\storage\DbSuite.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\test\scala\Rsf.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\test\scala\Ewrc.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\test\scala\Results.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\test\scala\iron.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\test\scala\Sharded.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\test\scala\Snapshot.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\test\scala\Util.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\test\scala\difflicious.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\test\scala\PressAuto.scala[0m
[0m[[0m[0mdebug[0m] [0m[0m	Modified: C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\test\scala\Arbitraries.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\test\scala\storage\DbSuite.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\test\scala\Rsf.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\test\scala\Ewrc.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\test\scala\Results.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\test\scala\iron.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\test\scala\Sharded.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\test\scala\Snapshot.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\test\scala\Util.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\test\scala\difflicious.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\test\scala\PressAuto.scala[0m
[0m[[0m[0mdebug[0m] [0m[0m	Unmodified: [0m
[0m[[0m[0mdebug[0m] [0m[0m	Added: C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\test\scala\Arbitraries.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\test\scala\storage\DbSuite.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\test\scala\Rsf.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\test\scala\Ewrc.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\test\scala\Results.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\test\scala\iron.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\test\scala\Sharded.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\test\scala\Snapshot.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\test\scala\Util.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\test\scala\difflicious.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\test\scala\PressAuto.scala[0m
[0m[[0m[0mdebug[0m] [0m[0m	Removed: [0m
[0m[[0m[0minfo[0m] [0m[0mscalafmt: Formatting 11 Scala sources (C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend)...[0m
[0m[[0m[0mdebug[0m] [0m[0mscalafmt: Unchanged 11 Scala sources[0m
