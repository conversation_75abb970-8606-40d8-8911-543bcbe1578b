# EWRC Fetcher

Python scripts to fetch rally results from [ewrc-results.com](https://www.ewrc-results.com/).

This implementation is based on the RallyEye Scala project and provides comprehensive rally data extraction.

## Scripts

### 🚀 **ewrc_complete.py** (Recommended)
Complete rally data fetcher with all features:
- ✅ **Rally Information**: Name, championship, dates, distance, entries
- ✅ **Stage Results**: Stage-by-stage timing data for all drivers
- ✅ **Driver Details**: Country, car, group classifications
- ✅ **Retirement Data**: Information about retired drivers
- ✅ **Penalties**: Automatic penalty extraction from dedicated penalty pages
- ✅ **Time Parsing**: Converts all times to milliseconds with precision
- ✅ **JSON Export**: Saves complete data to JSON file
- ✅ **Robust Error Handling**: Handles missing stages, parsing errors, network issues

### 📝 **ewrc_fetcher.py** (Basic)
Basic rally data fetcher with core functionality.

## Installation

1. **Install Python dependencies:**
```bash
pip install -r requirements.txt
```

## Usage

### Complete Fetcher (Recommended)
```bash
python ewrc_complete.py "<rally_id>"
```

### Basic Fetcher
```bash
python ewrc_fetcher.py "<rally_id>"
```

### Examples
```bash
# 2024 WRC Monte Carlo Rally
python ewrc_complete.py "2024/wrc/monte-carlo"

# 2024 WRC Rally Sweden
python ewrc_complete.py "2024/wrc/sweden"

# Greek Rally (with numeric ID)
python ewrc_complete.py "93042-olympiako-rally-2025"
```

### Finding Rally IDs

Rally IDs can be found in the URL structure of ewrc-results.com:
- Go to https://www.ewrc-results.com/
- Navigate to a rally's results page
- The URL will be like: `https://www.ewrc-results.com/final/2024/wrc/monte-carlo/`
- The rally ID is: `2024/wrc/monte-carlo`

## Output

### Console Output
The script displays:
- Rally information summary
- Stage-by-stage breakdown
- Total entries and statistics

### JSON File
Creates a file named `ewrc_complete_results_<rally_id>.json` containing:
```json
{
  "rally_info": {
    "name": "Rally Name",
    "championship": ["WRC"],
    "start_date": "15 March 2024",
    "end_date": "17 March 2024",
    "distance_meters": 123456,
    "total_entries": 67,
    "finished": 45
  },
  "results": [
    {
      "stage_number": 1,
      "stage_name": "Stage Name",
      "country": "finland",
      "driver_codriver": "Driver Name / Codriver Name",
      "entry_number": "1",
      "group": ["WRC"],
      "car": "Toyota GR Yaris Rally1",
      "stage_time_ms": 123456,
      "overall_time_ms": null,
      "penalties_ms": 0,
      "super_rally": false,
      "active": true,
      "nominal_time": false
    }
  ]
}
```

## Data Structure

### RallyInfo
- `name`: Rally name
- `championship`: List of championships (e.g., ["WRC", "WRC2"])
- `start_date`: Rally start date
- `end_date`: Rally end date
- `distance_meters`: Total rally distance in meters
- `total_entries`: Total number of entries
- `finished`: Number of drivers who finished

### Entry
- `stage_number`: Stage number (1, 2, 3, ...)
- `stage_name`: Stage name
- `country`: Driver's country
- `driver_codriver`: Driver and codriver names
- `entry_number`: Car number
- `group`: Competition groups (WRC, WRC2, etc.)
- `car`: Car make and model
- `stage_time_ms`: Stage time in milliseconds
- `overall_time_ms`: Overall time (not available in stage results)
- `penalties_ms`: Penalties in milliseconds
- `super_rally`: Whether driver is on Super Rally
- `active`: Whether driver is still active in rally
- `nominal_time`: Whether time is nominal

## Error Handling

The script includes robust error handling for:
- Network connectivity issues
- HTML parsing errors
- Missing data elements
- Invalid rally IDs

## Dependencies

- `requests`: HTTP requests
- `beautifulsoup4`: HTML parsing
- `lxml`: XML/HTML parser (faster than default)

## Based On

This Python implementation is based on the Scala code from the [RallyEye](https://github.com/2m/rallyeye) project, specifically the `Ewrc.scala` module.
