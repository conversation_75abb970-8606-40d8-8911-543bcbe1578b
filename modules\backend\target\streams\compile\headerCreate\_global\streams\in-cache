[{"file": "file:///C:/Users/<USER>/Documents/Projects/scalaproject/modules/backend/src/main/scala/Results.scala", "lastModified": 1747639102000}, {"file": "file:///C:/Users/<USER>/Documents/Projects/scalaproject/modules/backend/src/main/resources/META-INF/native-image/lt.dvim.rallyeye/backend/serialization-config.json", "lastModified": 1747639102000}, {"file": "file:///C:/Users/<USER>/Documents/Projects/scalaproject/modules/backend/src/main/resources/pressauto2023.csv", "lastModified": 1747639102000}, {"file": "file:///C:/Users/<USER>/Documents/Projects/scalaproject/modules/backend/src/main/resources/db/V002__add_penalty_columns.sql", "lastModified": 1747639102000}, {"file": "file:///C:/Users/<USER>/Documents/Projects/scalaproject/modules/backend/src/main/resources/META-INF/native-image/lt.dvim.rallyeye/backend/reflect-config.json", "lastModified": 1747639102000}, {"file": "file:///C:/Users/<USER>/Documents/Projects/scalaproject/modules/backend/src/main/resources/META-INF/native-image/lt.dvim.rallyeye/backend/resource-config.json", "lastModified": 1747639102000}, {"file": "file:///C:/Users/<USER>/Documents/Projects/scalaproject/modules/backend/src/main/resources/META-INF", "lastModified": 1747639102000}, {"file": "file:///C:/Users/<USER>/Documents/Projects/scalaproject/modules/backend/src/main/scala/storage/Db.scala", "lastModified": 1747639102000}, {"file": "file:///C:/Users/<USER>/Documents/Projects/scalaproject/modules/backend/src/main/scala/storage/Model.scala", "lastModified": 1747639102000}, {"file": "file:///C:/Users/<USER>/Documents/Projects/scalaproject/modules/backend/src/main/resources/META-INF/native-image/lt.dvim.rallyeye", "lastModified": 1747639102000}, {"file": "file:///C:/Users/<USER>/Documents/Projects/scalaproject/modules/backend/src/main/resources/META-INF/native-image/lt.dvim.rallyeye/backend/jni-config.json", "lastModified": 1747639102000}, {"file": "file:///C:/Users/<USER>/Documents/Projects/scalaproject/modules/backend/src/main/scala/Sharded.scala", "lastModified": 1747639102000}, {"file": "file:///C:/Users/<USER>/Documents/Projects/scalaproject/modules/backend/src/main/resources/db/V005__convert_group_to_array.sql", "lastModified": 1747639102000}, {"file": "file:///C:/Users/<USER>/Documents/Projects/scalaproject/modules/backend/src/main/scala/SmokeRun.scala", "lastModified": 1747639102000}, {"file": "file:///C:/Users/<USER>/Documents/Projects/scalaproject/modules/backend/src/main/resources/db", "lastModified": 1747639102000}, {"file": "file:///C:/Users/<USER>/Documents/Projects/scalaproject/modules/backend/src/main/scala/storage/GraalVMResourceProvider.scala", "lastModified": 1747639102000}, {"file": "file:///C:/Users/<USER>/Documents/Projects/scalaproject/modules/backend/src/main/resources/META-INF/native-image/lt.dvim.rallyeye/backend/predefined-classes-config.json", "lastModified": 1747639102000}, {"file": "file:///C:/Users/<USER>/Documents/Projects/scalaproject/modules/backend/src/main/resources/pressauto2024.csv", "lastModified": 1747639102000}, {"file": "file:///C:/Users/<USER>/Documents/Projects/scalaproject/modules/backend/src/main/scala/loader/PressAuto.scala", "lastModified": 1747639102000}, {"file": "file:///C:/Users/<USER>/Documents/Projects/scalaproject/modules/backend/src/main/resources/META-INF/native-image", "lastModified": 1747639102000}, {"file": "file:///C:/Users/<USER>/Documents/Projects/scalaproject/modules/backend/src/main/scala/storage/Repo.scala", "lastModified": 1747639102000}, {"file": "file:///C:/Users/<USER>/Documents/Projects/scalaproject/modules/backend/src/main/scala/Telemetry.scala", "lastModified": 1747639102000}, {"file": "file:///C:/Users/<USER>/Documents/Projects/scalaproject/modules/backend/src/main/resources/META-INF/native-image/lt.dvim.rallyeye/backend/proxy-config.json", "lastModified": 1747639102000}, {"file": "file:///C:/Users/<USER>/Documents/Projects/scalaproject/modules/backend/src/main/resources/db/V001__create_initial_tables.sql", "lastModified": 1747639102000}, {"file": "file:///C:/Users/<USER>/Documents/Projects/scalaproject/modules/backend/src/main/scala/storage/Migrations.scala", "lastModified": 1747639102000}, {"file": "file:///C:/Users/<USER>/Documents/Projects/scalaproject/modules/backend/src/main/resources/db/V003__add_rally_info_columns.sql", "lastModified": 1747639102000}, {"file": "file:///C:/Users/<USER>/Documents/Projects/scalaproject/modules/backend/src/main/scala/Util.scala", "lastModified": 1747639102000}, {"file": "file:///C:/Users/<USER>/Documents/Projects/scalaproject/modules/backend/src/main/scala/Main.scala", "lastModified": 1747639102000}, {"file": "file:///C:/Users/<USER>/Documents/Projects/scalaproject/modules/backend/src/main/scala/RallyEye.scala", "lastModified": 1747639102000}, {"file": "file:///C:/Users/<USER>/Documents/Projects/scalaproject/modules/backend/src/main/scala/Ewrc.scala", "lastModified": 1747639102000}, {"file": "file:///C:/Users/<USER>/Documents/Projects/scalaproject/modules/backend/src/main/resources/db/V004__convert_championship_to_array.sql", "lastModified": 1747639102000}, {"file": "file:///C:/Users/<USER>/Documents/Projects/scalaproject/modules/backend/src/main/scala/Rsf.scala", "lastModified": 1747639102000}, {"file": "file:///C:/Users/<USER>/Documents/Projects/scalaproject/modules/backend/src/main/scala/PressAuto.scala", "lastModified": 1747639102000}, {"file": "file:///C:/Users/<USER>/Documents/Projects/scalaproject/modules/backend/src/main/resources/logback.xml", "lastModified": 1747639102000}, {"file": "file:///C:/Users/<USER>/Documents/Projects/scalaproject/modules/backend/src/main/resources/META-INF/native-image/lt.dvim.rallyeye/backend", "lastModified": 1747639102000}, {"file": "file:///C:/Users/<USER>/Documents/Projects/scalaproject/modules/backend/src/main/resources", "lastModified": 1747639102000}]