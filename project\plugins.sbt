addSbtPlugin("org.scala-js"                % "sbt-scalajs"              % "1.19.0")
addSbtPlugin("org.scalablytyped.converter" % "sbt-converter"            % "1.0.0-beta44")
addSbtPlugin("org.scalameta"               % "sbt-scalafmt"             % "2.5.4")
addSbtPlugin("de.he<PERSON><PERSON><PERSON>"           % "sbt-header"               % "5.10.0")
addSbtPlugin("org.typelevel"               % "sbt-tpolecat"             % "0.5.2")
addSbtPlugin("io.spray"                    % "sbt-revolver"             % "0.10.0")
addSbtPlugin("com.github.sbt"              % "sbt-dynver"               % "5.1.0")
addSbtPlugin("com.eed3si9n"                % "sbt-buildinfo"            % "0.13.1")
addSbtPlugin("org.portable-scala"          % "sbt-scalajs-crossproject" % "1.3.2")
addSbtPlugin("org.scalameta"               % "sbt-native-image"         % "0.3.4")
addSbtPlugin("se.marc<PERSON><PERSON><PERSON>"           % "sbt-docker"               % "1.11.0")
addSbtPlugin("nl.gn0s1s"                   % "sbt-dotenv"               % "3.1.1")
