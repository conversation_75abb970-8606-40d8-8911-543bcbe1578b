C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\scala-js\sbt-scalajs_2.12_1.0\1.19.0\sbt-scalajs_2.12_1.0-1.19.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\scalablytyped\converter\sbt-converter_2.12_1.0\1.0.0-beta44\sbt-converter_2.12_1.0-1.0.0-beta44.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\scalameta\sbt-scalafmt_2.12_1.0\2.5.4\sbt-scalafmt_2.12_1.0-2.5.4.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\de\heikoseeberger\sbt-header_2.12_1.0\5.10.0\sbt-header-5.10.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\typelevel\sbt-tpolecat_2.12_1.0\0.5.2\sbt-tpolecat_2.12_1.0-0.5.2.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\io\spray\sbt-revolver_2.12_1.0\0.10.0\sbt-revolver-0.10.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\github\sbt\sbt-dynver_2.12_1.0\5.1.0\sbt-dynver_2.12_1.0-5.1.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\eed3si9n\sbt-buildinfo_2.12_1.0\0.13.1\sbt-buildinfo_2.12_1.0-0.13.1.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\portable-scala\sbt-scalajs-crossproject_2.12_1.0\1.3.2\sbt-scalajs-crossproject_2.12_1.0-1.3.2.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\scalameta\sbt-native-image_2.12_1.0\0.3.4\sbt-native-image-0.3.4.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\se\marcuslonnberg\sbt-docker_2.12_1.0\1.11.0\sbt-docker-1.11.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\nl\gn0s1s\sbt-dotenv_2.12_1.0\3.1.1\sbt-dotenv_2.12_1.0-3.1.1.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\scala-js\scalajs-linker-interface_2.12\1.19.0\scalajs-linker-interface_2.12-1.19.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\scala-js\scalajs-sbt-test-adapter_2.12\1.19.0\scalajs-sbt-test-adapter_2.12-1.19.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\portable-scala\sbt-platform-deps_2.12_1.0\1.0.2\sbt-platform-deps_2.12_1.0-1.0.2.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\scala-js\scalajs-js-envs_2.12\1.4.0\scalajs-js-envs_2.12-1.4.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\scala-js\scalajs-env-nodejs_2.12\1.4.0\scalajs-env-nodejs_2.12-1.4.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\scalablytyped\converter\importer-portable_2.12\1.0.0-beta44\importer-portable_2.12-1.0.0-beta44.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\ch\epfl\scala\sbt-scalajs-bundler_2.12_1.0\0.21.1\sbt-scalajs-bundler-0.21.1.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\software\amazon\awssdk\s3\2.15.28\s3-2.15.28.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\googlecode\java-diff-utils\diffutils\1.3.0\diffutils-1.3.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\scalameta\scalafmt-sysops_2.12\3.8.5\scalafmt-sysops_2.12-3.8.5.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\scalameta\scalafmt-dynamic_2.12\3.8.5\scalafmt-dynamic_2.12-3.8.5.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\typelevel\scalac-options_2.12\0.1.7\scalac-options_2.12-0.1.7.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\github\sbt\dynver_2.12\5.1.0\dynver_2.12-5.1.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\portable-scala\sbt-crossproject_2.12_1.0\1.3.2\sbt-crossproject_2.12_1.0-1.3.2.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\apache\commons\commons-lang3\3.12.0\commons-lang3-3.12.0.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\lib\scala-library.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\scala-js\scalajs-ir_2.12\1.19.0\scalajs-ir_2.12-1.19.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\scala-js\scalajs-logging_2.12\1.1.1\scalajs-logging_2.12-1.1.1.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\scala-sbt\test-interface\1.0\test-interface-1.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\google\jimfs\jimfs\1.2\jimfs-1.2.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\scalablytyped\converter\ts_2.12\1.0.0-beta44\ts_2.12-1.0.0-beta44.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\scalablytyped\converter\scalajs_2.12\1.0.0-beta44\scalajs_2.12-1.0.0-beta44.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\scalablytyped\converter\phases_2.12\1.0.0-beta44\phases_2.12-1.0.0-beta44.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\typesafe\play\play-json_2.12\2.9.2\play-json_2.12-2.9.2.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\software\amazon\awssdk\aws-xml-protocol\2.15.28\aws-xml-protocol-2.15.28.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\software\amazon\awssdk\protocol-core\2.15.28\protocol-core-2.15.28.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\software\amazon\awssdk\arns\2.15.28\arns-2.15.28.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\software\amazon\awssdk\profiles\2.15.28\profiles-2.15.28.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\software\amazon\awssdk\sdk-core\2.15.28\sdk-core-2.15.28.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\software\amazon\awssdk\auth\2.15.28\auth-2.15.28.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\software\amazon\awssdk\http-client-spi\2.15.28\http-client-spi-2.15.28.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\software\amazon\awssdk\regions\2.15.28\regions-2.15.28.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\software\amazon\awssdk\annotations\2.15.28\annotations-2.15.28.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\software\amazon\awssdk\utils\2.15.28\utils-2.15.28.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\software\amazon\awssdk\aws-core\2.15.28\aws-core-2.15.28.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\software\amazon\awssdk\metrics-spi\2.15.28\metrics-spi-2.15.28.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\software\amazon\awssdk\apache-client\2.15.28\apache-client-2.15.28.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\software\amazon\awssdk\netty-nio-client\2.15.28\netty-nio-client-2.15.28.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\github\bigwheel\util-backports_2.12\2.1\util-backports_2.12-2.1.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\scalameta\scalafmt-interfaces\3.8.5\scalafmt-interfaces-3.8.5.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\io\get-coursier\interface\1.0.26\interface-1.0.26.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\typesafe\config\1.4.3\config-1.4.3.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\google\guava\guava\30.1-android\guava-30.1-android.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\scalablytyped\converter\core_2.12\1.0.0-beta44\core_2.12-1.0.0-beta44.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\scalablytyped\converter\logging_2.12\1.0.0-beta44\logging_2.12-1.0.0-beta44.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\scala-lang\modules\scala-parser-combinators_2.12\1.1.2\scala-parser-combinators_2.12-1.1.2.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\scala-lang\modules\scala-xml_2.12\2.2.0\scala-xml_2.12-2.2.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\typesafe\play\play-functional_2.12\2.9.2\play-functional_2.12-2.9.2.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\lib\scala-reflect.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\fasterxml\jackson\core\jackson-core\2.10.5\jackson-core-2.10.5.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\fasterxml\jackson\core\jackson-annotations\2.10.5\jackson-annotations-2.10.5.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.10.5\jackson-datatype-jdk8-2.10.5.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.10.5\jackson-datatype-jsr310-2.10.5.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\fasterxml\jackson\core\jackson-databind\2.10.5.1\jackson-databind-2.10.5.1.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\software\amazon\awssdk\aws-query-protocol\2.15.28\aws-query-protocol-2.15.28.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\slf4j\slf4j-api\1.7.36\slf4j-api-1.7.36.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\reactivestreams\reactive-streams\1.0.3\reactive-streams-1.0.3.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\software\amazon\eventstream\eventstream\1.0.1\eventstream-1.0.1.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\apache\httpcomponents\httpclient\4.5.13\httpclient-4.5.13.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\apache\httpcomponents\httpcore\4.4.13\httpcore-4.4.13.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\io\netty\netty-codec-http\4.1.53.Final\netty-codec-http-4.1.53.Final.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\io\netty\netty-codec-http2\4.1.53.Final\netty-codec-http2-4.1.53.Final.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\io\netty\netty-codec\4.1.53.Final\netty-codec-4.1.53.Final.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\io\netty\netty-transport\4.1.53.Final\netty-transport-4.1.53.Final.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\io\netty\netty-common\4.1.53.Final\netty-common-4.1.53.Final.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\io\netty\netty-buffer\4.1.53.Final\netty-buffer-4.1.53.Final.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\io\netty\netty-handler\4.1.53.Final\netty-handler-4.1.53.Final.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\io\netty\netty-transport-native-epoll\4.1.53.Final\netty-transport-native-epoll-4.1.53.Final-linux-x86_64.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\typesafe\netty\netty-reactive-streams-http\2.0.4\netty-reactive-streams-http-2.0.4.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\google\guava\failureaccess\1.0.1\failureaccess-1.0.1.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\google\guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\google\code\findbugs\jsr305\3.0.2\jsr305-3.0.2.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\checkerframework\checker-compat-qual\2.5.5\checker-compat-qual-2.5.5.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\google\errorprone\error_prone_annotations\2.3.4\error_prone_annotations-2.3.4.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\google\j2objc\j2objc-annotations\1.3\j2objc-annotations-1.3.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\lihaoyi\ammonite-ops_2.12\2.4.0\ammonite-ops_2.12-2.4.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\lihaoyi\os-lib_2.12\0.9.1\os-lib_2.12-0.9.1.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\lihaoyi\sourcecode_2.12\0.3.1\sourcecode_2.12-0.3.1.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\scalablytyped\circe013\circe-generic_2.12\0.13.0-shaded-2\circe-generic_2.12-0.13.0-shaded-2.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\scalablytyped\circe013\circe-jackson29_2.12\0.13.0-shaded-2\circe-jackson29_2.12-0.13.0-shaded-2.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\lihaoyi\fansi_2.12\0.4.0\fansi_2.12-0.4.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\commons-logging\commons-logging\1.2\commons-logging-1.2.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\commons-codec\commons-codec\1.11\commons-codec-1.11.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\io\netty\netty-resolver\4.1.53.Final\netty-resolver-4.1.53.Final.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\io\netty\netty-transport-native-unix-common\4.1.53.Final\netty-transport-native-unix-common-4.1.53.Final.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\typesafe\netty\netty-reactive-streams\2.0.4\netty-reactive-streams-2.0.4.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\scala-lang\modules\scala-collection-compat_2.12\2.4.1\scala-collection-compat_2.12-2.4.1.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\lihaoyi\geny_2.12\1.0.0\geny_2.12-1.0.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\scalablytyped\circe013\circe-core_2.12\0.13.0-shaded-2\circe-core_2.12-0.13.0-shaded-2.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\chuusai\shapeless_2.12\2.3.3\shapeless_2.12-2.3.3.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\scalablytyped\circe013\circe-numbers_2.12\0.13.0-shaded-2\circe-numbers_2.12-0.13.0-shaded-2.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\typelevel\cats-core_2.12\2.1.0\cats-core_2.12-2.1.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\typelevel\macro-compat_2.12\1.1.1\macro-compat_2.12-1.1.1.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\typelevel\cats-macros_2.12\2.1.0\cats-macros_2.12-2.1.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\typelevel\cats-kernel_2.12\2.1.0\cats-kernel_2.12-2.1.0.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\lib\scala-compiler.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\lib\scala-xml_2.12-2.3.0.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\actions_2.12-1.10.11.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\caffeine-2.8.5.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\checker-qual-3.4.1.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\collections_2.12-1.10.11.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\command_2.12-1.10.11.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\compiler-bridge_2.12-1.10.8.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\compiler-interface-1.10.8.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\completion_2.12-1.10.11.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\config-1.4.2.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\core-macros_2.12-1.10.11.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\disruptor-3.4.2.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\error_prone_annotations-2.4.0.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\file-tree-views-2.1.12.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\gigahorse-apache-http_2.12-0.7.0.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\gigahorse-core_2.12-0.7.0.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\io_2.12-1.10.5.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\ipcsocket-1.6.3.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\ivy-2.3.0-sbt-77cc781d727b367d3761f097d89f5a4762771d41.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\jansi-2.4.1.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\jline-2.14.7-sbt-9a88bc413e2b34a4580c001c654d1a7f4f65bf18.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\jline-builtins-3.27.1.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\jline-native-3.27.1.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\jline-reader-3.27.1.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\jline-style-3.27.1.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\jline-terminal-3.27.1.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\jline-terminal-jni-3.27.1.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\jna-5.12.0.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\jna-platform-5.12.0.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\jsch-0.2.23.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\launcher-interface-1.4.4.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\librarymanagement-core_2.12-1.10.4.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\librarymanagement-ivy_2.12-1.10.4.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\lm-coursier-shaded_2.12-2.1.8.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\log4j-api-2.17.1.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\log4j-core-2.17.1.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\log4j-slf4j-impl-2.17.1.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\logic_2.12-1.10.11.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\main-settings_2.12-1.10.11.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\main_2.12-1.10.11.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\protocol_2.12-1.10.11.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\reactive-streams-1.0.3.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\run_2.12-1.10.11.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\sbinary_2.12-0.5.1.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\sbt-1.10.11.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\scala-collection-compat_2.12-2.13.0.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\scala-compiler-2.12.20.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\scala-library-2.12.20.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\scala-parser-combinators_2.12-1.1.2.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\scala-reflect-2.12.20.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\scala-xml_2.12-2.3.0.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\scripted-plugin_2.12-1.10.11.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\shaded-apache-httpasyncclient-0.7.0.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\shaded-jawn-parser_2.12-1.3.2.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\shaded-scalajson_2.12-1.0.0-M4.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\sjson-new-core_2.12-0.10.1.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\sjson-new-murmurhash_2.12-0.10.1.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\sjson-new-scalajson_2.12-0.10.1.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\slf4j-api-1.7.36.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\ssl-config-core_2.12-0.6.1.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\task-system_2.12-1.10.11.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\tasks_2.12-1.10.11.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\template-resolver-0.1.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\test-agent-1.10.11.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\test-interface-1.0.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\testing_2.12-1.10.11.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\util-cache_2.12-1.10.11.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\util-control_2.12-1.10.11.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\util-interface-1.10.11.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\util-logging_2.12-1.10.11.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\util-position_2.12-1.10.11.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\util-relation_2.12-1.10.11.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\util-tracking_2.12-1.10.11.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\zero-allocation-hashing-0.16.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\zinc-apiinfo_2.12-1.10.8.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\zinc-classfile_2.12-1.10.8.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\zinc-classpath_2.12-1.10.8.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\zinc-compile-core_2.12-1.10.8.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\zinc-compile_2.12-1.10.8.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\zinc-core_2.12-1.10.8.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\zinc-lm-integration_2.12-1.10.11.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\zinc-persist-core-assembly-1.10.8.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\zinc-persist_2.12-1.10.8.jar;C:\Users\<USER>\.sbt\boot\scala-2.12.20\org.scala-sbt\sbt\1.10.11\zinc_2.12-1.10.8.jar
