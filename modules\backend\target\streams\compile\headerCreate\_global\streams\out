[0m[[0m[0mdebug[0m] [0m[0mAbout to create/update header for C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\main\scala\PressAuto.scala[0m
[0m[[0m[0mdebug[0m] [0m[0mAbout to create/update header for C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\main\scala\storage\Repo.scala[0m
[0m[[0m[0mdebug[0m] [0m[0mAbout to create/update header for C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\main\scala\storage\GraalVMResourceProvider.scala[0m
[0m[[0m[0mdebug[0m] [0m[0mAbout to create/update header for C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\main\scala\Rsf.scala[0m
[0m[[0m[0mdebug[0m] [0m[0mAbout to create/update header for C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\main\scala\Telemetry.scala[0m
[0m[[0m[0mdebug[0m] [0m[0mAbout to create/update header for C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\main\scala\Sharded.scala[0m
[0m[[0m[0mdebug[0m] [0m[0mAbout to create/update header for C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\main\scala\Results.scala[0m
[0m[[0m[0mdebug[0m] [0m[0mAbout to create/update header for C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\main\scala\Util.scala[0m
[0m[[0m[0mdebug[0m] [0m[0mAbout to create/update header for C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\main\scala\Main.scala[0m
[0m[[0m[0mdebug[0m] [0m[0mAbout to create/update header for C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\main\scala\storage\Db.scala[0m
[0m[[0m[0mdebug[0m] [0m[0mAbout to create/update header for C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\main\scala\loader\PressAuto.scala[0m
[0m[[0m[0mdebug[0m] [0m[0mAbout to create/update header for C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\main\scala\SmokeRun.scala[0m
[0m[[0m[0mdebug[0m] [0m[0mAbout to create/update header for C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\main\scala\storage\Migrations.scala[0m
[0m[[0m[0mdebug[0m] [0m[0mAbout to create/update header for C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\main\scala\Ewrc.scala[0m
[0m[[0m[0mdebug[0m] [0m[0mAbout to create/update header for C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\main\scala\RallyEye.scala[0m
[0m[[0m[0mdebug[0m] [0m[0mAbout to create/update header for C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\main\scala\storage\Model.scala[0m
