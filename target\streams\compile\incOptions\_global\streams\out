[0m[[0m[0mdebug[0m] [0m[0mCreated transactional ClassFileManager with tempDir = C:\Users\<USER>\Documents\Projects\scalaproject\target\scala-3.6.4\classes.bak[0m
[0m[[0m[0mdebug[0m] [0m[0mAbout to delete class files:[0m
[0m[[0m[0mdebug[0m] [0m[0mWe backup class files:[0m
[0m[[0m[0mdebug[0m] [0m[0mCreated transactional ClassFileManager with tempDir = C:\Users\<USER>\Documents\Projects\scalaproject\target\scala-3.6.4\classes.bak[0m
[0m[[0m[0mdebug[0m] [0m[0mRemoving the temporary directory used for backing up class files: C:\Users\<USER>\Documents\Projects\scalaproject\target\scala-3.6.4\classes.bak[0m
