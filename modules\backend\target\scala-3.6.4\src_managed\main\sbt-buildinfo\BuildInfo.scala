// $COVERAGE-OFF$
package rallyeye

/** This object was generated by sbt-buildinfo. */
case object BuildInfo {
  /** The value is true. */
  val isSnapshot: scala.Boolean = true
  /** The value is new java.io.File("C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\src\\main\\resources"). */
  val resourceDirectory: java.io.File = new java.io.File("C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\src\\main\\resources")
  /** The value is new java.io.File("C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\src\\test\\resources"). */
  val test_resourceDirectory: java.io.File = new java.io.File("C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\src\\test\\resources")
  override val toString: String = {
    "isSnapshot: %s, resourceDirectory: %s, test_resourceDirectory: %s".format(
      isSnapshot, resourceDirectory, test_resourceDirectory
    )
  }
}
// $COVERAGE-ON$
