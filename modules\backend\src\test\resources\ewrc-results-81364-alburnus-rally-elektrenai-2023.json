[{"stageNumber": 1, "stageName": "A-mega", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["2WD", "LARČ6"], "car": "BMW 328 Ti Compact E46", "stageTimeMs": 0, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": false, "comment": []}, {"stageNumber": 1, "stageName": "A-mega", "country": "lithuania", "userName": "Žala Vaidotas - Pūķis Ivo", "realName": "", "group": ["AWD"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 228320, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "A-mega", "country": "lithuania", "userName": "Notkus <PERSON>rius - Strižanas D.", "realName": "", "group": ["AWD", "LARČ2"], "car": "Škoda Fabia R5", "stageTimeMs": 234590, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "A-mega", "country": "lithuania", "userName": "<PERSON><PERSON> - <PERSON><PERSON><PERSON>kas E.", "realName": "", "group": ["AWD", "LARČ1"], "car": "Škoda Fabia N5+", "stageTimeMs": 235480, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "A-mega", "country": "lithuania", "userName": "G<PERSON><PERSON><PERSON>čius D. - N<PERSON>rtavičius T.", "realName": "", "group": ["AWD", "LARČ5"], "car": "Ford Fiesta N5", "stageTimeMs": 237400, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "A-mega", "country": "latvia", "userName": "Blūms Emil<PERSON> - <PERSON><PERSON><PERSON><PERSON><PERSON>", "realName": "", "group": ["AWD", "LARČ3"], "car": "Mitsubishi Lancer Evo IX", "stageTimeMs": 242010, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "A-mega", "country": "lithuania", "userName": "Radišauskas N. - Vičiūnas J.", "realName": "", "group": ["AWD", "LARČ5"], "car": "Volkswagen Polo MK6 N5", "stageTimeMs": 242670, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "A-mega", "country": "lithuania", "userName": "Firant<PERSON> - Valiulis <PERSON>", "realName": "", "group": ["AWD", "LARČ3"], "car": "Mitsubishi Lancer Evo VIII", "stageTimeMs": 243170, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "A-mega", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON>čius L.", "realName": "", "group": ["AWD", "LARČ1"], "car": "Škoda Fabia N5+", "stageTimeMs": 243370, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "A-mega", "country": "lithuania", "userName": "Vėgė<PERSON><PERSON> - Saudargas G.", "realName": "", "group": ["AWD", "LARČ3"], "car": "Mitsubishi Lancer Evo IX", "stageTimeMs": 243970, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "A-mega", "country": "poland", "userName": "Ważny Paweł - <PERSON><PERSON><PERSON>", "realName": "", "group": ["AWD", "LARČ3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 245840, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "A-mega", "country": "poland", "userName": "<PERSON><PERSON><PERSON><PERSON> T. - <PERSON><PERSON><PERSON> A.", "realName": "", "group": ["AWD", "LARČ3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 246760, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "A-mega", "country": "lithuania", "userName": "Tamašauskas J. - Pranckūnas A.", "realName": "", "group": ["2WD", "LARČ6"], "car": "BMW M3 E46 Compact", "stageTimeMs": 247070, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "A-mega", "country": "lithuania", "userName": "Steponavičius R. - Ketvirtis D.", "realName": "", "group": ["AWD", "LARČ5"], "car": "Ford Fiesta N5", "stageTimeMs": 247360, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "A-mega", "country": "lithuania", "userName": "E<PERSON><PERSON><PERSON><PERSON><PERSON> - Barysas J.", "realName": "", "group": ["AWD", "LARČ5"], "car": "Ford Fiesta N5", "stageTimeMs": 248140, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "A-mega", "country": "lithuania", "userName": "Beniušis P. - <PERSON><PERSON><PERSON><PERSON><PERSON> K.", "realName": "", "group": ["AWD", "LARČ5"], "car": "Citroën C3 N5", "stageTimeMs": 248200, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "A-mega", "country": "poland", "userName": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "realName": "", "group": ["AWD", "LARČ2"], "car": "Ford Fiesta Rally2", "stageTimeMs": 248290, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "A-mega", "country": "ukraine", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["AWD", "LARČ3"], "car": "Škoda Fabia Rally2-Kit", "stageTimeMs": 250530, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "A-mega", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>.", "realName": "", "group": ["AWD", "LARČ3"], "car": "Mitsubishi Lancer Evo IX", "stageTimeMs": 250910, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "A-mega", "country": "lithuania", "userName": "Paškevičius V. - Vičiūnas P.", "realName": "", "group": ["AWD", "LARČ5"], "car": "Ford Fiesta N5", "stageTimeMs": 252020, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "A-mega", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> M. - <PERSON><PERSON><PERSON><PERSON> M.", "realName": "", "group": ["AWD", "LARČ1"], "car": "Volkswagen Polo 4WD", "stageTimeMs": 256370, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "A-mega", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON>.", "realName": "", "group": ["2WD", "LARČ6"], "car": "BMW M3 E46 Compact", "stageTimeMs": 257290, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "A-mega", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["AWD", "LARČ2"], "car": "Škoda Fabia R5", "stageTimeMs": 258150, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "A-mega", "country": "lithuania", "userName": "Simaška Justas - Kalėda Aras", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 318 Ti Compact E46", "stageTimeMs": 259560, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "A-mega", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON> E.", "realName": "", "group": ["2WD", "LARČ4"], "car": "Ford Fiesta R2T", "stageTimeMs": 260250, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "A-mega", "country": "poland", "userName": "<PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["2WD", "LARČ4"], "car": "Ford Fiesta Rally4", "stageTimeMs": 261420, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "A-mega", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["2WD", "Historic"], "car": "BMW 316i E30", "stageTimeMs": 262720, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "A-mega", "country": "lithuania", "userName": "Ačas Irmantas - Lazdauskas Benas", "realName": "", "group": ["2WD", "LARČ4"], "car": "Ford Fiesta R2T", "stageTimeMs": 262870, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "A-mega", "country": "poland", "userName": "Domżała Aron - <PERSON>owski Adrian", "realName": "", "group": ["2WD"], "car": "BMW M3 E36", "stageTimeMs": 264010, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "A-mega", "country": "lithuania", "userName": "Brok<PERSON>us <PERSON> - <PERSON><PERSON><PERSON><PERSON> R.", "realName": "", "group": ["2WD", "LARČ4"], "car": "Ford Fiesta Rally4", "stageTimeMs": 264060, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "A-mega", "country": "lithuania", "userName": "<PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["2WD", "LARČ7"], "car": "Honda Civic Type-R EP3", "stageTimeMs": 265250, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "A-mega", "country": "turkey", "userName": "Alakoç Can - Paliukėnas A.", "realName": "", "group": ["2WD", "LARČ4"], "car": "Ford Fiesta Rally4", "stageTimeMs": 265570, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "A-mega", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 316 Ti Compact E46", "stageTimeMs": 266150, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "A-mega", "country": "lithuania", "userName": "Miknius Jonas - <PERSON><PERSON><PERSON><PERSON> P.", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 328 Ti Compact E46", "stageTimeMs": 268270, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "A-mega", "country": "poland", "userName": "Polak K. - Grzywaczewski B.", "realName": "", "group": ["2WD"], "car": "BMW E46", "stageTimeMs": 268350, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "A-mega", "country": "lithuania", "userName": "Kutka Man<PERSON> - <PERSON><PERSON><PERSON><PERSON>s <PERSON>au<PERSON>", "realName": "", "group": ["2WD", "LARČ7"], "car": "Volkswagen Polo MK5", "stageTimeMs": 268570, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "A-mega", "country": "estonia", "userName": "Kõrgesaar Pranko - <PERSON><PERSON><PERSON> Ott", "realName": "", "group": ["2WD", "LARČ4"], "car": "Ford Fiesta Rally4", "stageTimeMs": 269100, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "A-mega", "country": "lithuania", "userName": "Kvedaras <PERSON> - <PERSON><PERSON><PERSON><PERSON>", "realName": "", "group": ["2WD", "LARČ4"], "car": "Peugeot 208 Rally4", "stageTimeMs": 269870, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "A-mega", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> - Valkeris R.", "realName": "", "group": ["2WD", "LARČ4"], "car": "Ford Fiesta R2T", "stageTimeMs": 271940, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "A-mega", "country": "estonia", "userName": "Tam<PERSON>ja <PERSON> - Ääremaa Henri", "realName": "", "group": ["2WD"], "car": "BMW M3 E36", "stageTimeMs": 272000, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "A-mega", "country": "poland", "userName": "Bilski Rafał - Radzik <PERSON>gni<PERSON>zka", "realName": "", "group": ["2WD", "LARČ4"], "car": "Ford Fiesta Rally4", "stageTimeMs": 272930, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "A-mega", "country": "poland", "userName": "<PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["2WD", "LARČ4"], "car": "Citroën DS3 R3T Max", "stageTimeMs": 274170, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "A-mega", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> Gegužinskas R.", "realName": "", "group": ["AWD", "LARČ3"], "car": "Subaru Impreza STi N10", "stageTimeMs": 275790, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "A-mega", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON><PERSON> - Sam<PERSON>lis M.", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 325 Ti Compact E46", "stageTimeMs": 277000, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "A-mega", "country": "lithuania", "userName": "Aukštuolis J. - Klikauskas M.", "realName": "", "group": ["2WD", "LARČ6"], "car": "BMW 325 Ti Compact E46", "stageTimeMs": 277500, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "A-mega", "country": "lithuania", "userName": "Čiutelė D. - Zvicevičius D.", "realName": "", "group": ["2WD", "Historic"], "car": "Lada VAZ 2105 VFTS", "stageTimeMs": 278970, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "A-mega", "country": "lithuania", "userName": "Račkauskas Marius - Tolstych A.", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 130i E87", "stageTimeMs": 279080, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "A-mega", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON> A.", "realName": "", "group": ["2WD", "LARČ7"], "car": "Volkswagen Polo MK5", "stageTimeMs": 280840, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "A-mega", "country": "lithuania", "userName": "Bartkuvėnas Mantas - Bagonas Ž.", "realName": "", "group": ["2WD", "LARČ4"], "car": "Ford Fiesta Rally4", "stageTimeMs": 280840, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "A-mega", "country": "lithuania", "userName": "Aleknavičius Š. - Klimašauskas T.", "realName": "", "group": ["2WD", "LARČ7"], "car": "Opel Astra GSi 16V", "stageTimeMs": 281430, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "A-mega", "country": "lithuania", "userName": "Chocka Gediminas - Čeledinas V.", "realName": "", "group": ["2WD"], "car": "BMW 325 Ti Compact E46", "stageTimeMs": 286960, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "A-mega", "country": "lithuania", "userName": "Povilionis P. - Balčiūnas V.", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 328 Ti Compact E46", "stageTimeMs": 288310, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "A-mega", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON><PERSON> - Vainiūnas J.", "realName": "", "group": ["2WD", "LARČ7"], "car": "Volkswagen Polo MK5", "stageTimeMs": 288610, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "A-mega", "country": "lithuania", "userName": "Griškus E. - Šmigelskas V.", "realName": "", "group": ["2WD", "LARČ6"], "car": "BMW 325 Ti Compact E46", "stageTimeMs": 291840, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "A-mega", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>.", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 328 Ti Compact E46", "stageTimeMs": 291860, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "A-mega", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON>", "realName": "", "group": ["2WD", "Historic"], "car": "Fiat 124", "stageTimeMs": 293660, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "A-mega", "country": "poland", "userName": "Kruszewski A. - Kruszewska K.", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 325 Ti Compact E46", "stageTimeMs": 294040, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "A-mega", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON><PERSON> Alfo<PERSON> - <PERSON><PERSON><PERSON><PERSON> M.", "realName": "", "group": ["2WD", "Historic"], "car": "Volkswagen Golf I", "stageTimeMs": 295310, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "A-mega", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 323 Ti Compact E36", "stageTimeMs": 295810, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "A-mega", "country": "lithuania", "userName": "Svirskas Patrikas - Svirskas R.", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 325 Ti Compact E46", "stageTimeMs": 296710, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "A-mega", "country": "lithuania", "userName": "Valev<PERSON> - Greč<PERSON>j", "realName": "", "group": ["2WD", "Historic"], "car": "Opel Ascona B", "stageTimeMs": 302420, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "A-mega", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "realName": "", "group": ["2WD", "Historic"], "car": "Lada VAZ 2105 VFTS", "stageTimeMs": 308940, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "A-mega", "country": "lithuania", "userName": "Stašaitis Donatas - Ližaitis M.", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 325i E90", "stageTimeMs": 832870, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "A-mega", "country": "lithuania", "userName": "Žiukelis M. - Kriaučiūnas S.", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 318 Ti Compact E46", "stageTimeMs": 1061310, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "A-mega", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON><PERSON> U.", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 328 Ti Compact E46", "stageTimeMs": 1681700, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "A-mega", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> E. - <PERSON><PERSON><PERSON>.", "realName": "", "group": ["AWD", "LARČ1"], "car": "Škoda Fabia N5+", "stageTimeMs": 2035770, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Kroon-Oil", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON><PERSON> U.", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 328 Ti Compact E46", "stageTimeMs": 0, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": false, "comment": []}, {"stageNumber": 2, "stageName": "Kroon-Oil", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["2WD", "Historic"], "car": "BMW 316i E30", "stageTimeMs": 0, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": false, "comment": []}, {"stageNumber": 2, "stageName": "Kroon-Oil", "country": "lithuania", "userName": "Ačas Irmantas - Lazdauskas Benas", "realName": "", "group": ["2WD", "LARČ4"], "car": "Ford Fiesta R2T", "stageTimeMs": 0, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": false, "comment": []}, {"stageNumber": 2, "stageName": "Kroon-Oil", "country": "lithuania", "userName": "Stašaitis Donatas - Ližaitis M.", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 325i E90", "stageTimeMs": 0, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": false, "comment": []}, {"stageNumber": 2, "stageName": "Kroon-Oil", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON>", "realName": "", "group": ["2WD", "Historic"], "car": "Fiat 124", "stageTimeMs": 0, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": false, "comment": []}, {"stageNumber": 2, "stageName": "Kroon-Oil", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 323 Ti Compact E36", "stageTimeMs": 0, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": false, "comment": []}, {"stageNumber": 2, "stageName": "Kroon-Oil", "country": "lithuania", "userName": "Žala Vaidotas - Pūķis Ivo", "realName": "", "group": ["AWD"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 724240, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Kroon-Oil", "country": "lithuania", "userName": "Notkus <PERSON>rius - Strižanas D.", "realName": "", "group": ["AWD", "LARČ2"], "car": "Škoda Fabia R5", "stageTimeMs": 731720, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Kroon-Oil", "country": "lithuania", "userName": "<PERSON><PERSON> - <PERSON><PERSON><PERSON>kas E.", "realName": "", "group": ["AWD", "LARČ1"], "car": "Škoda Fabia N5+", "stageTimeMs": 744160, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Kroon-Oil", "country": "latvia", "userName": "Blūms Emil<PERSON> - <PERSON><PERSON><PERSON><PERSON><PERSON>", "realName": "", "group": ["AWD", "LARČ3"], "car": "Mitsubishi Lancer Evo IX", "stageTimeMs": 747770, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Kroon-Oil", "country": "lithuania", "userName": "G<PERSON><PERSON><PERSON>čius D. - N<PERSON>rtavičius T.", "realName": "", "group": ["AWD", "LARČ5"], "car": "Ford Fiesta N5", "stageTimeMs": 748690, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Kroon-Oil", "country": "poland", "userName": "<PERSON><PERSON><PERSON><PERSON> T. - <PERSON><PERSON><PERSON> A.", "realName": "", "group": ["AWD", "LARČ3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 752130, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Kroon-Oil", "country": "lithuania", "userName": "Radišauskas N. - Vičiūnas J.", "realName": "", "group": ["AWD", "LARČ5"], "car": "Volkswagen Polo MK6 N5", "stageTimeMs": 758270, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Kroon-Oil", "country": "lithuania", "userName": "Steponavičius R. - Ketvirtis D.", "realName": "", "group": ["AWD", "LARČ5"], "car": "Ford Fiesta N5", "stageTimeMs": 764630, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Kroon-Oil", "country": "lithuania", "userName": "Vėgė<PERSON><PERSON> - Saudargas G.", "realName": "", "group": ["AWD", "LARČ3"], "car": "Mitsubishi Lancer Evo IX", "stageTimeMs": 770050, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Kroon-Oil", "country": "lithuania", "userName": "Tamašauskas J. - Pranckūnas A.", "realName": "", "group": ["2WD", "LARČ6"], "car": "BMW M3 E46 Compact", "stageTimeMs": 771420, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Kroon-Oil", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON>čius L.", "realName": "", "group": ["AWD", "LARČ1"], "car": "Škoda Fabia N5+", "stageTimeMs": 773230, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Kroon-Oil", "country": "lithuania", "userName": "Beniušis P. - <PERSON><PERSON><PERSON><PERSON><PERSON> K.", "realName": "", "group": ["AWD", "LARČ5"], "car": "Citroën C3 N5", "stageTimeMs": 776520, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Kroon-Oil", "country": "lithuania", "userName": "Paškevičius V. - Vičiūnas P.", "realName": "", "group": ["AWD", "LARČ5"], "car": "Ford Fiesta N5", "stageTimeMs": 784800, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Kroon-Oil", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON>.", "realName": "", "group": ["2WD", "LARČ6"], "car": "BMW M3 E46 Compact", "stageTimeMs": 790210, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Kroon-Oil", "country": "poland", "userName": "Ważny Paweł - <PERSON><PERSON><PERSON>", "realName": "", "group": ["AWD", "LARČ3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 791930, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Kroon-Oil", "country": "lithuania", "userName": "Simaška Justas - Kalėda Aras", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 318 Ti Compact E46", "stageTimeMs": 793930, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Kroon-Oil", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> M. - <PERSON><PERSON><PERSON><PERSON> M.", "realName": "", "group": ["AWD", "LARČ1"], "car": "Volkswagen Polo 4WD", "stageTimeMs": 799140, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Kroon-Oil", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 316 Ti Compact E46", "stageTimeMs": 804300, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Kroon-Oil", "country": "poland", "userName": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "realName": "", "group": ["AWD", "LARČ2"], "car": "Ford Fiesta Rally2", "stageTimeMs": 808800, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Kroon-Oil", "country": "ukraine", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["AWD", "LARČ3"], "car": "Škoda Fabia Rally2-Kit", "stageTimeMs": 815200, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Kroon-Oil", "country": "poland", "userName": "Domżała Aron - <PERSON>owski Adrian", "realName": "", "group": ["2WD"], "car": "BMW M3 E36", "stageTimeMs": 815310, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Kroon-Oil", "country": "estonia", "userName": "Kõrgesaar Pranko - <PERSON><PERSON><PERSON> Ott", "realName": "", "group": ["2WD", "LARČ4"], "car": "Ford Fiesta Rally4", "stageTimeMs": 816450, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Kroon-Oil", "country": "turkey", "userName": "Alakoç Can - Paliukėnas A.", "realName": "", "group": ["2WD", "LARČ4"], "car": "Ford Fiesta Rally4", "stageTimeMs": 819070, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Kroon-Oil", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON> E.", "realName": "", "group": ["2WD", "LARČ4"], "car": "Ford Fiesta R2T", "stageTimeMs": 819610, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Kroon-Oil", "country": "poland", "userName": "<PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["2WD", "LARČ4"], "car": "Ford Fiesta Rally4", "stageTimeMs": 820470, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Kroon-Oil", "country": "lithuania", "userName": "Firant<PERSON> - Valiulis <PERSON>", "realName": "", "group": ["AWD", "LARČ3"], "car": "Mitsubishi Lancer Evo VIII", "stageTimeMs": 822420, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Kroon-Oil", "country": "lithuania", "userName": "E<PERSON><PERSON><PERSON><PERSON><PERSON> - Barysas J.", "realName": "", "group": ["AWD", "LARČ5"], "car": "Ford Fiesta N5", "stageTimeMs": 822730, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Kroon-Oil", "country": "lithuania", "userName": "Brok<PERSON>us <PERSON> - <PERSON><PERSON><PERSON><PERSON> R.", "realName": "", "group": ["2WD", "LARČ4"], "car": "Ford Fiesta Rally4", "stageTimeMs": 824430, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Kroon-Oil", "country": "estonia", "userName": "Tam<PERSON>ja <PERSON> - Ääremaa Henri", "realName": "", "group": ["2WD"], "car": "BMW M3 E36", "stageTimeMs": 824970, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 170000, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Kroon-Oil", "country": "poland", "userName": "Polak K. - Grzywaczewski B.", "realName": "", "group": ["2WD"], "car": "BMW E46", "stageTimeMs": 831970, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 10000, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Kroon-Oil", "country": "lithuania", "userName": "<PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["2WD", "LARČ7"], "car": "Honda Civic Type-R EP3", "stageTimeMs": 834220, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Kroon-Oil", "country": "lithuania", "userName": "Kutka Man<PERSON> - <PERSON><PERSON><PERSON><PERSON>s <PERSON>au<PERSON>", "realName": "", "group": ["2WD", "LARČ7"], "car": "Volkswagen Polo MK5", "stageTimeMs": 837580, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Kroon-Oil", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON><PERSON> - Sam<PERSON>lis M.", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 325 Ti Compact E46", "stageTimeMs": 842960, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Kroon-Oil", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>.", "realName": "", "group": ["AWD", "LARČ3"], "car": "Mitsubishi Lancer Evo IX", "stageTimeMs": 845170, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Kroon-Oil", "country": "lithuania", "userName": "Čiutelė D. - Zvicevičius D.", "realName": "", "group": ["2WD", "Historic"], "car": "Lada VAZ 2105 VFTS", "stageTimeMs": 848320, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Kroon-Oil", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> Gegužinskas R.", "realName": "", "group": ["AWD", "LARČ3"], "car": "Subaru Impreza STi N10", "stageTimeMs": 850700, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Kroon-Oil", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> - Valkeris R.", "realName": "", "group": ["2WD", "LARČ4"], "car": "Ford Fiesta R2T", "stageTimeMs": 851850, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 10000, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Kroon-Oil", "country": "lithuania", "userName": "Miknius Jonas - <PERSON><PERSON><PERSON><PERSON> P.", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 328 Ti Compact E46", "stageTimeMs": 854330, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Kroon-Oil", "country": "lithuania", "userName": "Kvedaras <PERSON> - <PERSON><PERSON><PERSON><PERSON>", "realName": "", "group": ["2WD", "LARČ4"], "car": "Peugeot 208 Rally4", "stageTimeMs": 854790, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Kroon-Oil", "country": "poland", "userName": "Bilski Rafał - Radzik <PERSON>gni<PERSON>zka", "realName": "", "group": ["2WD", "LARČ4"], "car": "Ford Fiesta Rally4", "stageTimeMs": 859080, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Kroon-Oil", "country": "lithuania", "userName": "Aukštuolis J. - Klikauskas M.", "realName": "", "group": ["2WD", "LARČ6"], "car": "BMW 325 Ti Compact E46", "stageTimeMs": 862070, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Kroon-Oil", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["AWD", "LARČ2"], "car": "Škoda Fabia R5", "stageTimeMs": 866880, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 30000, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Kroon-Oil", "country": "poland", "userName": "<PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["2WD", "LARČ4"], "car": "Citroën DS3 R3T Max", "stageTimeMs": 871660, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Kroon-Oil", "country": "lithuania", "userName": "Bartkuvėnas Mantas - Bagonas Ž.", "realName": "", "group": ["2WD", "LARČ4"], "car": "Ford Fiesta Rally4", "stageTimeMs": 874720, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Kroon-Oil", "country": "lithuania", "userName": "Aleknavičius Š. - Klimašauskas T.", "realName": "", "group": ["2WD", "LARČ7"], "car": "Opel Astra GSi 16V", "stageTimeMs": 878260, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Kroon-Oil", "country": "lithuania", "userName": "Povilionis P. - Balčiūnas V.", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 328 Ti Compact E46", "stageTimeMs": 879750, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Kroon-Oil", "country": "lithuania", "userName": "Račkauskas Marius - Tolstych A.", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 130i E87", "stageTimeMs": 883340, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Kroon-Oil", "country": "lithuania", "userName": "Griškus E. - Šmigelskas V.", "realName": "", "group": ["2WD", "LARČ6"], "car": "BMW 325 Ti Compact E46", "stageTimeMs": 885000, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Kroon-Oil", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON><PERSON> - Vainiūnas J.", "realName": "", "group": ["2WD", "LARČ7"], "car": "Volkswagen Polo MK5", "stageTimeMs": 889440, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Kroon-Oil", "country": "lithuania", "userName": "Chocka Gediminas - Čeledinas V.", "realName": "", "group": ["2WD"], "car": "BMW 325 Ti Compact E46", "stageTimeMs": 894710, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Kroon-Oil", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>.", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 328 Ti Compact E46", "stageTimeMs": 922920, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Kroon-Oil", "country": "poland", "userName": "Kruszewski A. - Kruszewska K.", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 325 Ti Compact E46", "stageTimeMs": 926580, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Kroon-Oil", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> E. - <PERSON><PERSON><PERSON>.", "realName": "", "group": ["AWD", "LARČ1"], "car": "Škoda Fabia N5+", "stageTimeMs": 926760, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 130000, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Kroon-Oil", "country": "lithuania", "userName": "Valev<PERSON> - Greč<PERSON>j", "realName": "", "group": ["2WD", "Historic"], "car": "Opel Ascona B", "stageTimeMs": 940350, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Kroon-Oil", "country": "lithuania", "userName": "Svirskas Patrikas - Svirskas R.", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 325 Ti Compact E46", "stageTimeMs": 951750, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Kroon-Oil", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "realName": "", "group": ["2WD", "Historic"], "car": "Lada VAZ 2105 VFTS", "stageTimeMs": 962380, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Kroon-Oil", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON> A.", "realName": "", "group": ["2WD", "LARČ7"], "car": "Volkswagen Polo MK5", "stageTimeMs": 986160, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Kroon-Oil", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON><PERSON> Alfo<PERSON> - <PERSON><PERSON><PERSON><PERSON> M.", "realName": "", "group": ["2WD", "Historic"], "car": "Volkswagen Golf I", "stageTimeMs": 1021730, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Kroon-Oil", "country": "lithuania", "userName": "Žiukelis M. - Kriaučiūnas S.", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 318 Ti Compact E46", "stageTimeMs": 1184430, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 40000, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Fakto Auto", "country": "lithuania", "userName": "Aukštuolis J. - Klikauskas M.", "realName": "", "group": ["2WD", "LARČ6"], "car": "BMW 325 Ti Compact E46", "stageTimeMs": 0, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": false, "comment": []}, {"stageNumber": 3, "stageName": "Fakto Auto", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON> E.", "realName": "", "group": ["2WD", "LARČ4", "Junior"], "car": "Ford Fiesta R2T", "stageTimeMs": 0, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": false, "comment": []}, {"stageNumber": 3, "stageName": "Fakto Auto", "country": "poland", "userName": "<PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["2WD", "LARČ4"], "car": "Citroën DS3 R3T Max", "stageTimeMs": 0, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": false, "comment": []}, {"stageNumber": 3, "stageName": "Fakto Auto", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON><PERSON> Alfo<PERSON> - <PERSON><PERSON><PERSON><PERSON> M.", "realName": "", "group": ["2WD", "Historic"], "car": "Volkswagen Golf I", "stageTimeMs": 0, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": false, "comment": []}, {"stageNumber": 3, "stageName": "Fakto Auto", "country": "lithuania", "userName": "Žala Vaidotas - Pūķis Ivo", "realName": "", "group": ["AWD"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 225410, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Fakto Auto", "country": "lithuania", "userName": "Notkus <PERSON>rius - Strižanas D.", "realName": "", "group": ["AWD", "LARČ2"], "car": "Škoda Fabia R5", "stageTimeMs": 227150, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Fakto Auto", "country": "lithuania", "userName": "<PERSON><PERSON> - <PERSON><PERSON><PERSON>kas E.", "realName": "", "group": ["AWD", "LARČ1"], "car": "Škoda Fabia N5+", "stageTimeMs": 234160, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Fakto Auto", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON>čius L.", "realName": "", "group": ["AWD", "LARČ1"], "car": "Škoda Fabia N5+", "stageTimeMs": 236290, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Fakto Auto", "country": "lithuania", "userName": "Radišauskas N. - Vičiūnas J.", "realName": "", "group": ["AWD", "LARČ5"], "car": "Volkswagen Polo MK6 N5", "stageTimeMs": 237690, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Fakto Auto", "country": "latvia", "userName": "Blūms Emil<PERSON> - <PERSON><PERSON><PERSON><PERSON><PERSON>", "realName": "", "group": ["AWD", "LARČ3"], "car": "Mitsubishi Lancer Evo IX", "stageTimeMs": 237850, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Fakto Auto", "country": "lithuania", "userName": "G<PERSON><PERSON><PERSON>čius D. - N<PERSON>rtavičius T.", "realName": "", "group": ["AWD", "LARČ5"], "car": "Ford Fiesta N5", "stageTimeMs": 238710, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Fakto Auto", "country": "lithuania", "userName": "Steponavičius R. - Ketvirtis D.", "realName": "", "group": ["AWD", "LARČ5"], "car": "Ford Fiesta N5", "stageTimeMs": 240010, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Fakto Auto", "country": "lithuania", "userName": "Beniušis P. - <PERSON><PERSON><PERSON><PERSON><PERSON> K.", "realName": "", "group": ["AWD", "LARČ5"], "car": "Citroën C3 N5", "stageTimeMs": 241960, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Fakto Auto", "country": "poland", "userName": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "realName": "", "group": ["AWD", "LARČ2"], "car": "Ford Fiesta Rally2", "stageTimeMs": 243360, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Fakto Auto", "country": "lithuania", "userName": "Paškevičius V. - Vičiūnas P.", "realName": "", "group": ["AWD", "LARČ5"], "car": "Ford Fiesta N5", "stageTimeMs": 246870, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Fakto Auto", "country": "poland", "userName": "<PERSON><PERSON><PERSON><PERSON> T. - <PERSON><PERSON><PERSON> A.", "realName": "", "group": ["AWD", "LARČ3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 247620, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Fakto Auto", "country": "poland", "userName": "Ważny Paweł - <PERSON><PERSON><PERSON>", "realName": "", "group": ["AWD", "LARČ3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 248620, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Fakto Auto", "country": "ukraine", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["AWD", "LARČ3"], "car": "Škoda Fabia Rally2-Kit", "stageTimeMs": 249000, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Fakto Auto", "country": "lithuania", "userName": "Vėgė<PERSON><PERSON> - Saudargas G.", "realName": "", "group": ["AWD", "LARČ3"], "car": "Mitsubishi Lancer Evo IX", "stageTimeMs": 249860, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Fakto Auto", "country": "lithuania", "userName": "Tamašauskas J. - Pranckūnas A.", "realName": "", "group": ["2WD", "LARČ6"], "car": "BMW M3 E46 Compact", "stageTimeMs": 249980, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Fakto Auto", "country": "lithuania", "userName": "E<PERSON><PERSON><PERSON><PERSON><PERSON> - Barysas J.", "realName": "", "group": ["AWD", "LARČ5"], "car": "Ford Fiesta N5", "stageTimeMs": 252940, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Fakto Auto", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>.", "realName": "", "group": ["AWD", "LARČ3"], "car": "Mitsubishi Lancer Evo IX", "stageTimeMs": 253270, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Fakto Auto", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> M. - <PERSON><PERSON><PERSON><PERSON> M.", "realName": "", "group": ["AWD", "LARČ1"], "car": "Volkswagen Polo 4WD", "stageTimeMs": 255540, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Fakto Auto", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON>.", "realName": "", "group": ["2WD", "LARČ6"], "car": "BMW M3 E46 Compact", "stageTimeMs": 257460, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Fakto Auto", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["AWD", "LARČ2"], "car": "Škoda Fabia R5", "stageTimeMs": 257670, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Fakto Auto", "country": "poland", "userName": "Domżała Aron - <PERSON>owski Adrian", "realName": "", "group": ["2WD"], "car": "BMW M3 E36", "stageTimeMs": 258250, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Fakto Auto", "country": "lithuania", "userName": "Firant<PERSON> - Valiulis <PERSON>", "realName": "", "group": ["AWD", "LARČ3"], "car": "Mitsubishi Lancer Evo VIII", "stageTimeMs": 259480, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Fakto Auto", "country": "lithuania", "userName": "Simaška Justas - Kalėda Aras", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 318 Ti Compact E46", "stageTimeMs": 259560, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Fakto Auto", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> E. - <PERSON><PERSON><PERSON>.", "realName": "", "group": ["AWD", "LARČ1"], "car": "Škoda Fabia N5+", "stageTimeMs": 260250, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 160000, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Fakto Auto", "country": "poland", "userName": "<PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["2WD", "LARČ4"], "car": "Ford Fiesta Rally4", "stageTimeMs": 260330, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Fakto Auto", "country": "lithuania", "userName": "Brok<PERSON>us <PERSON> - <PERSON><PERSON><PERSON><PERSON> R.", "realName": "", "group": ["2WD", "LARČ4"], "car": "Ford Fiesta Rally4", "stageTimeMs": 262040, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Fakto Auto", "country": "estonia", "userName": "Kõrgesaar Pranko - <PERSON><PERSON><PERSON> Ott", "realName": "", "group": ["2WD", "LARČ4"], "car": "Ford Fiesta Rally4", "stageTimeMs": 262110, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Fakto Auto", "country": "lithuania", "userName": "Kutka Man<PERSON> - <PERSON><PERSON><PERSON><PERSON>s <PERSON>au<PERSON>", "realName": "", "group": ["2WD", "LARČ7"], "car": "Volkswagen Polo MK5", "stageTimeMs": 263180, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Fakto Auto", "country": "lithuania", "userName": "Kvedaras <PERSON> - <PERSON><PERSON><PERSON><PERSON>", "realName": "", "group": ["2WD", "LARČ4"], "car": "Peugeot 208 Rally4", "stageTimeMs": 265160, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Fakto Auto", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> - Valkeris R.", "realName": "", "group": ["2WD", "LARČ4"], "car": "Ford Fiesta R2T", "stageTimeMs": 268770, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Fakto Auto", "country": "poland", "userName": "Polak K. - Grzywaczewski B.", "realName": "", "group": ["2WD"], "car": "BMW E46", "stageTimeMs": 270230, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Fakto Auto", "country": "lithuania", "userName": "Bartkuvėnas Mantas - Bagonas Ž.", "realName": "", "group": ["2WD", "LARČ4"], "car": "Ford Fiesta Rally4", "stageTimeMs": 270300, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Fakto Auto", "country": "lithuania", "userName": "<PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["2WD", "LARČ7"], "car": "Honda Civic Type-R EP3", "stageTimeMs": 271360, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Fakto Auto", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 316 Ti Compact E46", "stageTimeMs": 272180, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Fakto Auto", "country": "lithuania", "userName": "Žiukelis M. - Kriaučiūnas S.", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 318 Ti Compact E46", "stageTimeMs": 273990, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Fakto Auto", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON><PERSON> - Sam<PERSON>lis M.", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 325 Ti Compact E46", "stageTimeMs": 274580, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Fakto Auto", "country": "poland", "userName": "Bilski Rafał - Radzik <PERSON>gni<PERSON>zka", "realName": "", "group": ["2WD", "LARČ4"], "car": "Ford Fiesta Rally4", "stageTimeMs": 274590, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Fakto Auto", "country": "lithuania", "userName": "Miknius Jonas - <PERSON><PERSON><PERSON><PERSON> P.", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 328 Ti Compact E46", "stageTimeMs": 276660, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Fakto Auto", "country": "lithuania", "userName": "Čiutelė D. - Zvicevičius D.", "realName": "", "group": ["2WD", "Historic"], "car": "Lada VAZ 2105 VFTS", "stageTimeMs": 276990, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Fakto Auto", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> Gegužinskas R.", "realName": "", "group": ["AWD", "LARČ3"], "car": "Subaru Impreza STi N10", "stageTimeMs": 277730, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Fakto Auto", "country": "turkey", "userName": "Alakoç Can - Paliukėnas A.", "realName": "", "group": ["2WD", "LARČ4"], "car": "Ford Fiesta Rally4", "stageTimeMs": 278270, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Fakto Auto", "country": "lithuania", "userName": "Chocka Gediminas - Čeledinas V.", "realName": "", "group": ["2WD"], "car": "BMW 325 Ti Compact E46", "stageTimeMs": 279340, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Fakto Auto", "country": "estonia", "userName": "Tam<PERSON>ja <PERSON> - Ääremaa Henri", "realName": "", "group": ["2WD"], "car": "BMW M3 E36", "stageTimeMs": 280700, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Fakto Auto", "country": "lithuania", "userName": "Račkauskas Marius - Tolstych A.", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 130i E87", "stageTimeMs": 280830, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Fakto Auto", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON><PERSON> - Vainiūnas J.", "realName": "", "group": ["2WD", "LARČ7"], "car": "Volkswagen Polo MK5", "stageTimeMs": 283120, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Fakto Auto", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON> A.", "realName": "", "group": ["2WD", "LARČ7"], "car": "Volkswagen Polo MK5", "stageTimeMs": 285700, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Fakto Auto", "country": "lithuania", "userName": "Povilionis P. - Balčiūnas V.", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 328 Ti Compact E46", "stageTimeMs": 286120, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Fakto Auto", "country": "lithuania", "userName": "Svirskas Patrikas - Svirskas R.", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 325 Ti Compact E46", "stageTimeMs": 292770, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Fakto Auto", "country": "poland", "userName": "Kruszewski A. - Kruszewska K.", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 325 Ti Compact E46", "stageTimeMs": 295490, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Fakto Auto", "country": "lithuania", "userName": "Aleknavičius Š. - Klimašauskas T.", "realName": "", "group": ["2WD", "LARČ7"], "car": "Opel Astra GSi 16V", "stageTimeMs": 295810, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Fakto Auto", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>.", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 328 Ti Compact E46", "stageTimeMs": 304150, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Fakto Auto", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "realName": "", "group": ["2WD", "Historic"], "car": "Lada VAZ 2105 VFTS", "stageTimeMs": 318610, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Fakto Auto", "country": "lithuania", "userName": "Valev<PERSON> - Greč<PERSON>j", "realName": "", "group": ["2WD", "Historic"], "car": "Opel Ascona B", "stageTimeMs": 322060, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Fakto Auto", "country": "lithuania", "userName": "Griškus E. - Šmigelskas V.", "realName": "", "group": ["2WD", "LARČ6"], "car": "BMW 325 Ti Compact E46", "stageTimeMs": 419890, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Bags & More", "country": "lithuania", "userName": "Radišauskas N. - Vičiūnas J.", "realName": "", "group": ["AWD", "LARČ5"], "car": "Volkswagen Polo MK6 N5", "stageTimeMs": 0, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": false, "comment": []}, {"stageNumber": 4, "stageName": "Bags & More", "country": "lithuania", "userName": "Firant<PERSON> - Valiulis <PERSON>", "realName": "", "group": ["AWD", "LARČ3"], "car": "Mitsubishi Lancer Evo VIII", "stageTimeMs": 0, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": false, "comment": []}, {"stageNumber": 4, "stageName": "Bags & More", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON>čius L.", "realName": "", "group": ["AWD", "LARČ1"], "car": "Škoda Fabia N5+", "stageTimeMs": 0, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": false, "comment": []}, {"stageNumber": 4, "stageName": "Bags & More", "country": "lithuania", "userName": "Tamašauskas J. - Pranckūnas A.", "realName": "", "group": ["2WD", "LARČ6"], "car": "BMW M3 E46 Compact", "stageTimeMs": 0, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": false, "comment": []}, {"stageNumber": 4, "stageName": "Bags & More", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> M. - <PERSON><PERSON><PERSON><PERSON> M.", "realName": "", "group": ["AWD", "LARČ1"], "car": "Volkswagen Polo 4WD", "stageTimeMs": 0, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": false, "comment": []}, {"stageNumber": 4, "stageName": "Bags & More", "country": "lithuania", "userName": "Aleknavičius Š. - Klimašauskas T.", "realName": "", "group": ["2WD", "LARČ7"], "car": "Opel Astra GSi 16V", "stageTimeMs": 0, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": false, "comment": []}, {"stageNumber": 4, "stageName": "Bags & More", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "realName": "", "group": ["2WD", "Historic"], "car": "Lada VAZ 2105 VFTS", "stageTimeMs": 0, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": false, "comment": []}, {"stageNumber": 4, "stageName": "Bags & More", "country": "lithuania", "userName": "Žala Vaidotas - Pūķis Ivo", "realName": "", "group": ["AWD"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 707400, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Bags & More", "country": "lithuania", "userName": "<PERSON><PERSON> - <PERSON><PERSON><PERSON>kas E.", "realName": "", "group": ["AWD", "LARČ1"], "car": "Škoda Fabia N5+", "stageTimeMs": 732100, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Bags & More", "country": "lithuania", "userName": "Steponavičius R. - Ketvirtis D.", "realName": "", "group": ["AWD", "LARČ5"], "car": "Ford Fiesta N5", "stageTimeMs": 741810, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Bags & More", "country": "lithuania", "userName": "G<PERSON><PERSON><PERSON>čius D. - N<PERSON>rtavičius T.", "realName": "", "group": ["AWD", "LARČ5"], "car": "Ford Fiesta N5", "stageTimeMs": 749020, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Bags & More", "country": "lithuania", "userName": "Beniušis P. - <PERSON><PERSON><PERSON><PERSON><PERSON> K.", "realName": "", "group": ["AWD", "LARČ5"], "car": "Citroën C3 N5", "stageTimeMs": 759550, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Bags & More", "country": "poland", "userName": "<PERSON><PERSON><PERSON><PERSON> T. - <PERSON><PERSON><PERSON> A.", "realName": "", "group": ["AWD", "LARČ3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 760080, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Bags & More", "country": "poland", "userName": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "realName": "", "group": ["AWD", "LARČ2"], "car": "Ford Fiesta Rally2", "stageTimeMs": 762700, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Bags & More", "country": "lithuania", "userName": "Vėgė<PERSON><PERSON> - Saudargas G.", "realName": "", "group": ["AWD", "LARČ3"], "car": "Mitsubishi Lancer Evo IX", "stageTimeMs": 766830, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Bags & More", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON>.", "realName": "", "group": ["2WD", "LARČ6"], "car": "BMW M3 E46 Compact", "stageTimeMs": 775520, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Bags & More", "country": "latvia", "userName": "Blūms Emil<PERSON> - <PERSON><PERSON><PERSON><PERSON><PERSON>", "realName": "", "group": ["AWD", "LARČ3"], "car": "Mitsubishi Lancer Evo IX", "stageTimeMs": 780220, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Bags & More", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>.", "realName": "", "group": ["AWD", "LARČ3"], "car": "Mitsubishi Lancer Evo IX", "stageTimeMs": 783850, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Bags & More", "country": "lithuania", "userName": "Paškevičius V. - Vičiūnas P.", "realName": "", "group": ["AWD", "LARČ5"], "car": "Ford Fiesta N5", "stageTimeMs": 784550, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Bags & More", "country": "lithuania", "userName": "Simaška Justas - Kalėda Aras", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 318 Ti Compact E46", "stageTimeMs": 792970, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Bags & More", "country": "estonia", "userName": "Kõrgesaar Pranko - <PERSON><PERSON><PERSON> Ott", "realName": "", "group": ["2WD", "LARČ4"], "car": "Ford Fiesta Rally4", "stageTimeMs": 798300, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Bags & More", "country": "ukraine", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["AWD", "LARČ3"], "car": "Škoda Fabia Rally2-Kit", "stageTimeMs": 798470, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Bags & More", "country": "poland", "userName": "<PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["2WD", "LARČ4"], "car": "Ford Fiesta Rally4", "stageTimeMs": 801050, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Bags & More", "country": "turkey", "userName": "Alakoç Can - Paliukėnas A.", "realName": "", "group": ["2WD", "LARČ4"], "car": "Ford Fiesta Rally4", "stageTimeMs": 805380, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Bags & More", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 316 Ti Compact E46", "stageTimeMs": 806440, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Bags & More", "country": "lithuania", "userName": "Brok<PERSON>us <PERSON> - <PERSON><PERSON><PERSON><PERSON> R.", "realName": "", "group": ["2WD", "LARČ4"], "car": "Ford Fiesta Rally4", "stageTimeMs": 807320, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Bags & More", "country": "lithuania", "userName": "Kutka Man<PERSON> - <PERSON><PERSON><PERSON><PERSON>s <PERSON>au<PERSON>", "realName": "", "group": ["2WD", "LARČ7"], "car": "Volkswagen Polo MK5", "stageTimeMs": 814650, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Bags & More", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["AWD", "LARČ2"], "car": "Škoda Fabia R5", "stageTimeMs": 823940, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Bags & More", "country": "estonia", "userName": "Tam<PERSON>ja <PERSON> - Ääremaa Henri", "realName": "", "group": ["2WD"], "car": "BMW M3 E36", "stageTimeMs": 827810, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Bags & More", "country": "lithuania", "userName": "E<PERSON><PERSON><PERSON><PERSON><PERSON> - Barysas J.", "realName": "", "group": ["AWD", "LARČ5"], "car": "Ford Fiesta N5", "stageTimeMs": 830800, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Bags & More", "country": "lithuania", "userName": "Kvedaras <PERSON> - <PERSON><PERSON><PERSON><PERSON>", "realName": "", "group": ["2WD", "LARČ4"], "car": "Peugeot 208 Rally4", "stageTimeMs": 833010, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Bags & More", "country": "lithuania", "userName": "Žiukelis M. - Kriaučiūnas S.", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 318 Ti Compact E46", "stageTimeMs": 839000, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Bags & More", "country": "lithuania", "userName": "Bartkuvėnas Mantas - Bagonas Ž.", "realName": "", "group": ["2WD", "LARČ4"], "car": "Ford Fiesta Rally4", "stageTimeMs": 839160, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 10000, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Bags & More", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON><PERSON> - Sam<PERSON>lis M.", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 325 Ti Compact E46", "stageTimeMs": 840460, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Bags & More", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> - Valkeris R.", "realName": "", "group": ["2WD", "LARČ4"], "car": "Ford Fiesta R2T", "stageTimeMs": 840680, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Bags & More", "country": "lithuania", "userName": "Miknius Jonas - <PERSON><PERSON><PERSON><PERSON> P.", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 328 Ti Compact E46", "stageTimeMs": 842110, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Bags & More", "country": "lithuania", "userName": "Povilionis P. - Balčiūnas V.", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 328 Ti Compact E46", "stageTimeMs": 857350, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Bags & More", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> Gegužinskas R.", "realName": "", "group": ["AWD", "LARČ3"], "car": "Subaru Impreza STi N10", "stageTimeMs": 859550, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Bags & More", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON> A.", "realName": "", "group": ["2WD", "LARČ7"], "car": "Volkswagen Polo MK5", "stageTimeMs": 867230, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Bags & More", "country": "lithuania", "userName": "Chocka Gediminas - Čeledinas V.", "realName": "", "group": ["2WD"], "car": "BMW 325 Ti Compact E46", "stageTimeMs": 873690, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Bags & More", "country": "lithuania", "userName": "Račkauskas Marius - Tolstych A.", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 130i E87", "stageTimeMs": 878850, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Bags & More", "country": "lithuania", "userName": "Čiutelė D. - Zvicevičius D.", "realName": "", "group": ["2WD", "Historic"], "car": "Lada VAZ 2105 VFTS", "stageTimeMs": 893350, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Bags & More", "country": "lithuania", "userName": "Svirskas Patrikas - Svirskas R.", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 325 Ti Compact E46", "stageTimeMs": 898630, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Bags & More", "country": "lithuania", "userName": "Griškus E. - Šmigelskas V.", "realName": "", "group": ["2WD", "LARČ6"], "car": "BMW 325 Ti Compact E46", "stageTimeMs": 906790, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Bags & More", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> E. - <PERSON><PERSON><PERSON>.", "realName": "", "group": ["AWD", "LARČ1"], "car": "Škoda Fabia N5+", "stageTimeMs": 906950, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Bags & More", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>.", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 328 Ti Compact E46", "stageTimeMs": 916200, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Bags & More", "country": "poland", "userName": "Kruszewski A. - Kruszewska K.", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 325 Ti Compact E46", "stageTimeMs": 918940, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Bags & More", "country": "poland", "userName": "Bilski Rafał - Radzik <PERSON>gni<PERSON>zka", "realName": "", "group": ["2WD", "LARČ4"], "car": "Ford Fiesta Rally4", "stageTimeMs": 943340, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Bags & More", "country": "lithuania", "userName": "Valev<PERSON> - Greč<PERSON>j", "realName": "", "group": ["2WD", "Historic"], "car": "Opel Ascona B", "stageTimeMs": 949510, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Bags & More", "country": "lithuania", "userName": "Notkus <PERSON>rius - Strižanas D.", "realName": "", "group": ["AWD", "LARČ2"], "car": "Škoda Fabia R5", "stageTimeMs": 950730, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Bags & More", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON><PERSON> - Vainiūnas J.", "realName": "", "group": ["2WD", "LARČ7"], "car": "Volkswagen Polo MK5", "stageTimeMs": 960450, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Bags & More", "country": "poland", "userName": "Ważny Paweł - <PERSON><PERSON><PERSON>", "realName": "", "group": ["AWD", "LARČ3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 1094840, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Bags & More", "country": "poland", "userName": "Domżała Aron - <PERSON>owski Adrian", "realName": "", "group": ["2WD"], "car": "BMW M3 E36", "stageTimeMs": 1181690, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 10000, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Bags & More", "country": "lithuania", "userName": "<PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["2WD", "LARČ7"], "car": "Honda Civic Type-R EP3", "stageTimeMs": 1248450, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Bags & More", "country": "poland", "userName": "Polak K. - Grzywaczewski B.", "realName": "", "group": ["2WD"], "car": "BMW E46", "stageTimeMs": 1396860, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Viada", "country": "lithuania", "userName": "Notkus <PERSON>rius - Strižanas D.", "realName": "", "group": ["AWD", "LARČ2"], "car": "Škoda Fabia R5", "stageTimeMs": 0, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": false, "comment": []}, {"stageNumber": 5, "stageName": "Viada", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> E. - <PERSON><PERSON><PERSON>.", "realName": "", "group": ["AWD", "LARČ1"], "car": "Škoda Fabia N5+", "stageTimeMs": 0, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": false, "comment": []}, {"stageNumber": 5, "stageName": "Viada", "country": "lithuania", "userName": "Beniušis P. - <PERSON><PERSON><PERSON><PERSON><PERSON> K.", "realName": "", "group": ["AWD", "LARČ5"], "car": "Citroën C3 N5", "stageTimeMs": 0, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": false, "comment": []}, {"stageNumber": 5, "stageName": "Viada", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> - Valkeris R.", "realName": "", "group": ["2WD", "LARČ4", "Junior"], "car": "Ford Fiesta R2T", "stageTimeMs": 0, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": false, "comment": []}, {"stageNumber": 5, "stageName": "Viada", "country": "lithuania", "userName": "<PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["2WD", "LARČ7"], "car": "Honda Civic Type-R EP3", "stageTimeMs": 0, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": false, "comment": []}, {"stageNumber": 5, "stageName": "Viada", "country": "lithuania", "userName": "Vėgė<PERSON><PERSON> - Saudargas G.", "realName": "", "group": ["AWD", "LARČ3"], "car": "Mitsubishi Lancer Evo IX", "stageTimeMs": 0, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": false, "comment": []}, {"stageNumber": 5, "stageName": "Viada", "country": "lithuania", "userName": "Žala Vaidotas - Pūķis Ivo", "realName": "", "group": ["AWD"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 484830, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Viada", "country": "lithuania", "userName": "<PERSON><PERSON> - <PERSON><PERSON><PERSON>kas E.", "realName": "", "group": ["AWD", "LARČ1"], "car": "Škoda Fabia N5+", "stageTimeMs": 512030, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Viada", "country": "lithuania", "userName": "Steponavičius R. - Ketvirtis D.", "realName": "", "group": ["AWD", "LARČ5"], "car": "Ford Fiesta N5", "stageTimeMs": 520330, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Viada", "country": "poland", "userName": "<PERSON><PERSON><PERSON><PERSON> T. - <PERSON><PERSON><PERSON> A.", "realName": "", "group": ["AWD", "LARČ3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 523350, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Viada", "country": "lithuania", "userName": "G<PERSON><PERSON><PERSON>čius D. - N<PERSON>rtavičius T.", "realName": "", "group": ["AWD", "LARČ5"], "car": "Ford Fiesta N5", "stageTimeMs": 527950, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Viada", "country": "poland", "userName": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "realName": "", "group": ["AWD", "LARČ2"], "car": "Ford Fiesta Rally2", "stageTimeMs": 529340, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Viada", "country": "lithuania", "userName": "Paškevičius V. - Vičiūnas P.", "realName": "", "group": ["AWD", "LARČ5"], "car": "Ford Fiesta N5", "stageTimeMs": 533540, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Viada", "country": "lithuania", "userName": "Simaška Justas - Kalėda Aras", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 318 Ti Compact E46", "stageTimeMs": 541820, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Viada", "country": "ukraine", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["AWD", "LARČ3"], "car": "Škoda Fabia Rally2-Kit", "stageTimeMs": 543060, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Viada", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>.", "realName": "", "group": ["AWD", "LARČ3"], "car": "Mitsubishi Lancer Evo IX", "stageTimeMs": 543100, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Viada", "country": "lithuania", "userName": "E<PERSON><PERSON><PERSON><PERSON><PERSON> - Barysas J.", "realName": "", "group": ["AWD", "LARČ5"], "car": "Ford Fiesta N5", "stageTimeMs": 545660, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Viada", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON>.", "realName": "", "group": ["2WD", "LARČ6"], "car": "BMW M3 E46 Compact", "stageTimeMs": 545860, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 30000, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Viada", "country": "lithuania", "userName": "Brok<PERSON>us <PERSON> - <PERSON><PERSON><PERSON><PERSON> R.", "realName": "", "group": ["2WD", "LARČ4"], "car": "Ford Fiesta Rally4", "stageTimeMs": 553780, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Viada", "country": "poland", "userName": "<PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["2WD", "LARČ4"], "car": "Ford Fiesta Rally4", "stageTimeMs": 553910, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Viada", "country": "latvia", "userName": "Blūms Emil<PERSON> - <PERSON><PERSON><PERSON><PERSON><PERSON>", "realName": "", "group": ["AWD", "LARČ3"], "car": "Mitsubishi Lancer Evo IX", "stageTimeMs": 554860, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Viada", "country": "poland", "userName": "Domżała Aron - <PERSON>owski Adrian", "realName": "", "group": ["2WD"], "car": "BMW M3 E36", "stageTimeMs": 555770, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Viada", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 316 Ti Compact E46", "stageTimeMs": 558420, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Viada", "country": "turkey", "userName": "Alakoç Can - Paliukėnas A.", "realName": "", "group": ["2WD", "LARČ4"], "car": "Ford Fiesta Rally4", "stageTimeMs": 559330, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Viada", "country": "estonia", "userName": "Kõrgesaar Pranko - <PERSON><PERSON><PERSON> Ott", "realName": "", "group": ["2WD", "LARČ4"], "car": "Ford Fiesta Rally4", "stageTimeMs": 560550, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Viada", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["AWD", "LARČ2"], "car": "Škoda Fabia R5", "stageTimeMs": 562600, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Viada", "country": "lithuania", "userName": "Miknius Jonas - <PERSON><PERSON><PERSON><PERSON> P.", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 328 Ti Compact E46", "stageTimeMs": 569810, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Viada", "country": "lithuania", "userName": "Kutka Man<PERSON> - <PERSON><PERSON><PERSON><PERSON>s <PERSON>au<PERSON>", "realName": "", "group": ["2WD", "LARČ7"], "car": "Volkswagen Polo MK5", "stageTimeMs": 571270, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Viada", "country": "poland", "userName": "Polak K. - Grzywaczewski B.", "realName": "", "group": ["2WD"], "car": "BMW E46", "stageTimeMs": 577040, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Viada", "country": "lithuania", "userName": "Žiukelis M. - Kriaučiūnas S.", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 318 Ti Compact E46", "stageTimeMs": 577070, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Viada", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON> A.", "realName": "", "group": ["2WD", "LARČ7"], "car": "Volkswagen Polo MK5", "stageTimeMs": 577690, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Viada", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON><PERSON> - Sam<PERSON>lis M.", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 325 Ti Compact E46", "stageTimeMs": 577780, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 100000, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Viada", "country": "lithuania", "userName": "Bartkuvėnas Mantas - Bagonas Ž.", "realName": "", "group": ["2WD", "LARČ4"], "car": "Ford Fiesta Rally4", "stageTimeMs": 581350, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Viada", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> Gegužinskas R.", "realName": "", "group": ["AWD", "LARČ3"], "car": "Subaru Impreza STi N10", "stageTimeMs": 581780, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Viada", "country": "lithuania", "userName": "Kvedaras <PERSON> - <PERSON><PERSON><PERSON><PERSON>", "realName": "", "group": ["2WD", "LARČ4"], "car": "Peugeot 208 Rally4", "stageTimeMs": 585470, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Viada", "country": "poland", "userName": "Bilski Rafał - Radzik <PERSON>gni<PERSON>zka", "realName": "", "group": ["2WD", "LARČ4"], "car": "Ford Fiesta Rally4", "stageTimeMs": 589210, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Viada", "country": "lithuania", "userName": "Čiutelė D. - Zvicevičius D.", "realName": "", "group": ["2WD", "Historic"], "car": "Lada VAZ 2105 VFTS", "stageTimeMs": 589430, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Viada", "country": "lithuania", "userName": "Povilionis P. - Balčiūnas V.", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 328 Ti Compact E46", "stageTimeMs": 590130, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Viada", "country": "lithuania", "userName": "Račkauskas Marius - Tolstych A.", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 130i E87", "stageTimeMs": 594130, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Viada", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON><PERSON> - Vainiūnas J.", "realName": "", "group": ["2WD", "LARČ7"], "car": "Volkswagen Polo MK5", "stageTimeMs": 595380, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Viada", "country": "lithuania", "userName": "Griškus E. - Šmigelskas V.", "realName": "", "group": ["2WD", "LARČ6"], "car": "BMW 325 Ti Compact E46", "stageTimeMs": 603710, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Viada", "country": "lithuania", "userName": "Chocka Gediminas - Čeledinas V.", "realName": "", "group": ["2WD"], "car": "BMW 325 Ti Compact E46", "stageTimeMs": 605230, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Viada", "country": "lithuania", "userName": "Svirskas Patrikas - Svirskas R.", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 325 Ti Compact E46", "stageTimeMs": 611510, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Viada", "country": "poland", "userName": "Kruszewski A. - Kruszewska K.", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 325 Ti Compact E46", "stageTimeMs": 621770, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Viada", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>.", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 328 Ti Compact E46", "stageTimeMs": 633290, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Viada", "country": "lithuania", "userName": "Valev<PERSON> - Greč<PERSON>j", "realName": "", "group": ["2WD", "Historic"], "car": "Opel Ascona B", "stageTimeMs": 665080, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Viada", "country": "estonia", "userName": "Tam<PERSON>ja <PERSON> - Ääremaa Henri", "realName": "", "group": ["2WD"], "car": "BMW M3 E36", "stageTimeMs": 743480, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Viada", "country": "poland", "userName": "Ważny Paweł - <PERSON><PERSON><PERSON>", "realName": "", "group": ["AWD", "LARČ3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 1248970, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 6, "stageName": "Alburnus 1", "country": "lithuania", "userName": "Žala Vaidotas - Pūķis Ivo", "realName": "", "group": ["AWD"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 406510, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 6, "stageName": "Alburnus 1", "country": "lithuania", "userName": "<PERSON><PERSON> - <PERSON><PERSON><PERSON>kas E.", "realName": "", "group": ["AWD", "LARČ1"], "car": "Škoda Fabia N5+", "stageTimeMs": 427980, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 6, "stageName": "Alburnus 1", "country": "lithuania", "userName": "Steponavičius R. - Ketvirtis D.", "realName": "", "group": ["AWD", "LARČ5"], "car": "Ford Fiesta N5", "stageTimeMs": 428760, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 6, "stageName": "Alburnus 1", "country": "poland", "userName": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "realName": "", "group": ["AWD", "LARČ2"], "car": "Ford Fiesta Rally2", "stageTimeMs": 438700, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 6, "stageName": "Alburnus 1", "country": "poland", "userName": "<PERSON><PERSON><PERSON><PERSON> T. - <PERSON><PERSON><PERSON> A.", "realName": "", "group": ["AWD", "LARČ3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 439450, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 6, "stageName": "Alburnus 1", "country": "lithuania", "userName": "Paškevičius V. - Vičiūnas P.", "realName": "", "group": ["AWD", "LARČ5"], "car": "Ford Fiesta N5", "stageTimeMs": 441290, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 6, "stageName": "Alburnus 1", "country": "lithuania", "userName": "G<PERSON><PERSON><PERSON>čius D. - N<PERSON>rtavičius T.", "realName": "", "group": ["AWD", "LARČ5"], "car": "Ford Fiesta N5", "stageTimeMs": 445530, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 6, "stageName": "Alburnus 1", "country": "lithuania", "userName": "Simaška Justas - Kalėda Aras", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 318 Ti Compact E46", "stageTimeMs": 446250, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 6, "stageName": "Alburnus 1", "country": "lithuania", "userName": "E<PERSON><PERSON><PERSON><PERSON><PERSON> - Barysas J.", "realName": "", "group": ["AWD", "LARČ5"], "car": "Ford Fiesta N5", "stageTimeMs": 447440, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 6, "stageName": "Alburnus 1", "country": "poland", "userName": "Domżała Aron - <PERSON>owski Adrian", "realName": "", "group": ["2WD"], "car": "BMW M3 E36", "stageTimeMs": 448220, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 6, "stageName": "Alburnus 1", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON>.", "realName": "", "group": ["2WD", "LARČ6"], "car": "BMW M3 E46 Compact", "stageTimeMs": 448970, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 6, "stageName": "Alburnus 1", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>.", "realName": "", "group": ["AWD", "LARČ3"], "car": "Mitsubishi Lancer Evo IX", "stageTimeMs": 449080, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 6, "stageName": "Alburnus 1", "country": "poland", "userName": "Ważny Paweł - <PERSON><PERSON><PERSON>", "realName": "", "group": ["AWD", "LARČ3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 451360, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 6, "stageName": "Alburnus 1", "country": "ukraine", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["AWD", "LARČ3"], "car": "Škoda Fabia Rally2-Kit", "stageTimeMs": 454030, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 6, "stageName": "Alburnus 1", "country": "latvia", "userName": "Blūms Emil<PERSON> - <PERSON><PERSON><PERSON><PERSON><PERSON>", "realName": "", "group": ["AWD", "LARČ3"], "car": "Mitsubishi Lancer Evo IX", "stageTimeMs": 454600, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 6, "stageName": "Alburnus 1", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 316 Ti Compact E46", "stageTimeMs": 460060, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 6, "stageName": "Alburnus 1", "country": "estonia", "userName": "Kõrgesaar Pranko - <PERSON><PERSON><PERSON> Ott", "realName": "", "group": ["2WD", "LARČ4"], "car": "Ford Fiesta Rally4", "stageTimeMs": 462680, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 6, "stageName": "Alburnus 1", "country": "lithuania", "userName": "Miknius Jonas - <PERSON><PERSON><PERSON><PERSON> P.", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 328 Ti Compact E46", "stageTimeMs": 462950, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 6, "stageName": "Alburnus 1", "country": "poland", "userName": "<PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["2WD", "LARČ4"], "car": "Ford Fiesta Rally4", "stageTimeMs": 463770, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 6, "stageName": "Alburnus 1", "country": "lithuania", "userName": "Brok<PERSON>us <PERSON> - <PERSON><PERSON><PERSON><PERSON> R.", "realName": "", "group": ["2WD", "LARČ4"], "car": "Ford Fiesta Rally4", "stageTimeMs": 464890, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 6, "stageName": "Alburnus 1", "country": "turkey", "userName": "Alakoç Can - Paliukėnas A.", "realName": "", "group": ["2WD", "LARČ4"], "car": "Ford Fiesta Rally4", "stageTimeMs": 465790, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 6, "stageName": "Alburnus 1", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["AWD", "LARČ2"], "car": "Škoda Fabia R5", "stageTimeMs": 469600, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 6, "stageName": "Alburnus 1", "country": "estonia", "userName": "Tam<PERSON>ja <PERSON> - Ääremaa Henri", "realName": "", "group": ["2WD"], "car": "BMW M3 E36", "stageTimeMs": 469760, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 6, "stageName": "Alburnus 1", "country": "lithuania", "userName": "Kutka Man<PERSON> - <PERSON><PERSON><PERSON><PERSON>s <PERSON>au<PERSON>", "realName": "", "group": ["2WD", "LARČ7"], "car": "Volkswagen Polo MK5", "stageTimeMs": 471190, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 6, "stageName": "Alburnus 1", "country": "lithuania", "userName": "Žiukelis M. - Kriaučiūnas S.", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 318 Ti Compact E46", "stageTimeMs": 473900, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 6, "stageName": "Alburnus 1", "country": "poland", "userName": "Polak K. - Grzywaczewski B.", "realName": "", "group": ["2WD"], "car": "BMW E46", "stageTimeMs": 474870, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 6, "stageName": "Alburnus 1", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON><PERSON> - Sam<PERSON>lis M.", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 325 Ti Compact E46", "stageTimeMs": 476410, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 6, "stageName": "Alburnus 1", "country": "poland", "userName": "Bilski Rafał - Radzik <PERSON>gni<PERSON>zka", "realName": "", "group": ["2WD", "LARČ4"], "car": "Ford Fiesta Rally4", "stageTimeMs": 477930, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 6, "stageName": "Alburnus 1", "country": "lithuania", "userName": "Bartkuvėnas Mantas - Bagonas Ž.", "realName": "", "group": ["2WD", "LARČ4"], "car": "Ford Fiesta Rally4", "stageTimeMs": 480570, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 6, "stageName": "Alburnus 1", "country": "lithuania", "userName": "Čiutelė D. - Zvicevičius D.", "realName": "", "group": ["2WD", "Historic"], "car": "Lada VAZ 2105 VFTS", "stageTimeMs": 480990, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 6, "stageName": "Alburnus 1", "country": "lithuania", "userName": "Kvedaras <PERSON> - <PERSON><PERSON><PERSON><PERSON>", "realName": "", "group": ["2WD", "LARČ4"], "car": "Peugeot 208 Rally4", "stageTimeMs": 481690, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 6, "stageName": "Alburnus 1", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> Gegužinskas R.", "realName": "", "group": ["AWD", "LARČ3"], "car": "Subaru Impreza STi N10", "stageTimeMs": 482700, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 6, "stageName": "Alburnus 1", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON> A.", "realName": "", "group": ["2WD", "LARČ7"], "car": "Volkswagen Polo MK5", "stageTimeMs": 483040, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 6, "stageName": "Alburnus 1", "country": "lithuania", "userName": "Povilionis P. - Balčiūnas V.", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 328 Ti Compact E46", "stageTimeMs": 484680, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 6, "stageName": "Alburnus 1", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON><PERSON> - Vainiūnas J.", "realName": "", "group": ["2WD", "LARČ7"], "car": "Volkswagen Polo MK5", "stageTimeMs": 486860, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 6, "stageName": "Alburnus 1", "country": "lithuania", "userName": "Chocka Gediminas - Čeledinas V.", "realName": "", "group": ["2WD"], "car": "BMW 325 Ti Compact E46", "stageTimeMs": 490810, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 6, "stageName": "Alburnus 1", "country": "lithuania", "userName": "Griškus E. - Šmigelskas V.", "realName": "", "group": ["2WD", "LARČ6"], "car": "BMW 325 Ti Compact E46", "stageTimeMs": 493560, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 6, "stageName": "Alburnus 1", "country": "lithuania", "userName": "Račkauskas Marius - Tolstych A.", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 130i E87", "stageTimeMs": 499450, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 6, "stageName": "Alburnus 1", "country": "lithuania", "userName": "Svirskas Patrikas - Svirskas R.", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 325 Ti Compact E46", "stageTimeMs": 500800, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 6, "stageName": "Alburnus 1", "country": "poland", "userName": "Kruszewski A. - Kruszewska K.", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 325 Ti Compact E46", "stageTimeMs": 505670, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 6, "stageName": "Alburnus 1", "country": "lithuania", "userName": "Valev<PERSON> - Greč<PERSON>j", "realName": "", "group": ["2WD", "Historic"], "car": "Opel Ascona B", "stageTimeMs": 519280, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 6, "stageName": "Alburnus 1", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>.", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 328 Ti Compact E46", "stageTimeMs": 522640, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 7, "stageName": "TOPsport", "country": "lithuania", "userName": "Žiukelis M. - Kriaučiūnas S.", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 318 Ti Compact E46", "stageTimeMs": 0, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": false, "comment": []}, {"stageNumber": 7, "stageName": "TOPsport", "country": "lithuania", "userName": "Žala Vaidotas - Pūķis Ivo", "realName": "", "group": ["AWD"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 475290, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 7, "stageName": "TOPsport", "country": "lithuania", "userName": "Steponavičius R. - Ketvirtis D.", "realName": "", "group": ["AWD", "LARČ5"], "car": "Ford Fiesta N5", "stageTimeMs": 506560, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 7, "stageName": "TOPsport", "country": "lithuania", "userName": "<PERSON><PERSON> - <PERSON><PERSON><PERSON>kas E.", "realName": "", "group": ["AWD", "LARČ1"], "car": "Škoda Fabia N5+", "stageTimeMs": 512660, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 7, "stageName": "TOPsport", "country": "lithuania", "userName": "G<PERSON><PERSON><PERSON>čius D. - N<PERSON>rtavičius T.", "realName": "", "group": ["AWD", "LARČ5"], "car": "Ford Fiesta N5", "stageTimeMs": 512880, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 7, "stageName": "TOPsport", "country": "poland", "userName": "<PERSON><PERSON><PERSON><PERSON> T. - <PERSON><PERSON><PERSON> A.", "realName": "", "group": ["AWD", "LARČ3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 517100, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 7, "stageName": "TOPsport", "country": "lithuania", "userName": "Paškevičius V. - Vičiūnas P.", "realName": "", "group": ["AWD", "LARČ5"], "car": "Ford Fiesta N5", "stageTimeMs": 523950, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 10000, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 7, "stageName": "TOPsport", "country": "poland", "userName": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "realName": "", "group": ["AWD", "LARČ2"], "car": "Ford Fiesta Rally2", "stageTimeMs": 529160, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 7, "stageName": "TOPsport", "country": "lithuania", "userName": "Simaška Justas - Kalėda Aras", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 318 Ti Compact E46", "stageTimeMs": 532040, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 7, "stageName": "TOPsport", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>.", "realName": "", "group": ["AWD", "LARČ3"], "car": "Mitsubishi Lancer Evo IX", "stageTimeMs": 532680, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 7, "stageName": "TOPsport", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON>.", "realName": "", "group": ["2WD", "LARČ6"], "car": "BMW M3 E46 Compact", "stageTimeMs": 533570, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 7, "stageName": "TOPsport", "country": "latvia", "userName": "Blūms Emil<PERSON> - <PERSON><PERSON><PERSON><PERSON><PERSON>", "realName": "", "group": ["AWD", "LARČ3"], "car": "Mitsubishi Lancer Evo IX", "stageTimeMs": 537760, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 7, "stageName": "TOPsport", "country": "lithuania", "userName": "E<PERSON><PERSON><PERSON><PERSON><PERSON> - Barysas J.", "realName": "", "group": ["AWD", "LARČ5"], "car": "Ford Fiesta N5", "stageTimeMs": 539170, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 7, "stageName": "TOPsport", "country": "lithuania", "userName": "Kutka Man<PERSON> - <PERSON><PERSON><PERSON><PERSON>s <PERSON>au<PERSON>", "realName": "", "group": ["2WD", "LARČ7"], "car": "Volkswagen Polo MK5", "stageTimeMs": 544630, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 7, "stageName": "TOPsport", "country": "estonia", "userName": "Kõrgesaar Pranko - <PERSON><PERSON><PERSON> Ott", "realName": "", "group": ["2WD", "LARČ4"], "car": "Ford Fiesta Rally4", "stageTimeMs": 547080, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 7, "stageName": "TOPsport", "country": "poland", "userName": "<PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["2WD", "LARČ4"], "car": "Ford Fiesta Rally4", "stageTimeMs": 547120, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 7, "stageName": "TOPsport", "country": "turkey", "userName": "Alakoç Can - Paliukėnas A.", "realName": "", "group": ["2WD", "LARČ4"], "car": "Ford Fiesta Rally4", "stageTimeMs": 547290, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 7, "stageName": "TOPsport", "country": "poland", "userName": "Domżała Aron - <PERSON>owski Adrian", "realName": "", "group": ["2WD"], "car": "BMW M3 E36", "stageTimeMs": 547350, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 7, "stageName": "TOPsport", "country": "ukraine", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["AWD", "LARČ3"], "car": "Škoda Fabia Rally2-Kit", "stageTimeMs": 547740, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 7, "stageName": "TOPsport", "country": "poland", "userName": "Ważny Paweł - <PERSON><PERSON><PERSON>", "realName": "", "group": ["AWD", "LARČ3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 552640, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 7, "stageName": "TOPsport", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 316 Ti Compact E46", "stageTimeMs": 553400, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 7, "stageName": "TOPsport", "country": "lithuania", "userName": "Brok<PERSON>us <PERSON> - <PERSON><PERSON><PERSON><PERSON> R.", "realName": "", "group": ["2WD", "LARČ4"], "car": "Ford Fiesta Rally4", "stageTimeMs": 558690, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 7, "stageName": "TOPsport", "country": "lithuania", "userName": "Kvedaras <PERSON> - <PERSON><PERSON><PERSON><PERSON>", "realName": "", "group": ["2WD", "LARČ4"], "car": "Peugeot 208 Rally4", "stageTimeMs": 558770, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 7, "stageName": "TOPsport", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["AWD", "LARČ2"], "car": "Škoda Fabia R5", "stageTimeMs": 561230, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 7, "stageName": "TOPsport", "country": "lithuania", "userName": "Bartkuvėnas Mantas - Bagonas Ž.", "realName": "", "group": ["2WD", "LARČ4"], "car": "Ford Fiesta Rally4", "stageTimeMs": 572270, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 7, "stageName": "TOPsport", "country": "lithuania", "userName": "Miknius Jonas - <PERSON><PERSON><PERSON><PERSON> P.", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 328 Ti Compact E46", "stageTimeMs": 574220, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 7, "stageName": "TOPsport", "country": "lithuania", "userName": "Povilionis P. - Balčiūnas V.", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 328 Ti Compact E46", "stageTimeMs": 582080, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 7, "stageName": "TOPsport", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> Gegužinskas R.", "realName": "", "group": ["AWD", "LARČ3"], "car": "Subaru Impreza STi N10", "stageTimeMs": 582470, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 7, "stageName": "TOPsport", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON><PERSON> - Sam<PERSON>lis M.", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 325 Ti Compact E46", "stageTimeMs": 585770, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 7, "stageName": "TOPsport", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON><PERSON> - Vainiūnas J.", "realName": "", "group": ["2WD", "LARČ7"], "car": "Volkswagen Polo MK5", "stageTimeMs": 590710, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 7, "stageName": "TOPsport", "country": "lithuania", "userName": "Račkauskas Marius - Tolstych A.", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 130i E87", "stageTimeMs": 590860, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 7, "stageName": "TOPsport", "country": "poland", "userName": "Polak K. - Grzywaczewski B.", "realName": "", "group": ["2WD"], "car": "BMW E46", "stageTimeMs": 595640, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 7, "stageName": "TOPsport", "country": "lithuania", "userName": "Čiutelė D. - Zvicevičius D.", "realName": "", "group": ["2WD", "Historic"], "car": "Lada VAZ 2105 VFTS", "stageTimeMs": 598700, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 7, "stageName": "TOPsport", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON> A.", "realName": "", "group": ["2WD", "LARČ7"], "car": "Volkswagen Polo MK5", "stageTimeMs": 599060, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 7, "stageName": "TOPsport", "country": "lithuania", "userName": "Griškus E. - Šmigelskas V.", "realName": "", "group": ["2WD", "LARČ6"], "car": "BMW 325 Ti Compact E46", "stageTimeMs": 613210, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 7, "stageName": "TOPsport", "country": "lithuania", "userName": "Svirskas Patrikas - Svirskas R.", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 325 Ti Compact E46", "stageTimeMs": 617120, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 7, "stageName": "TOPsport", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>.", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 328 Ti Compact E46", "stageTimeMs": 621450, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 7, "stageName": "TOPsport", "country": "poland", "userName": "Kruszewski A. - Kruszewska K.", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 325 Ti Compact E46", "stageTimeMs": 621460, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 7, "stageName": "TOPsport", "country": "lithuania", "userName": "Chocka Gediminas - Čeledinas V.", "realName": "", "group": ["2WD"], "car": "BMW 325 Ti Compact E46", "stageTimeMs": 621990, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 7, "stageName": "TOPsport", "country": "estonia", "userName": "Tam<PERSON>ja <PERSON> - Ääremaa Henri", "realName": "", "group": ["2WD"], "car": "BMW M3 E36", "stageTimeMs": 656930, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 7, "stageName": "TOPsport", "country": "lithuania", "userName": "Valev<PERSON> - Greč<PERSON>j", "realName": "", "group": ["2WD", "Historic"], "car": "Opel Ascona B", "stageTimeMs": 658070, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 7, "stageName": "TOPsport", "country": "poland", "userName": "Bilski Rafał - Radzik <PERSON>gni<PERSON>zka", "realName": "", "group": ["2WD", "LARČ4"], "car": "Ford Fiesta Rally4", "stageTimeMs": 707070, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 8, "stageName": "Alburnus 2", "country": "ukraine", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["AWD", "LARČ3"], "car": "Škoda Fabia Rally2-Kit", "stageTimeMs": 0, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": false, "comment": []}, {"stageNumber": 8, "stageName": "Alburnus 2", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON><PERSON> - Sam<PERSON>lis M.", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 325 Ti Compact E46", "stageTimeMs": 0, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": false, "comment": []}, {"stageNumber": 8, "stageName": "Alburnus 2", "country": "poland", "userName": "Bilski Rafał - Radzik <PERSON>gni<PERSON>zka", "realName": "", "group": ["2WD", "LARČ4"], "car": "Ford Fiesta Rally4", "stageTimeMs": 0, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": false, "comment": []}, {"stageNumber": 8, "stageName": "Alburnus 2", "country": "lithuania", "userName": "Žala Vaidotas - Pūķis Ivo", "realName": "", "group": ["AWD"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 400900, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 8, "stageName": "Alburnus 2", "country": "lithuania", "userName": "Steponavičius R. - Ketvirtis D.", "realName": "", "group": ["AWD", "LARČ5"], "car": "Ford Fiesta N5", "stageTimeMs": 422040, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 8, "stageName": "Alburnus 2", "country": "lithuania", "userName": "<PERSON><PERSON> - <PERSON><PERSON><PERSON>kas E.", "realName": "", "group": ["AWD", "LARČ1"], "car": "Škoda Fabia N5+", "stageTimeMs": 424590, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 8, "stageName": "Alburnus 2", "country": "lithuania", "userName": "G<PERSON><PERSON><PERSON>čius D. - N<PERSON>rtavičius T.", "realName": "", "group": ["AWD", "LARČ5"], "car": "Ford Fiesta N5", "stageTimeMs": 426170, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 8, "stageName": "Alburnus 2", "country": "poland", "userName": "<PERSON><PERSON><PERSON><PERSON> T. - <PERSON><PERSON><PERSON> A.", "realName": "", "group": ["AWD", "LARČ3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 430660, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 8, "stageName": "Alburnus 2", "country": "poland", "userName": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "realName": "", "group": ["AWD", "LARČ2"], "car": "Ford Fiesta Rally2", "stageTimeMs": 435220, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 8, "stageName": "Alburnus 2", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON>.", "realName": "", "group": ["2WD", "LARČ6"], "car": "BMW M3 E46 Compact", "stageTimeMs": 436240, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 10000, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 8, "stageName": "Alburnus 2", "country": "lithuania", "userName": "Paškevičius V. - Vičiūnas P.", "realName": "", "group": ["AWD", "LARČ5"], "car": "Ford Fiesta N5", "stageTimeMs": 440390, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 8, "stageName": "Alburnus 2", "country": "poland", "userName": "Domżała Aron - <PERSON>owski Adrian", "realName": "", "group": ["2WD"], "car": "BMW M3 E36", "stageTimeMs": 440980, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 8, "stageName": "Alburnus 2", "country": "lithuania", "userName": "E<PERSON><PERSON><PERSON><PERSON><PERSON> - Barysas J.", "realName": "", "group": ["AWD", "LARČ5"], "car": "Ford Fiesta N5", "stageTimeMs": 441530, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 8, "stageName": "Alburnus 2", "country": "latvia", "userName": "Blūms Emil<PERSON> - <PERSON><PERSON><PERSON><PERSON><PERSON>", "realName": "", "group": ["AWD", "LARČ3"], "car": "Mitsubishi Lancer Evo IX", "stageTimeMs": 443330, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 8, "stageName": "Alburnus 2", "country": "estonia", "userName": "Tam<PERSON>ja <PERSON> - Ääremaa Henri", "realName": "", "group": ["2WD"], "car": "BMW M3 E36", "stageTimeMs": 450630, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 8, "stageName": "Alburnus 2", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 316 Ti Compact E46", "stageTimeMs": 450770, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 8, "stageName": "Alburnus 2", "country": "estonia", "userName": "Kõrgesaar Pranko - <PERSON><PERSON><PERSON> Ott", "realName": "", "group": ["2WD", "LARČ4"], "car": "Ford Fiesta Rally4", "stageTimeMs": 451990, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 8, "stageName": "Alburnus 2", "country": "turkey", "userName": "Alakoç Can - Paliukėnas A.", "realName": "", "group": ["2WD", "LARČ4"], "car": "Ford Fiesta Rally4", "stageTimeMs": 452340, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 8, "stageName": "Alburnus 2", "country": "poland", "userName": "<PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["2WD", "LARČ4"], "car": "Ford Fiesta Rally4", "stageTimeMs": 455480, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 8, "stageName": "Alburnus 2", "country": "lithuania", "userName": "Kutka Man<PERSON> - <PERSON><PERSON><PERSON><PERSON>s <PERSON>au<PERSON>", "realName": "", "group": ["2WD", "LARČ7"], "car": "Volkswagen Polo MK5", "stageTimeMs": 458590, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 8, "stageName": "Alburnus 2", "country": "lithuania", "userName": "Brok<PERSON>us <PERSON> - <PERSON><PERSON><PERSON><PERSON> R.", "realName": "", "group": ["2WD", "LARČ4"], "car": "Ford Fiesta Rally4", "stageTimeMs": 458680, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 8, "stageName": "Alburnus 2", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["AWD", "LARČ2"], "car": "Škoda Fabia R5", "stageTimeMs": 463490, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 8, "stageName": "Alburnus 2", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>.", "realName": "", "group": ["AWD", "LARČ3"], "car": "Mitsubishi Lancer Evo IX", "stageTimeMs": 464300, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 8, "stageName": "Alburnus 2", "country": "lithuania", "userName": "Kvedaras <PERSON> - <PERSON><PERSON><PERSON><PERSON>", "realName": "", "group": ["2WD", "LARČ4"], "car": "Peugeot 208 Rally4", "stageTimeMs": 465080, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 8, "stageName": "Alburnus 2", "country": "lithuania", "userName": "Miknius Jonas - <PERSON><PERSON><PERSON><PERSON> P.", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 328 Ti Compact E46", "stageTimeMs": 470960, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 8, "stageName": "Alburnus 2", "country": "poland", "userName": "Polak K. - Grzywaczewski B.", "realName": "", "group": ["2WD"], "car": "BMW E46", "stageTimeMs": 476240, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 8, "stageName": "Alburnus 2", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON><PERSON> - Vainiūnas J.", "realName": "", "group": ["2WD", "LARČ7"], "car": "Volkswagen Polo MK5", "stageTimeMs": 476290, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 8, "stageName": "Alburnus 2", "country": "lithuania", "userName": "Čiutelė D. - Zvicevičius D.", "realName": "", "group": ["2WD", "Historic"], "car": "Lada VAZ 2105 VFTS", "stageTimeMs": 481200, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 8, "stageName": "Alburnus 2", "country": "lithuania", "userName": "Povilionis P. - Balčiūnas V.", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 328 Ti Compact E46", "stageTimeMs": 481760, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 8, "stageName": "Alburnus 2", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> Gegužinskas R.", "realName": "", "group": ["AWD", "LARČ3"], "car": "Subaru Impreza STi N10", "stageTimeMs": 483800, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 8, "stageName": "Alburnus 2", "country": "lithuania", "userName": "Bartkuvėnas Mantas - Bagonas Ž.", "realName": "", "group": ["2WD", "LARČ4"], "car": "Ford Fiesta Rally4", "stageTimeMs": 484370, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 8, "stageName": "Alburnus 2", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON> A.", "realName": "", "group": ["2WD", "LARČ7"], "car": "Volkswagen Polo MK5", "stageTimeMs": 489020, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 8, "stageName": "Alburnus 2", "country": "lithuania", "userName": "Chocka Gediminas - Čeledinas V.", "realName": "", "group": ["2WD"], "car": "BMW 325 Ti Compact E46", "stageTimeMs": 493940, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 8, "stageName": "Alburnus 2", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>.", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 328 Ti Compact E46", "stageTimeMs": 508690, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 8, "stageName": "Alburnus 2", "country": "lithuania", "userName": "Svirskas Patrikas - Svirskas R.", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 325 Ti Compact E46", "stageTimeMs": 509460, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 8, "stageName": "Alburnus 2", "country": "poland", "userName": "Kruszewski A. - Kruszewska K.", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 325 Ti Compact E46", "stageTimeMs": 512250, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 8, "stageName": "Alburnus 2", "country": "lithuania", "userName": "Račkauskas Marius - Tolstych A.", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 130i E87", "stageTimeMs": 514810, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 8, "stageName": "Alburnus 2", "country": "lithuania", "userName": "Valev<PERSON> - Greč<PERSON>j", "realName": "", "group": ["2WD", "Historic"], "car": "Opel Ascona B", "stageTimeMs": 531690, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 8, "stageName": "Alburnus 2", "country": "lithuania", "userName": "Griškus E. - Šmigelskas V.", "realName": "", "group": ["2WD", "LARČ6"], "car": "BMW 325 Ti Compact E46", "stageTimeMs": 536760, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 8, "stageName": "Alburnus 2", "country": "poland", "userName": "Ważny Paweł - <PERSON><PERSON><PERSON>", "realName": "", "group": ["AWD", "LARČ3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 740330, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 8, "stageName": "Alburnus 2", "country": "lithuania", "userName": "Simaška Justas - Kalėda Aras", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 318 Ti Compact E46", "stageTimeMs": 805820, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 9, "stageName": "Elektrėnų Mero taurė", "country": "lithuania", "userName": "Griškus E. - Šmigelskas V.", "realName": "", "group": ["2WD", "LARČ6"], "car": "BMW 325 Ti Compact E46", "stageTimeMs": 0, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": false, "comment": []}, {"stageNumber": 9, "stageName": "Elektrėnų Mero taurė", "country": "lithuania", "userName": "Žala Vaidotas - Pūķis Ivo", "realName": "", "group": ["AWD"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 164100, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 9, "stageName": "Elektrėnų Mero taurė", "country": "lithuania", "userName": "Steponavičius R. - Ketvirtis D.", "realName": "", "group": ["AWD", "LARČ5"], "car": "Ford Fiesta N5", "stageTimeMs": 167630, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 9, "stageName": "Elektrėnų Mero taurė", "country": "lithuania", "userName": "G<PERSON><PERSON><PERSON>čius D. - N<PERSON>rtavičius T.", "realName": "", "group": ["AWD", "LARČ5"], "car": "Ford Fiesta N5", "stageTimeMs": 168660, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 9, "stageName": "Elektrėnų Mero taurė", "country": "estonia", "userName": "Kõrgesaar Pranko - <PERSON><PERSON><PERSON> Ott", "realName": "", "group": ["2WD", "LARČ4"], "car": "Ford Fiesta Rally4", "stageTimeMs": 171370, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 9, "stageName": "Elektrėnų Mero taurė", "country": "poland", "userName": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "realName": "", "group": ["AWD", "LARČ2"], "car": "Ford Fiesta Rally2", "stageTimeMs": 171920, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 9, "stageName": "Elektrėnų Mero taurė", "country": "lithuania", "userName": "E<PERSON><PERSON><PERSON><PERSON><PERSON> - Barysas J.", "realName": "", "group": ["AWD", "LARČ5"], "car": "Ford Fiesta N5", "stageTimeMs": 172410, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 9, "stageName": "Elektrėnų Mero taurė", "country": "lithuania", "userName": "Paškevičius V. - Vičiūnas P.", "realName": "", "group": ["AWD", "LARČ5"], "car": "Ford Fiesta N5", "stageTimeMs": 174030, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 9, "stageName": "Elektrėnų Mero taurė", "country": "turkey", "userName": "Alakoç Can - Paliukėnas A.", "realName": "", "group": ["2WD", "LARČ4"], "car": "Ford Fiesta Rally4", "stageTimeMs": 175550, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 9, "stageName": "Elektrėnų Mero taurė", "country": "latvia", "userName": "Blūms Emil<PERSON> - <PERSON><PERSON><PERSON><PERSON><PERSON>", "realName": "", "group": ["AWD", "LARČ3"], "car": "Mitsubishi Lancer Evo IX", "stageTimeMs": 175730, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 9, "stageName": "Elektrėnų Mero taurė", "country": "lithuania", "userName": "Miknius Jonas - <PERSON><PERSON><PERSON><PERSON> P.", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 328 Ti Compact E46", "stageTimeMs": 176320, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 9, "stageName": "Elektrėnų Mero taurė", "country": "lithuania", "userName": "Kutka Man<PERSON> - <PERSON><PERSON><PERSON><PERSON>s <PERSON>au<PERSON>", "realName": "", "group": ["2WD", "LARČ7"], "car": "Volkswagen Polo MK5", "stageTimeMs": 176390, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 9, "stageName": "Elektrėnų Mero taurė", "country": "lithuania", "userName": "<PERSON><PERSON> - <PERSON><PERSON><PERSON>kas E.", "realName": "", "group": ["AWD", "LARČ1"], "car": "Škoda Fabia N5+", "stageTimeMs": 177650, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 9, "stageName": "Elektrėnų Mero taurė", "country": "poland", "userName": "Ważny Paweł - <PERSON><PERSON><PERSON>", "realName": "", "group": ["AWD", "LARČ3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 178170, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 9, "stageName": "Elektrėnų Mero taurė", "country": "poland", "userName": "Domżała Aron - <PERSON>owski Adrian", "realName": "", "group": ["2WD"], "car": "BMW M3 E36", "stageTimeMs": 178580, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 20000, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 9, "stageName": "Elektrėnų Mero taurė", "country": "lithuania", "userName": "Simaška Justas - Kalėda Aras", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 318 Ti Compact E46", "stageTimeMs": 179100, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 9, "stageName": "Elektrėnų Mero taurė", "country": "lithuania", "userName": "Čiutelė D. - Zvicevičius D.", "realName": "", "group": ["2WD", "Historic"], "car": "Lada VAZ 2105 VFTS", "stageTimeMs": 179130, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 9, "stageName": "Elektrėnų Mero taurė", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>.", "realName": "", "group": ["AWD", "LARČ3"], "car": "Mitsubishi Lancer Evo IX", "stageTimeMs": 179170, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 9, "stageName": "Elektrėnų Mero taurė", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 316 Ti Compact E46", "stageTimeMs": 180320, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 9, "stageName": "Elektrėnų Mero taurė", "country": "poland", "userName": "<PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["2WD", "LARČ4"], "car": "Ford Fiesta Rally4", "stageTimeMs": 180570, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 9, "stageName": "Elektrėnų Mero taurė", "country": "lithuania", "userName": "Bartkuvėnas Mantas - Bagonas Ž.", "realName": "", "group": ["2WD", "LARČ4"], "car": "Ford Fiesta Rally4", "stageTimeMs": 180810, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 9, "stageName": "Elektrėnų Mero taurė", "country": "lithuania", "userName": "Kvedaras <PERSON> - <PERSON><PERSON><PERSON><PERSON>", "realName": "", "group": ["2WD", "LARČ4"], "car": "Peugeot 208 Rally4", "stageTimeMs": 183150, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 9, "stageName": "Elektrėnų Mero taurė", "country": "poland", "userName": "<PERSON><PERSON><PERSON><PERSON> T. - <PERSON><PERSON><PERSON> A.", "realName": "", "group": ["AWD", "LARČ3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 184090, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 20000, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 9, "stageName": "Elektrėnų Mero taurė", "country": "lithuania", "userName": "Chocka Gediminas - Čeledinas V.", "realName": "", "group": ["2WD"], "car": "BMW 325 Ti Compact E46", "stageTimeMs": 185410, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 9, "stageName": "Elektrėnų Mero taurė", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["AWD", "LARČ2"], "car": "Škoda Fabia R5", "stageTimeMs": 185560, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 9, "stageName": "Elektrėnų Mero taurė", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> Gegužinskas R.", "realName": "", "group": ["AWD", "LARČ3"], "car": "Subaru Impreza STi N10", "stageTimeMs": 185690, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 60000, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 9, "stageName": "Elektrėnų Mero taurė", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON> A.", "realName": "", "group": ["2WD", "LARČ7"], "car": "Volkswagen Polo MK5", "stageTimeMs": 186410, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 9, "stageName": "Elektrėnų Mero taurė", "country": "lithuania", "userName": "Brok<PERSON>us <PERSON> - <PERSON><PERSON><PERSON><PERSON> R.", "realName": "", "group": ["2WD", "LARČ4"], "car": "Ford Fiesta Rally4", "stageTimeMs": 186770, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 9, "stageName": "Elektrėnų Mero taurė", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON>.", "realName": "", "group": ["2WD", "LARČ6"], "car": "BMW M3 E46 Compact", "stageTimeMs": 187320, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 9, "stageName": "Elektrėnų Mero taurė", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON><PERSON> - Vainiūnas J.", "realName": "", "group": ["2WD", "LARČ7"], "car": "Volkswagen Polo MK5", "stageTimeMs": 188980, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 9, "stageName": "Elektrėnų Mero taurė", "country": "estonia", "userName": "Tam<PERSON>ja <PERSON> - Ääremaa Henri", "realName": "", "group": ["2WD"], "car": "BMW M3 E36", "stageTimeMs": 189480, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 9, "stageName": "Elektrėnų Mero taurė", "country": "lithuania", "userName": "Povilionis P. - Balčiūnas V.", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 328 Ti Compact E46", "stageTimeMs": 189550, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 9, "stageName": "Elektrėnų Mero taurė", "country": "lithuania", "userName": "Račkauskas Marius - Tolstych A.", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 130i E87", "stageTimeMs": 194410, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 9, "stageName": "Elektrėnų Mero taurė", "country": "poland", "userName": "Polak K. - Grzywaczewski B.", "realName": "", "group": ["2WD"], "car": "BMW E46", "stageTimeMs": 194860, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 20000, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 9, "stageName": "Elektrėnų Mero taurė", "country": "lithuania", "userName": "Svirskas Patrikas - Svirskas R.", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 325 Ti Compact E46", "stageTimeMs": 196770, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 9, "stageName": "Elektrėnų Mero taurė", "country": "lithuania", "userName": "Valev<PERSON> - Greč<PERSON>j", "realName": "", "group": ["2WD", "Historic"], "car": "Opel Ascona B", "stageTimeMs": 198340, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 9, "stageName": "Elektrėnų Mero taurė", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>.", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 328 Ti Compact E46", "stageTimeMs": 212780, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 20000, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 9, "stageName": "Elektrėnų Mero taurė", "country": "poland", "userName": "Kruszewski A. - Kruszewska K.", "realName": "", "group": ["2WD", "LARČ8"], "car": "BMW 325 Ti Compact E46", "stageTimeMs": 227010, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}]