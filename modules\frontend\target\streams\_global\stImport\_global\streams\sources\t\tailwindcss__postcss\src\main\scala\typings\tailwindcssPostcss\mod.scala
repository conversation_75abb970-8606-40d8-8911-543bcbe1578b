package typings.tailwindcssPostcss

import org.scalablytyped.runtime.Shortcut
import typings.postcss.mod.PluginCreator
import typings.tailwindcssPostcss.distMod.PluginOptions
import org.scalablytyped.runtime.StObject
import scala.scalajs.js
import scala.scalajs.js.annotation.{JSGlobalScope, JSGlobal, JSImport, JSName, JSBracketAccess}

/* from `exports` in `package.json` */
object mod extends Shortcut {
  
  @JSImport("@tailwindcss/postcss", JSImport.Namespace)
  @js.native
  val ^ : PluginCreator[PluginOptions] = js.native
  
  type _To = PluginCreator[PluginOptions]
  
  /* This means you don't have to write `^`, but can instead just say `mod.foo` */
  override def _to: PluginCreator[PluginOptions] = ^
}
