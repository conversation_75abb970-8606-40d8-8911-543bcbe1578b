/*
 * Copyright 2022 github.com/2m/rallyeye/contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package rallyeye

import difflicious.Differ
import difflicious.DiffResultPrinter
import munit.Assertions.*
import munit.Location

def assertDiffIsOk[A: Differ](obtained: A, expected: A)(implicit loc: Location): Unit =
  val result = summon[Differ[A]].diff(obtained, expected)
  if !result.isOk then fail(DiffResultPrinter.consoleOutput(result, 0).render)(loc)
