-- Ενεργοποίηση της επέκτασης για UUIDs
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- <PERSON>NUM types
CREATE TYPE rally_status AS ENUM ('upcoming', 'running', 'finished');
CREATE TYPE stage_status AS ENUM ('upcoming', 'running', 'finished');
CREATE TYPE entry_status AS ENUM ('entered', 'running', 'finished', 'retired', 'dns', 'dnf', 'dsq');
CREATE TYPE championship_type AS ENUM ('international', 'national', 'local');

-- Users table (admin/editor roles)
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  email TEXT UNIQUE NOT NULL,
  name TEXT NOT NULL,
  role TEXT NOT NULL CHECK (role IN ('admin', 'editor')),
  password_hash TEXT NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Persons table (common structure for drivers & codrivers)
CREATE TABLE persons (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  first_name TEXT NOT NULL,
  last_name TEXT NOT NULL,
  nationality TEXT NOT NULL,
  date_of_birth DATE,
  photo_url TEXT,
  bio TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Drivers / Codrivers mapped to persons
CREATE TABLE drivers (
  id UUID PRIMARY KEY REFERENCES persons(id)
);

CREATE TABLE codrivers (
  id UUID PRIMARY KEY REFERENCES persons(id)
);

-- Championships
CREATE TABLE championships (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  year INTEGER NOT NULL,
  type championship_type NOT NULL,
  description TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Rallies
CREATE TABLE rallies (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  country TEXT NOT NULL,
  start_date TIMESTAMPTZ NOT NULL,
  end_date TIMESTAMPTZ NOT NULL,
  status rally_status NOT NULL,
  surface TEXT NOT NULL,
  banner_url TEXT,
  logo_url TEXT,
  views INTEGER DEFAULT 0,
  current_stage TEXT,
  current_stage_start TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Stages
CREATE TABLE stages (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  rally_id UUID NOT NULL REFERENCES rallies(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  number INTEGER NOT NULL,
  length NUMERIC(6,2) NOT NULL,
  surface TEXT NOT NULL,
  start_time TIMESTAMPTZ NOT NULL,
  status stage_status NOT NULL,
  is_power_stage BOOLEAN DEFAULT FALSE,
  is_super_special BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
-- Teams
CREATE TABLE teams (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  country TEXT NOT NULL,
  logo_url TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Entries
CREATE TABLE entries (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  rally_id UUID NOT NULL REFERENCES rallies(id) ON DELETE CASCADE,
  driver_id UUID NOT NULL REFERENCES persons(id),
  codriver_id UUID NOT NULL REFERENCES persons(id),
  team_id UUID REFERENCES teams(id),
  car TEXT NOT NULL,
  number INTEGER NOT NULL,
  class TEXT NOT NULL,
  status entry_status NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Results
CREATE TABLE results (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  rally_id UUID NOT NULL REFERENCES rallies(id) ON DELETE CASCADE,
  stage_id UUID NOT NULL REFERENCES stages(id) ON DELETE CASCADE,
  entry_id UUID NOT NULL REFERENCES entries(id) ON DELETE CASCADE,
  time NUMERIC(10, 3) NOT NULL, -- Time in seconds
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Penalties
CREATE TABLE penalties (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  rally_id UUID NOT NULL REFERENCES rallies(id) ON DELETE CASCADE,
  stage_id UUID REFERENCES stages(id) ON DELETE CASCADE,
  stage_number INTEGER,
  entry_id UUID NOT NULL REFERENCES entries(id) ON DELETE CASCADE,
  time NUMERIC(10, 3) NOT NULL,
  reason TEXT NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Championship events
CREATE TABLE championship_events (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  championship_id UUID NOT NULL REFERENCES championships(id) ON DELETE CASCADE,
  rally_id UUID NOT NULL REFERENCES rallies(id) ON DELETE CASCADE,
  coefficient NUMERIC(3, 1) DEFAULT 1.0,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Power stage points
CREATE TABLE power_stage_points (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  rally_id UUID NOT NULL REFERENCES rallies(id) ON DELETE CASCADE,
  stage_id UUID NOT NULL REFERENCES stages(id) ON DELETE CASCADE,
  entry_id UUID NOT NULL REFERENCES entries(id) ON DELETE CASCADE,
  points INTEGER NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- News
CREATE TABLE news (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  title TEXT NOT NULL,
  content TEXT NOT NULL,
  excerpt TEXT NOT NULL,
  author_id UUID NOT NULL REFERENCES users(id),
  image_url TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes
-- Rallies
CREATE INDEX idx_rallies_status ON rallies(status);

-- Stages
CREATE INDEX idx_stages_rally_id ON stages(rally_id);
CREATE INDEX idx_stages_status ON stages(status);

-- Entries
CREATE INDEX idx_entries_rally_id ON entries(rally_id);
CREATE INDEX idx_entries_driver_id ON entries(driver_id);
CREATE INDEX idx_entries_codriver_id ON entries(codriver_id);
CREATE INDEX idx_entries_team_id ON entries(team_id);
CREATE INDEX idx_entries_class ON entries(class);

-- Results
CREATE INDEX idx_results_rally_id ON results(rally_id);
CREATE INDEX idx_results_stage_id ON results(stage_id);
CREATE INDEX idx_results_entry_id ON results(entry_id);

-- Penalties
CREATE INDEX idx_penalties_rally_id ON penalties(rally_id);
CREATE INDEX idx_penalties_entry_id ON penalties(entry_id);
CREATE INDEX idx_penalties_stage_id ON penalties(stage_id);

-- Championship Events
CREATE INDEX idx_championship_events_championship_id ON championship_events(championship_id);
CREATE INDEX idx_championship_events_rally_id ON championship_events(rally_id);

-- Power Stage Points
CREATE INDEX idx_power_stage_points_rally_id ON power_stage_points(rally_id);
CREATE INDEX idx_power_stage_points_stage_id ON power_stage_points(stage_id);
CREATE INDEX idx_power_stage_points_entry_id ON power_stage_points(entry_id);


-- Splits (for future intermediate timing data)
CREATE TABLE splits (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  stage_id UUID NOT NULL REFERENCES stages(id) ON DELETE CASCADE,
  entry_id UUID NOT NULL REFERENCES entries(id) ON DELETE CASCADE,
  split_number INTEGER NOT NULL,
  time NUMERIC(10, 3) NOT NULL, -- in seconds
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- View: overall_classification
CREATE VIEW overall_classification AS
SELECT
  e.rally_id,
  e.id AS entry_id,
  e.number,
  pd.first_name || ' ' || pd.last_name AS driver,
  pcd.first_name || ' ' || pcd.last_name AS codriver,
  SUM(r.time + COALESCE(p.time, 0)) AS total_time,
  RANK() OVER (PARTITION BY e.rally_id ORDER BY SUM(r.time + COALESCE(p.time, 0))) AS position,
  SUM(r.time + COALESCE(p.time, 0)) - MIN(SUM(r.time + COALESCE(p.time, 0))) OVER (PARTITION BY e.rally_id) AS time_diff
FROM entries e
JOIN results r ON r.entry_id = e.id
JOIN drivers d ON d.id = e.driver_id
JOIN persons pd ON pd.id = d.id
JOIN codrivers cd ON cd.id = e.codriver_id
JOIN persons pcd ON pcd.id = cd.id
LEFT JOIN penalties p ON p.entry_id = e.id AND p.stage_id = r.stage_id
GROUP BY e.rally_id, e.id, pd.first_name, pd.last_name, pcd.first_name, pcd.last_name, e.number;

-- View: power_stage_classification
CREATE VIEW power_stage_classification AS
SELECT
  ps.rally_id,
  ps.stage_id,
  e.id AS entry_id,
  e.number,
  pd.first_name || ' ' || pd.last_name AS driver,
  ps.points,
  RANK() OVER (PARTITION BY ps.stage_id ORDER BY ps.points DESC) AS position
FROM power_stage_points ps
JOIN entries e ON e.id = ps.entry_id
JOIN drivers d ON d.id = e.driver_id
JOIN persons pd ON pd.id = d.id;

-- View: current_stage_results
CREATE VIEW current_stage_results AS
SELECT
  r.rally_id,
  s.id AS stage_id,
  e.number,
  pd.first_name || ' ' || pd.last_name AS driver,
  r.time,
  RANK() OVER (PARTITION BY s.id ORDER BY r.time) AS position
FROM rallies ra
JOIN stages s ON s.rally_id = ra.id AND ra.current_stage = s.name
JOIN results r ON r.stage_id = s.id
JOIN entries e ON e.id = r.entry_id
JOIN drivers d ON d.id = e.driver_id
JOIN persons pd ON pd.id = d.id;

-- View: class_classification (ανά κατηγορία)
CREATE VIEW class_classification AS
SELECT
  e.rally_id,
  e.class,
  e.id AS entry_id,
  pd.first_name || ' ' || pd.last_name AS driver,
  SUM(r.time + COALESCE(p.time, 0)) AS total_time,
  RANK() OVER (PARTITION BY e.rally_id, e.class ORDER BY SUM(r.time + COALESCE(p.time, 0))) AS class_position
FROM entries e
JOIN results r ON r.entry_id = e.id
JOIN drivers d ON d.id = e.driver_id
JOIN persons pd ON pd.id = d.id
LEFT JOIN penalties p ON p.entry_id = e.id AND p.stage_id = r.stage_id
GROUP BY e.rally_id, e.class, e.id, pd.first_name, pd.last_name;

-- View: stage_winners
CREATE VIEW stage_winners AS
SELECT
  DISTINCT ON (r.stage_id)
  r.stage_id,
  s.name AS stage_name,
  r.rally_id,
  e.number,
  pd.first_name || ' ' || pd.last_name AS driver,
  r.time
FROM results r
JOIN entries e ON e.id = r.entry_id
JOIN drivers d ON d.id = e.driver_id
JOIN persons pd ON pd.id = d.id
JOIN stages s ON s.id = r.stage_id
ORDER BY r.stage_id, r.time ASC;

-- View: driver_stats (νίκες, podiums, συμμετοχές)
CREATE VIEW driver_stats AS
SELECT
  d.id AS driver_id,
  pd.first_name || ' ' || pd.last_name AS driver,
  COUNT(DISTINCT e.rally_id) AS rallies_participated,
  COUNT(CASE WHEN oc.position = 1 THEN 1 END) AS wins,
  COUNT(CASE WHEN oc.position <= 3 THEN 1 END) AS podiums
FROM drivers d
JOIN persons pd ON pd.id = d.id
JOIN entries e ON e.driver_id = d.id
JOIN overall_classification oc ON oc.entry_id = e.id
GROUP BY d.id, pd.first_name, pd.last_name;

-- View: stage_win_count
CREATE VIEW stage_win_count AS
SELECT
  d.id AS driver_id,
  pd.first_name || ' ' || pd.last_name AS driver,
  COUNT(sw.stage_id) AS stage_wins
FROM drivers d
JOIN persons pd ON pd.id = d.id
JOIN entries e ON e.driver_id = d.id
JOIN stage_winners sw ON sw.number = e.number AND sw.driver = pd.first_name || ' ' || pd.last_name
GROUP BY d.id, pd.first_name, pd.last_name;

-- View: championship_standings
CREATE VIEW championship_standings AS
SELECT
  ce.championship_id,
  e.driver_id,
  pd.first_name || ' ' || pd.last_name AS driver,
  SUM(COALESCE(ps.points, 0)) AS total_points,
  RANK() OVER (PARTITION BY ce.championship_id ORDER BY SUM(COALESCE(ps.points, 0)) DESC) AS position
FROM championship_events ce
JOIN rallies r ON r.id = ce.rally_id
JOIN power_stage_points ps ON ps.rally_id = r.id
JOIN entries e ON e.id = ps.entry_id
JOIN drivers d ON d.id = e.driver_id
JOIN persons pd ON pd.id = d.id
GROUP BY ce.championship_id, e.driver_id, pd.first_name, pd.last_name;
