{"resources": {"includes": [{"pattern": "\\QMETA-INF/maven/org.xerial/sqlite-jdbc/pom.properties\\E"}, {"pattern": "\\QMETA-INF/services/ch.qos.logback.classic.spi.Configurator\\E"}, {"pattern": "\\QMETA-INF/services/io.opentelemetry.context.ContextStorageProvider\\E"}, {"pattern": "\\QMETA-INF/services/io.opentelemetry.exporter.internal.grpc.GrpcSenderProvider\\E"}, {"pattern": "\\QMETA-INF/services/io.opentelemetry.sdk.autoconfigure.spi.AutoConfigurationCustomizerProvider\\E"}, {"pattern": "\\QMETA-INF/services/io.opentelemetry.sdk.autoconfigure.spi.ConfigurablePropagatorProvider\\E"}, {"pattern": "\\QMETA-INF/services/io.opentelemetry.sdk.autoconfigure.spi.ResourceProvider\\E"}, {"pattern": "\\QMETA-INF/services/io.opentelemetry.sdk.autoconfigure.spi.logs.ConfigurableLogRecordExporterProvider\\E"}, {"pattern": "\\QMETA-INF/services/io.opentelemetry.sdk.autoconfigure.spi.metrics.ConfigurableMetricExporterProvider\\E"}, {"pattern": "\\QMETA-INF/services/io.opentelemetry.sdk.autoconfigure.spi.traces.ConfigurableSamplerProvider\\E"}, {"pattern": "\\QMETA-INF/services/io.opentelemetry.sdk.autoconfigure.spi.traces.ConfigurableSpanExporterProvider\\E"}, {"pattern": "\\QMETA-INF/services/io.opentelemetry.sdk.autoconfigure.spi.traces.SdkTracerProviderConfigurer\\E"}, {"pattern": "\\QMETA-INF/services/java.lang.System$LoggerFinder\\E"}, {"pattern": "\\QMETA-INF/services/java.net.spi.InetAddressResolverProvider\\E"}, {"pattern": "\\QMETA-INF/services/java.net.spi.URLStreamHandlerProvider\\E"}, {"pattern": "\\QMETA-INF/services/java.nio.channels.spi.AsynchronousChannelProvider\\E"}, {"pattern": "\\QMETA-INF/services/java.nio.channels.spi.SelectorProvider\\E"}, {"pattern": "\\QMETA-INF/services/java.nio.charset.spi.CharsetProvider\\E"}, {"pattern": "\\QMETA-INF/services/java.sql.Driver\\E"}, {"pattern": "\\QMETA-INF/services/java.time.zone.ZoneRulesProvider\\E"}, {"pattern": "\\QMETA-INF/services/javax.xml.parsers.SAXParserFactory\\E"}, {"pattern": "\\QMETA-INF/services/org.flywaydb.core.extensibility.Plugin\\E"}, {"pattern": "\\QMETA-INF/services/org.slf4j.spi.SLF4JServiceProvider\\E"}, {"pattern": "\\Qdb/V001__create_initial_tables.sql\\E"}, {"pattern": "\\Qdb/V002__add_penalty_columns.sql\\E"}, {"pattern": "\\Qdb/V003__add_rally_info_columns.sql\\E"}, {"pattern": "\\Qdb/V004__convert_championship_to_array.sql\\E"}, {"pattern": "\\Qdb/V005__convert_group_to_array.sql\\E"}, {"pattern": "\\Qdb/callback\\E"}, {"pattern": "\\Qdb\\E"}, {"pattern": "\\Qlogback-test.scmo\\E"}, {"pattern": "\\Qlogback-test.xml\\E"}, {"pattern": "\\Qlogback.scmo\\E"}, {"pattern": "\\Qlogback.xml\\E"}, {"pattern": "\\Qorg/flywaydb/core/internal/version.txt\\E"}, {"pattern": "\\Qorg/jsoup/nodes/entities-base.properties\\E"}, {"pattern": "\\Qorg/jsoup/nodes/entities-full.properties\\E"}, {"pattern": "\\Qorg/sqlite/native/Mac/aarch64/libsqlitejdbc.dylib\\E"}, {"pattern": "\\Qpressauto2023.csv\\E"}, {"pattern": "\\Qpressauto2024.csv\\E"}, {"pattern": "\\Qsqlite-jdbc.properties\\E"}, {"pattern": "java.base:\\Qjdk/internal/icu/impl/data/icudt72b/nfc.nrm\\E"}, {"pattern": "java.base:\\Qjdk/internal/icu/impl/data/icudt72b/nfkc.nrm\\E"}, {"pattern": "java.base:\\Qjdk/internal/icu/impl/data/icudt72b/uprops.icu\\E"}, {"pattern": "java.base:\\Qsun/net/idn/uidna.spp\\E"}, {"pattern": "jdk.jfr:\\Qjdk/jfr/internal/query/view.ini\\E"}]}, "bundles": []}