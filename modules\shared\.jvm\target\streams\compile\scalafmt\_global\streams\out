[0m[[0m[0mdebug[0m] [0m[0mscalafmt: Adding repositories [https://repo1.maven.org/maven2/][0m
[0m[[0m[0mdebug[0m] [0m[0mscalafmt: Adding credentials [][0m
[0m[[0m[0mdebug[0m] [0m[0mscalafmt: parsed config (v3.9.3): C:\Users\<USER>\Documents\Projects\scalaproject\.scalafmt.conf[0m
[0m[[0m[0mdebug[0m] [0m[0mscalafmt: considering all files (no git)[0m
[0m[[0m[0mdebug[0m] [0m[0mscalafmt: Change report:[0m
[0m[[0m[0mdebug[0m] [0m[0m	Checked: C:\Users\<USER>\Documents\Projects\scalaproject\modules\shared\src\main\scala\borer.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\shared\src\main\scala\iron.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\shared\src\main\scala\Model.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\shared\src\main\scala\Results.scala[0m
[0m[[0m[0mdebug[0m] [0m[0m	Modified: C:\Users\<USER>\Documents\Projects\scalaproject\modules\shared\src\main\scala\borer.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\shared\src\main\scala\iron.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\shared\src\main\scala\Model.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\shared\src\main\scala\Results.scala[0m
[0m[[0m[0mdebug[0m] [0m[0m	Unmodified: [0m
[0m[[0m[0mdebug[0m] [0m[0m	Added: C:\Users\<USER>\Documents\Projects\scalaproject\modules\shared\src\main\scala\borer.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\shared\src\main\scala\iron.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\shared\src\main\scala\Model.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\shared\src\main\scala\Results.scala[0m
[0m[[0m[0mdebug[0m] [0m[0m	Removed: [0m
[0m[[0m[0minfo[0m] [0m[0mscalafmt: Formatting 4 Scala sources (C:\Users\<USER>\Documents\Projects\scalaproject\modules\shared\.jvm)...[0m
[0m[[0m[0mdebug[0m] [0m[0mscalafmt: Unchanged 4 Scala sources[0m
