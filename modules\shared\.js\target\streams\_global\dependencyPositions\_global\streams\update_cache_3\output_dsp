{"{\"organization\":\"org.scala-lang\",\"name\":\"scala3-library_sjs1\",\"revision\":\"3.6.4\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\build.sbt", "startLine": 24}, "type": "LinePosition"}, "{\"organization\":\"io.bullet\",\"name\":\"borer-derivation\",\"revision\":\"1.16.1\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"sjs1_\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\build.sbt", "startLine": 24}, "type": "LinePosition"}, "{\"organization\":\"com.github.ghik\",\"name\":\"zerowaste\",\"revision\":\"1.0.0\",\"configurations\":\"plugin->default(compile)\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Full\",\"prefix\":\"\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\build.sbt", "startLine": 24}, "type": "LinePosition"}, "{\"organization\":\"org.scala-js\",\"name\":\"scalajs-library_2.13\",\"revision\":\"1.19.0\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Disabled\"}}": {"value": {"$fields": ["path", "startLine"], "path": "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\build.sbt", "startLine": 24}, "type": "LinePosition"}, "{\"organization\":\"org.scala-js\",\"name\":\"scalajs-test-bridge_2.13\",\"revision\":\"1.19.0\",\"configurations\":\"test\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Disabled\"}}": {"value": {"$fields": ["path", "startLine"], "path": "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\build.sbt", "startLine": 24}, "type": "LinePosition"}, "{\"organization\":\"io.github.iltotore\",\"name\":\"iron-borer\",\"revision\":\"3.0.1\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"sjs1_\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\build.sbt", "startLine": 24}, "type": "LinePosition"}, "{\"organization\":\"com.softwaremill.sttp.tapir\",\"name\":\"tapir-core\",\"revision\":\"1.11.29\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"sjs1_\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\build.sbt", "startLine": 24}, "type": "LinePosition"}, "{\"organization\":\"org.scala-lang\",\"name\":\"scala3-library\",\"revision\":\"3.6.4\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "(sbt.Classpaths.jvmBaseSettings) Defaults.scala", "startLine": 3481}, "type": "LinePosition"}, "{\"organization\":\"io.github.iltotore\",\"name\":\"iron\",\"revision\":\"3.0.1\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"sjs1_\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\build.sbt", "startLine": 24}, "type": "LinePosition"}}