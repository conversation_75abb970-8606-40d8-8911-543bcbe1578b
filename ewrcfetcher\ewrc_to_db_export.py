#!/usr/bin/env python3
"""
EWRC to StageTime Database Export Script

Converts EWRC JSON results to SQL format compatible with your StageTime PostgreSQL database schema.
Based on StageTimeSchema.sql with proper UUID handling and PostgreSQL data types.
"""

import json
import sys
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Tuple


class EWRCStageTimeExporter:
    """Export EWRC JSON data to StageTime database format."""

    def __init__(self):
        self.person_cache = {}  # Cache for person UUIDs to avoid duplicates
        self.driver_cache = {}  # Cache for driver UUIDs
        self.codriver_cache = {}  # Cache for codriver UUIDs

    def parse_driver_codriver(self, driver_codriver: str) -> Tuple[str, str, Optional[str], Optional[str]]:
        """Parse driver/codriver string into first/last names."""
        # Handle formats like "Driver Name - Codriver Name" or "Driver Name / Codriver Name"
        separators = [' - ', ' / ', ' – ', ' — ']

        for sep in separators:
            if sep in driver_codriver:
                parts = driver_codriver.split(sep, 1)
                if len(parts) == 2:
                    driver_name = parts[0].strip()
                    codriver_name = parts[1].strip()

                    # Split names into first/last (assume last word is last name)
                    driver_parts = driver_name.split()
                    codriver_parts = codriver_name.split()

                    driver_first = ' '.join(driver_parts[:-1]) if len(driver_parts) > 1 else driver_name
                    driver_last = driver_parts[-1] if len(driver_parts) > 1 else ""

                    codriver_first = ' '.join(codriver_parts[:-1]) if len(codriver_parts) > 1 else codriver_name
                    codriver_last = codriver_parts[-1] if len(codriver_parts) > 1 else ""

                    return driver_first, driver_last, codriver_first, codriver_last

        # No codriver found, treat as driver only
        driver_parts = driver_codriver.split()
        driver_first = ' '.join(driver_parts[:-1]) if len(driver_parts) > 1 else driver_codriver
        driver_last = driver_parts[-1] if len(driver_parts) > 1 else ""

        return driver_first, driver_last, None, None

    def parse_date(self, date_str: str) -> str:
        """Parse date string to ISO format."""
        try:
            # Handle formats like "5.4.2025" or "12.4.2025"
            if '.' in date_str:
                parts = date_str.split('.')
                if len(parts) == 3:
                    day, month, year = parts
                    return f"{year}-{month.zfill(2)}-{day.zfill(2)}"

            # Handle other formats
            return date_str
        except:
            return date_str

    def get_or_create_person_uuid(self, first_name: str, last_name: str, nationality: str) -> str:
        """Get or create UUID for a person."""
        key = f"{first_name}_{last_name}_{nationality}".lower()
        if key not in self.person_cache:
            self.person_cache[key] = str(uuid.uuid4())
        return self.person_cache[key]

    def export_to_sql(self, json_file: str, output_file: str):
        """Export EWRC JSON to StageTime PostgreSQL SQL file."""
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)

        rally_info = data['rally_info']
        results = data['results']

        # Generate all SQL statements
        sql_statements = self._generate_stagetime_sql(rally_info, results)

        # Write to file
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("-- EWRC to StageTime Database Export\n")
            f.write(f"-- Generated: {datetime.now().isoformat()}\n")
            f.write(f"-- Source: {json_file}\n")
            f.write(f"-- Rally: {rally_info['name']}\n\n")

            f.write("BEGIN;\n\n")

            for sql in sql_statements:
                f.write(sql + "\n\n")

            f.write("COMMIT;\n")

        print(f"StageTime SQL export saved to: {output_file}")
        print(f"Rally: {rally_info['name']}")
        print(f"Results: {len(results)} entries")

    def _generate_stagetime_sql(self, rally_info: Dict, results: List[Dict]) -> List[str]:
        """Generate SQL statements for StageTime database schema."""
        sql_statements = []

        # Generate UUIDs
        rally_uuid = str(uuid.uuid4())

        # Parse rally dates
        dates = rally_info.get('dates', '').split(' - ')
        start_date = self.parse_date(dates[0]) if dates else '2025-01-01'
        end_date = self.parse_date(dates[1]) if len(dates) > 1 else start_date

        # 1. Insert Rally
        rally_sql = f"""INSERT INTO rallies (
    id, name, country, start_date, end_date, status, surface, views, created_at, updated_at
) VALUES (
    '{rally_uuid}',
    '{rally_info.get('name', '').replace("'", "''")}',
    'greece',
    '{start_date}T00:00:00Z',
    '{end_date}T23:59:59Z',
    'finished',
    'asphalt',
    0,
    NOW(),
    NOW()
);"""
        sql_statements.append(rally_sql)

        # 2. Process stages and entries
        stages_by_number = {}
        entries_by_driver = {}

        for result in results:
            stage_num = result.get('stage_number', 1)
            stage_name = result.get('stage_name', f'Stage {stage_num}')

            # Create stage if not exists
            if stage_num not in stages_by_number:
                stage_uuid = str(uuid.uuid4())
                stages_by_number[stage_num] = {
                    'uuid': stage_uuid,
                    'name': stage_name
                }

                # Determine stage properties
                is_power_stage = 'power stage' in stage_name.lower()
                is_super_special = 'sss' in stage_name.lower() or 'super special' in stage_name.lower()

                stage_sql = f"""INSERT INTO stages (
    id, rally_id, name, number, length, surface, start_time, status, is_power_stage, is_super_special, created_at, updated_at
) VALUES (
    '{stage_uuid}',
    '{rally_uuid}',
    '{stage_name.replace("'", "''")}',
    {stage_num},
    10.00,
    'asphalt',
    '{start_date}T08:00:00Z',
    'finished',
    {str(is_power_stage).lower()},
    {str(is_super_special).lower()},
    NOW(),
    NOW()
);"""
                sql_statements.append(stage_sql)

            # Process driver/codriver
            driver_codriver = result.get('driver_codriver', '')
            country = result.get('country', 'unknown')

            driver_first, driver_last, codriver_first, codriver_last = self.parse_driver_codriver(driver_codriver)

            # Create entry key
            entry_key = f"{driver_first}_{driver_last}_{country}".lower()

            if entry_key not in entries_by_driver:
                # Create persons, drivers, codrivers, and entry
                driver_uuid = self.get_or_create_person_uuid(driver_first, driver_last, country)

                # Driver person
                driver_person_sql = f"""INSERT INTO persons (
    id, first_name, last_name, nationality, created_at, updated_at
) VALUES (
    '{driver_uuid}',
    '{driver_first.replace("'", "''")}',
    '{driver_last.replace("'", "''")}',
    '{country}',
    NOW(),
    NOW()
) ON CONFLICT (id) DO NOTHING;"""
                sql_statements.append(driver_person_sql)

                # Driver
                driver_sql = f"""INSERT INTO drivers (id) VALUES ('{driver_uuid}') ON CONFLICT (id) DO NOTHING;"""
                sql_statements.append(driver_sql)

                # Codriver (if exists)
                codriver_uuid = None
                if codriver_first and codriver_last:
                    codriver_uuid = self.get_or_create_person_uuid(codriver_first, codriver_last, country)

                    codriver_person_sql = f"""INSERT INTO persons (
    id, first_name, last_name, nationality, created_at, updated_at
) VALUES (
    '{codriver_uuid}',
    '{codriver_first.replace("'", "''")}',
    '{codriver_last.replace("'", "''")}',
    '{country}',
    NOW(),
    NOW()
) ON CONFLICT (id) DO NOTHING;"""
                    sql_statements.append(codriver_person_sql)

                    codriver_sql = f"""INSERT INTO codrivers (id) VALUES ('{codriver_uuid}') ON CONFLICT (id) DO NOTHING;"""
                    sql_statements.append(codriver_sql)
                else:
                    # Use driver as codriver if no codriver specified
                    codriver_uuid = driver_uuid

                # Entry
                entry_uuid = str(uuid.uuid4())
                entry_number = result.get('entry_number', '#0').replace('#', '')
                car = result.get('car', 'Unknown Car')
                group = result.get('group', 'Unknown')

                entry_sql = f"""INSERT INTO entries (
    id, rally_id, driver_id, codriver_id, car, number, class, status, created_at, updated_at
) VALUES (
    '{entry_uuid}',
    '{rally_uuid}',
    '{driver_uuid}',
    '{codriver_uuid}',
    '{car.replace("'", "''")}',
    {entry_number if entry_number.isdigit() else 0},
    '{group.replace("'", "''")}',
    'finished',
    NOW(),
    NOW()
);"""
                sql_statements.append(entry_sql)

                entries_by_driver[entry_key] = entry_uuid

            # 3. Insert Result
            entry_uuid = entries_by_driver[entry_key]
            stage_uuid = stages_by_number[stage_num]['uuid']

            # Convert time from milliseconds to seconds
            time_ms = result.get('stage_time_ms', 0)
            time_seconds = time_ms / 1000.0 if time_ms else 0.0

            result_sql = f"""INSERT INTO results (
    id, rally_id, stage_id, entry_id, time, created_at, updated_at
) VALUES (
    '{str(uuid.uuid4())}',
    '{rally_uuid}',
    '{stage_uuid}',
    '{entry_uuid}',
    {time_seconds:.3f},
    NOW(),
    NOW()
);"""
            sql_statements.append(result_sql)

        return sql_statements

def main():
    """Main function."""
    if len(sys.argv) < 2:
        print("Usage: python ewrc_to_db_export.py <json_file> [output_file]")
        print("Examples:")
        print("  python ewrc_to_db_export.py results.json output.sql")
        print("  python ewrc_to_db_export.py ewrc_complete_results_94276-rally-kentavros-2025.json kentavros.sql")
        sys.exit(1)

    json_file = sys.argv[1]
    output_path = sys.argv[2] if len(sys.argv) > 2 else json_file.replace('.json', '_stagetime.sql')

    exporter = EWRCStageTimeExporter()
    exporter.export_to_sql(json_file, output_path)


if __name__ == "__main__":
    main()
