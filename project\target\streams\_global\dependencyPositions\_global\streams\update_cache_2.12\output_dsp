{"{\"organization\":\"org.scala-lang\",\"name\":\"scala-library\",\"revision\":\"2.12.20\",\"configurations\":\"provided\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Disabled\"}}": {"value": {"$fields": ["path", "range"], "path": "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\project\\plugins.sbt", "range": {"$fields": ["start", "end"], "start": 12, "end": 13}}, "type": "RangePosition"}, "{\"organization\":\"org.scalablytyped.converter\",\"name\":\"sbt-converter\",\"revision\":\"1.0.0-beta44\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{\"e:sbtVersion\":\"1.0\",\"e:scalaVersion\":\"2.12\"},\"crossVersion\":{\"type\":\"Disabled\"}}": {"value": {"$fields": ["path", "range"], "path": "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\project\\plugins.sbt", "range": {"$fields": ["start", "end"], "start": 12, "end": 13}}, "type": "RangePosition"}, "{\"organization\":\"org.scalameta\",\"name\":\"sbt-scalafmt\",\"revision\":\"2.5.4\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{\"e:sbtVersion\":\"1.0\",\"e:scalaVersion\":\"2.12\"},\"crossVersion\":{\"type\":\"Disabled\"}}": {"value": {"$fields": ["path", "range"], "path": "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\project\\plugins.sbt", "range": {"$fields": ["start", "end"], "start": 12, "end": 13}}, "type": "RangePosition"}, "{\"organization\":\"org.scalameta\",\"name\":\"sbt-native-image\",\"revision\":\"0.3.4\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{\"e:sbtVersion\":\"1.0\",\"e:scalaVersion\":\"2.12\"},\"crossVersion\":{\"type\":\"Disabled\"}}": {"value": {"$fields": ["path", "range"], "path": "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\project\\plugins.sbt", "range": {"$fields": ["start", "end"], "start": 12, "end": 13}}, "type": "RangePosition"}, "{\"organization\":\"nl.gn0s1s\",\"name\":\"sbt-dotenv\",\"revision\":\"3.1.1\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{\"e:sbtVersion\":\"1.0\",\"e:scalaVersion\":\"2.12\"},\"crossVersion\":{\"type\":\"Disabled\"}}": {"value": {"$fields": ["path", "range"], "path": "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\project\\plugins.sbt", "range": {"$fields": ["start", "end"], "start": 12, "end": 13}}, "type": "RangePosition"}, "{\"organization\":\"de.heikoseeberger\",\"name\":\"sbt-header\",\"revision\":\"5.10.0\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{\"e:sbtVersion\":\"1.0\",\"e:scalaVersion\":\"2.12\"},\"crossVersion\":{\"type\":\"Disabled\"}}": {"value": {"$fields": ["path", "range"], "path": "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\project\\plugins.sbt", "range": {"$fields": ["start", "end"], "start": 12, "end": 13}}, "type": "RangePosition"}, "{\"organization\":\"org.scala-js\",\"name\":\"sbt-scalajs\",\"revision\":\"1.19.0\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{\"e:sbtVersion\":\"1.0\",\"e:scalaVersion\":\"2.12\"},\"crossVersion\":{\"type\":\"Disabled\"}}": {"value": {"$fields": ["path", "range"], "path": "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\project\\plugins.sbt", "range": {"$fields": ["start", "end"], "start": 12, "end": 13}}, "type": "RangePosition"}, "{\"organization\":\"org.portable-scala\",\"name\":\"sbt-scalajs-crossproject\",\"revision\":\"1.3.2\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{\"e:sbtVersion\":\"1.0\",\"e:scalaVersion\":\"2.12\"},\"crossVersion\":{\"type\":\"Disabled\"}}": {"value": {"$fields": ["path", "range"], "path": "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\project\\plugins.sbt", "range": {"$fields": ["start", "end"], "start": 12, "end": 13}}, "type": "RangePosition"}, "{\"organization\":\"io.spray\",\"name\":\"sbt-revolver\",\"revision\":\"0.10.0\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{\"e:sbtVersion\":\"1.0\",\"e:scalaVersion\":\"2.12\"},\"crossVersion\":{\"type\":\"Disabled\"}}": {"value": {"$fields": ["path", "range"], "path": "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\project\\plugins.sbt", "range": {"$fields": ["start", "end"], "start": 12, "end": 13}}, "type": "RangePosition"}, "{\"organization\":\"se.marcuslonnberg\",\"name\":\"sbt-docker\",\"revision\":\"1.11.0\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{\"e:sbtVersion\":\"1.0\",\"e:scalaVersion\":\"2.12\"},\"crossVersion\":{\"type\":\"Disabled\"}}": {"value": {"$fields": ["path", "range"], "path": "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\project\\plugins.sbt", "range": {"$fields": ["start", "end"], "start": 12, "end": 13}}, "type": "RangePosition"}, "{\"organization\":\"com.github.sbt\",\"name\":\"sbt-dynver\",\"revision\":\"5.1.0\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{\"e:sbtVersion\":\"1.0\",\"e:scalaVersion\":\"2.12\"},\"crossVersion\":{\"type\":\"Disabled\"}}": {"value": {"$fields": ["path", "range"], "path": "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\project\\plugins.sbt", "range": {"$fields": ["start", "end"], "start": 12, "end": 13}}, "type": "RangePosition"}, "{\"organization\":\"com.eed3si9n\",\"name\":\"sbt-buildinfo\",\"revision\":\"0.13.1\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{\"e:sbtVersion\":\"1.0\",\"e:scalaVersion\":\"2.12\"},\"crossVersion\":{\"type\":\"Disabled\"}}": {"value": {"$fields": ["path", "range"], "path": "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\project\\plugins.sbt", "range": {"$fields": ["start", "end"], "start": 12, "end": 13}}, "type": "RangePosition"}, "{\"organization\":\"org.typelevel\",\"name\":\"sbt-tpolecat\",\"revision\":\"0.5.2\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{\"e:sbtVersion\":\"1.0\",\"e:scalaVersion\":\"2.12\"},\"crossVersion\":{\"type\":\"Disabled\"}}": {"value": {"$fields": ["path", "range"], "path": "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\project\\plugins.sbt", "range": {"$fields": ["start", "end"], "start": 12, "end": 13}}, "type": "RangePosition"}}