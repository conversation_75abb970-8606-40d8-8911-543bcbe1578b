package typings.tailwindcssPostcss

import org.scalablytyped.runtime.Shortcut
import typings.postcss.mod.PluginCreator
import typings.tailwindcssPostcss.anon.Minify
import org.scalablytyped.runtime.StObject
import scala.scalajs.js
import scala.scalajs.js.annotation.{JSGlobalScope, JSGlobal, JSImport, JSName, JSBracketAccess}

object distMod extends Shortcut {
  
  @JSImport("@tailwindcss/postcss/dist", JSImport.Namespace)
  @js.native
  val ^ : PluginCreator[PluginOptions] = js.native
  
  trait PluginOptions extends StObject {
    
    var base: js.UndefOr[String] = js.undefined
    
    var optimize: js.UndefOr[Boolean | Minify] = js.undefined
  }
  object PluginOptions {
    
    inline def apply(): PluginOptions = {
      val __obj = js.Dynamic.literal()
      __obj.asInstanceOf[PluginOptions]
    }
    
    @scala.inline
    implicit open class MutableBuilder[Self <: PluginOptions] (val x: Self) extends AnyVal {
      
      inline def setBase(value: String): Self = StObject.set(x, "base", value.asInstanceOf[js.Any])
      
      inline def setBaseUndefined: Self = StObject.set(x, "base", js.undefined)
      
      inline def setOptimize(value: Boolean | Minify): Self = StObject.set(x, "optimize", value.asInstanceOf[js.Any])
      
      inline def setOptimizeUndefined: Self = StObject.set(x, "optimize", js.undefined)
    }
  }
  
  type _To = PluginCreator[PluginOptions]
  
  /* This means you don't have to write `^`, but can instead just say `distMod.foo` */
  override def _to: PluginCreator[PluginOptions] = ^
}
