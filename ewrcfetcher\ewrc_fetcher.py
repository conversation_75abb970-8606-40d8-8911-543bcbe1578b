#!/usr/bin/env python3
"""
EWRC Results Fetcher

A Python script to fetch rally results from ewrc-results.com
Based on the RallyEye Scala implementation.

Usage:
    python ewrc_fetcher.py <rally_id>

Example:
    python ewrc_fetcher.py "2024/wrc/monte-carlo"
"""

import requests
import re
import json
import sys
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict
from bs4 import BeautifulSoup


@dataclass
class RallyInfo:
    name: str
    championship: List[str]
    start_date: str
    end_date: str
    distance_meters: int
    total_entries: int
    finished: int


@dataclass
class Entry:
    stage_number: int
    stage_name: str
    country: str
    driver_codriver: str
    entry_number: str
    group: List[str]
    car: str
    stage_time_ms: Optional[int]
    overall_time_ms: Optional[int]
    penalties_ms: int
    super_rally: bool
    active: bool
    nominal_time: bool
    comments: Optional[str] = None


class EWRCFetcher:
    BASE_URL = "https://www.ewrc-results.com"

    def __init__(self, debug=False):
        self.debug = debug
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })

    def fetch_rally_info(self, rally_id: str) -> RallyInfo:
        """Fetch basic rally information from the final results page."""
        url = f"{self.BASE_URL}/final/{rally_id}/"
        response = self.session.get(url)
        response.raise_for_status()

        return self._parse_rally_info(response.text)

    def fetch_rally_results(self, rally_id: str) -> List[Entry]:
        """Fetch all rally results including stage-by-stage data."""
        # First get the final page to extract stage IDs
        final_url = f"{self.BASE_URL}/final/{rally_id}/"
        final_response = self.session.get(final_url)
        final_response.raise_for_status()

        # Parse retired drivers info
        retired_drivers = self._parse_retired_drivers(final_response.text)

        # Extract stage IDs
        stage_ids = self._parse_stage_ids(final_response.text, rally_id)

        if not stage_ids:
            print("No stages found, trying to fetch from main results page...")
            # Try the main results page instead
            results_url = f"{self.BASE_URL}/results/{rally_id}/"
            try:
                results_response = self.session.get(results_url)
                results_response.raise_for_status()
                stage_ids = self._parse_stage_ids(results_response.text, rally_id)
            except Exception as e:
                print(f"Could not fetch main results page: {e}")

        # Fetch results for each stage
        all_entries = []
        for stage_id in stage_ids:
            try:
                stage_entries = self._fetch_stage_results(rally_id, stage_id, retired_drivers)
                all_entries.extend(stage_entries)
            except Exception as e:
                print(f"Warning: Could not fetch stage {stage_id}: {e}")
                continue

        return all_entries

    def _parse_rally_info(self, html: str) -> RallyInfo:
        """Parse rally information from the final results page."""
        soup = BeautifulSoup(html, 'html.parser')

        # Extract rally name
        h3_element = soup.select_one("main#main-section h3")
        if not h3_element:
            raise ValueError("Could not find rally name")

        rally_name_text = h3_element.get_text().strip()
        name_match = re.match(r'(\d+)\.\s*(.*)', rally_name_text)
        name = name_match.group(2).strip() if name_match else rally_name_text

        # Extract top info (dates, distance)
        top_info = soup.select_one("main#main-section div.top-info")
        if not top_info:
            raise ValueError("Could not find top info section")

        top_info_text = top_info.get_text()
        top_info_parts = [part.strip() for part in top_info_text.split('•')]

        # Parse dates - try multiple approaches
        date_part = None

        # First try: look for date with dash and month names
        date_part = next((part for part in top_info_parts if '-' in part and any(month in part.lower() for month in ['jan', 'feb', 'mar', 'apr', 'may', 'jun', 'jul', 'aug', 'sep', 'oct', 'nov', 'dec'])), None)

        # Second try: look for any part with month names
        if not date_part:
            date_part = next((part for part in top_info_parts if any(month in part.lower() for month in ['jan', 'feb', 'mar', 'apr', 'may', 'jun', 'jul', 'aug', 'sep', 'oct', 'nov', 'dec'])), None)

        # Third try: look for date patterns with numbers
        if not date_part:
            date_part = next((part for part in top_info_parts if re.search(r'\d{1,2}[-/]\d{1,2}[-/]\d{4}', part)), None)

        # Fourth try: look for year patterns
        if not date_part:
            date_part = next((part for part in top_info_parts if re.search(r'\d{4}', part)), None)

        if not date_part:
            print(f"Warning: Could not find date information. Available parts: {top_info_parts}")
            start_date, end_date = "Unknown", "Unknown"
        else:
            start_date, end_date = self._parse_dates(date_part)

        # Parse distance
        distance_part = next((part for part in top_info_parts if 'km' in part), None)
        distance_meters = 0

        if distance_part:
            # Try different distance patterns
            distance_text = distance_part.split('cancelled')[0]

            # Pattern 1: "123.45 km"
            distance_match = re.search(r'(\d+)\.(\d+)\s*km', distance_text)
            if distance_match:
                distance_meters = int(distance_match.group(1)) * 1000 + int(distance_match.group(2)) * 100
            else:
                # Pattern 2: "123 km"
                distance_match = re.search(r'(\d+)\s*km', distance_text)
                if distance_match:
                    distance_meters = int(distance_match.group(1)) * 1000
                else:
                    print(f"Warning: Could not parse distance from: {distance_part}")
        else:
            print(f"Warning: Could not find distance information. Available parts: {top_info_parts}")

        # Parse championship
        top_sections = soup.select_one("main#main-section div.top-sections")
        championship = []
        if top_sections:
            championship_text = top_sections.get_text()
            championship = [part.split('#')[0].strip() for part in championship_text.split('•')]

        # Parse finished count
        finished_elements = soup.select("main#main-section div.text-center.text-primary.font-weight-bold")
        finished = 0
        for element in finished_elements:
            if 'finished' in element.get_text():
                finished_match = re.search(r'finished: (\d+)', element.get_text())
                if finished_match:
                    finished = int(finished_match.group(1))
                break

        # Parse retirements count
        retirements = 0
        retirement_elements = soup.select("main#main-section div.final-results table.results tbody h4.text-center.mt-3")
        for element in retirement_elements:
            if 'Retirements' in element.get_text():
                sibling = element.find_next_sibling()
                if sibling:
                    retirement_match = re.search(r'(\d+)', sibling.get_text())
                    if retirement_match:
                        retirements = int(retirement_match.group(1))
                break

        total_entries = finished + retirements

        return RallyInfo(
            name=name,
            championship=championship,
            start_date=start_date,
            end_date=end_date,
            distance_meters=distance_meters,
            total_entries=total_entries,
            finished=finished
        )

    def _parse_dates(self, date_str: str) -> Tuple[str, str]:
        """Parse start and end dates from date string."""
        # Handle European format like "5. 4. – 6. 4. 2025"
        euro_range_match = re.search(r'(\d+)\.\s*(\d+)\.\s*–\s*(\d+)\.\s*(\d+)\.\s*(\d+)', date_str)
        if euro_range_match:
            start_day, start_month, end_day, end_month, year = euro_range_match.groups()
            start_date = f"{start_day}.{start_month}.{year}"
            end_date = f"{end_day}.{end_month}.{year}"
            return start_date, end_date

        # Handle various date formats like "15-17 March 2024"
        date_match = re.search(r'(\d+)-(\d+)\s+(\w+)\s+(\d+)', date_str)
        if date_match:
            start_day, end_day, month, year = date_match.groups()
            start_date = f"{start_day} {month} {year}"
            end_date = f"{end_day} {month} {year}"
            return start_date, end_date

        # Handle single date format like "15 March 2024"
        single_date_match = re.search(r'(\d+)\s+(\w+)\s+(\d+)', date_str)
        if single_date_match:
            day, month, year = single_date_match.groups()
            date = f"{day} {month} {year}"
            return date, date

        # Handle numeric date formats like "15/03/2024" or "15-03-2024"
        numeric_date_match = re.search(r'(\d{1,2})[-/](\d{1,2})[-/](\d{4})', date_str)
        if numeric_date_match:
            day, month, year = numeric_date_match.groups()
            date = f"{day}/{month}/{year}"
            return date, date

        # Handle European single date like "5. 4. 2025"
        euro_single_match = re.search(r'(\d+)\.\s*(\d+)\.\s*(\d+)', date_str)
        if euro_single_match:
            day, month, year = euro_single_match.groups()
            date = f"{day}.{month}.{year}"
            return date, date

        # Handle year only
        year_match = re.search(r'(\d{4})', date_str)
        if year_match:
            year = year_match.group(1)
            return year, year

        # If nothing matches, return the original string
        return date_str, date_str

    def _parse_retired_drivers(self, html: str) -> Dict[str, List[str]]:
        """Parse retired drivers and their groups from final page."""
        soup = BeautifulSoup(html, 'html.parser')
        retired_drivers = {}

        retired_elements = soup.select(".final-results-stage")
        for element in retired_elements:
            parent = element.parent
            if parent:
                number_elem = parent.select_one(".final-results-number")
                group_elem = parent.select_one(".final-results-cat")
                special_group_elem = parent.select_one(".startlist-m")

                if number_elem:
                    number = number_elem.get_text().strip()
                    groups = []

                    if group_elem:
                        # Split by <br> tags
                        group_html = str(group_elem)
                        group_parts = re.split(r'<br\s*/?>', group_html)
                        groups.extend([BeautifulSoup(part, 'html.parser').get_text().strip() for part in group_parts if part.strip()])

                    if special_group_elem:
                        special_text = special_group_elem.get_text().strip()
                        if special_text:
                            groups.append(special_text)

                    retired_drivers[number] = groups

        return retired_drivers

    def _parse_stage_ids(self, html: str, rally_id: str) -> List[int]:
        """Extract stage IDs from the final results page."""
        soup = BeautifulSoup(html, 'html.parser')

        # Look for stage links with the specific rally ID
        stage_links = soup.select(f'a[href*="/results/{rally_id}/?s="]')

        stage_ids = []
        stage_info = {}  # Map stage_id -> stage_number for debugging

        for link in stage_links:
            href = link.get('href', '')
            text = link.get_text().strip()

            # Extract the stage ID from the URL parameter
            stage_match = re.search(r's=(\d+)', href)
            if stage_match:
                stage_id = int(stage_match.group(1))
                stage_ids.append(stage_id)

                # Try to extract stage number from link text (SS1, SS2, etc.)
                stage_num_match = re.search(r'SS(\d+)', text)
                if stage_num_match:
                    stage_info[stage_id] = int(stage_num_match.group(1))

        # Remove duplicates while preserving order
        unique_stages = []
        seen = set()
        for stage_id in stage_ids:
            if stage_id not in seen:
                unique_stages.append(stage_id)
                seen.add(stage_id)

        if self.debug:
            print(f"Found {len(unique_stages)} unique stages:")
            for stage_id in unique_stages:
                stage_num = stage_info.get(stage_id, '?')
                print(f"  Stage ID {stage_id} -> SS{stage_num}")
        else:
            print(f"Found {len(unique_stages)} stages: {unique_stages}")

        return unique_stages

    def _fetch_stage_results(self, rally_id: str, stage_id: int, retired_drivers: Dict[str, List[str]]) -> List[Entry]:
        """Fetch results for a specific stage."""
        url = f"{self.BASE_URL}/results/{rally_id}/?s={stage_id}"
        response = self.session.get(url)
        response.raise_for_status()

        return self._parse_stage_results(response.text, retired_drivers)

    def _parse_stage_results(self, html: str, retired_drivers: Dict[str, List[str]]) -> List[Entry]:
        """Parse stage results from HTML."""
        soup = BeautifulSoup(html, 'html.parser')

        # Extract stage number and name
        h5_element = soup.select_one("main#main-section h5")
        if not h5_element:
            raise ValueError("Could not find stage header")

        stage_text = h5_element.get_text()
        stage_match = re.match(r'SS(\d+)\s+([^-]+)(?:\s*-\s*[\d.]+\s*km)?', stage_text.strip())
        if not stage_match:
            raise ValueError(f"Could not parse stage info from: {stage_text}")

        stage_number = int(stage_match.group(1))
        stage_name = stage_match.group(2).strip()

        # Check if stage was cancelled
        stage_cancelled = bool(soup.select("main#main-section div#stage-results span.badge-danger"))

        # Get stage results table
        stage_table_rows = []
        stage_table = soup.select_one("main#main-section div#stage-results table.results")
        if stage_table:
            stage_table_rows = stage_table.select("tr")

        # Get overall results table to filter active drivers
        overall_table_rows = []
        overall_tables = soup.select("main#main-section div#stage-results table.results")
        if len(overall_tables) > 1:
            overall_table_rows = overall_tables[-1].select("tr")

        # Extract entry numbers from overall table
        drivers_in_overall = set()
        for row in overall_table_rows:
            number_elem = row.select_one("td.text-left span.font-weight-bold.text-primary")
            if number_elem:
                drivers_in_overall.add(number_elem.get_text().strip())

        # Parse retired drivers for this stage
        retired_entries = self._parse_retired_entries(soup, stage_number, stage_name, retired_drivers)

        # Parse stage results
        stage_entries = []
        for row in stage_table_rows:
            # Skip header rows
            if not row.select("td"):
                continue

            # Check if driver is still active (in overall table) or if stage was cancelled
            number_elem = row.select_one("td.text-left span.font-weight-bold.text-primary")
            if not number_elem:
                continue

            entry_number = number_elem.get_text().strip()
            if not (entry_number in drivers_in_overall or stage_cancelled):
                continue

            try:
                entry = self._parse_stage_entry(row, stage_number, stage_name, retired_drivers)
                if entry:
                    stage_entries.append(entry)
            except Exception as e:
                print(f"Warning: Could not parse entry {entry_number}: {e}")
                continue

        return retired_entries + stage_entries

    def _parse_retired_entries(self, soup: BeautifulSoup, stage_number: int, stage_name: str, retired_drivers: Dict[str, List[str]]) -> List[Entry]:
        """Parse retired entries from the retirement panel."""
        retired_entries = []

        info_panels = soup.select("main#main-section div.mt-3")
        for panel in info_panels:
            if 'Retirement' in panel.get_text():
                retired_rows = panel.select("tr")
                for row in retired_rows:
                    try:
                        # Extract country from flag
                        country = self._get_country_from_flag(row)

                        # Extract driver/codriver name
                        name_elem = row.select_one("a")
                        driver_codriver = name_elem.get_text().strip() if name_elem else ""

                        # Extract car
                        car_elem = row.select_one("td.retired-car")
                        car = car_elem.get_text().strip() if car_elem else ""

                        # Extract entry number
                        number_elem = row.select_one("td.font-weight-bold.text-primary")
                        entry_number = number_elem.get_text().strip() if number_elem else ""

                        # Get group from retired drivers map
                        group = retired_drivers.get(entry_number, [])

                        retired_entry = Entry(
                            stage_number=stage_number,
                            stage_name=stage_name,
                            country=country,
                            driver_codriver=driver_codriver,
                            entry_number=entry_number,
                            group=group,
                            car=car,
                            stage_time_ms=None,
                            overall_time_ms=None,
                            penalties_ms=0,
                            super_rally=False,
                            active=False,
                            nominal_time=False
                        )
                        retired_entries.append(retired_entry)
                    except Exception as e:
                        print(f"Warning: Could not parse retired entry: {e}")
                        continue

        return retired_entries

    def _parse_stage_entry(self, row, stage_number: int, stage_name: str, retired_drivers: Dict[str, List[str]]) -> Optional[Entry]:
        """Parse a single stage entry from a table row."""
        # Extract country from flag
        country = self._get_country_from_flag(row)

        # Extract driver/codriver name
        name_elem = row.select_one("td.position-relative > a")
        driver_codriver = name_elem.get_text().strip() if name_elem else ""

        # Extract car
        car_elem = row.select_one("td.position-relative > span")
        car = car_elem.get_text().strip() if car_elem else ""

        # Extract entry number
        number_elem = row.select_one("td.text-left span.font-weight-bold.text-primary")
        entry_number = number_elem.get_text().strip() if number_elem else ""

        # Extract group information
        group_elem = row.select_one("td.px-1")
        group = []
        if group_elem:
            # Replace <br> tags with separators for parsing
            group_html = str(group_elem)
            group_html = re.sub(r'<br\s*/?>', '---', group_html)
            group_soup = BeautifulSoup(group_html, 'html.parser')
            group_text = group_soup.get_text()
            group = [g.strip() for g in group_text.split('---') if g.strip()]
            group = [g if g else "No group" for g in group]

        # Extract stage time
        time_elem = row.select_one("td.font-weight-bold.text-right")
        stage_time_ms = None
        nominal_time = False

        if time_elem:
            nominal_time = "[N]" in time_elem.get_text()
            # Remove span elements (like [N] markers)
            for span in time_elem.select("span"):
                span.decompose()
            time_text = time_elem.get_text().strip()
            if time_text and time_text != "-":
                stage_time_ms = self._parse_time_to_ms(time_text)

        # Check for Super Rally
        super_rally = "[SR]" in car

        return Entry(
            stage_number=stage_number,
            stage_name=stage_name,
            country=country,
            driver_codriver=driver_codriver,
            entry_number=entry_number,
            group=group,
            car=car,
            stage_time_ms=stage_time_ms,
            overall_time_ms=None,  # Not available in stage results
            penalties_ms=0,  # Would need additional parsing
            super_rally=super_rally,
            active=True,
            nominal_time=nominal_time
        )

    def _get_country_from_flag(self, row) -> str:
        """Extract country from flag image."""
        flag_img = row.select_one("td img.flag-s")
        if not flag_img:
            return "unknown"

        src = flag_img.get('src', '')
        if not src:
            return "unknown"

        # Extract country code from flag image path
        country_code = src.split('/')[-1].split('.')[0]

        # Map some special cases (based on Scala code)
        country_mapping = {
            'uk': 'united kingdom',
            'saudi_arabia': 'saudi arabia',
            'costa_rica': 'costa rica',
            'nederland': 'netherlands',
            'jar': 'south africa',
            'newzealand': 'new zealand'
        }

        return country_mapping.get(country_code, country_code)

    def _parse_time_to_ms(self, time_str: str) -> int:
        """Parse time string to milliseconds."""
        time_str = time_str.strip()
        if not time_str or time_str == "-":
            return 0

        # Handle different time formats
        parts = time_str.split(':')

        if len(parts) == 3:  # HH:MM:SS.sss
            hours, minutes, seconds_and_tenths = parts
            total_seconds = int(hours) * 3600 + int(minutes) * 60
            ms = self._seconds_to_ms(seconds_and_tenths)
            return total_seconds * 1000 + ms
        elif len(parts) == 2:  # MM:SS.sss
            minutes, seconds_and_tenths = parts
            total_seconds = int(minutes) * 60
            ms = self._seconds_to_ms(seconds_and_tenths)
            return total_seconds * 1000 + ms
        elif len(parts) == 1:  # SS.sss
            return self._seconds_to_ms(parts[0])
        else:
            raise ValueError(f"Unable to parse time: {time_str}")

    def _seconds_to_ms(self, seconds_str: str) -> int:
        """Convert seconds.tenths to milliseconds."""
        if '.' in seconds_str:
            seconds, tenths = seconds_str.split('.')
            return int(seconds) * 1000 + int(tenths.ljust(3, '0')[:3])
        else:
            return int(seconds_str) * 1000


def main():
    """Main function to demonstrate usage."""
    if len(sys.argv) != 2:
        print("Usage: python ewrc_fetcher.py <rally_id>")
        print("Example: python ewrc_fetcher.py '2024/wrc/monte-carlo'")
        sys.exit(1)

    rally_id = sys.argv[1]
    fetcher = EWRCFetcher()

    try:
        print(f"Fetching rally info for: {rally_id}")
        rally_info = fetcher.fetch_rally_info(rally_id)

        print("\n=== RALLY INFO ===")
        print(f"Name: {rally_info.name}")
        print(f"Championship: {', '.join(rally_info.championship)}")
        print(f"Dates: {rally_info.start_date} - {rally_info.end_date}")
        print(f"Distance: {rally_info.distance_meters / 1000:.2f} km")
        print(f"Entries: {rally_info.total_entries} (Finished: {rally_info.finished})")

        print(f"\nFetching detailed results...")
        results = fetcher.fetch_rally_results(rally_id)

        print(f"\n=== RESULTS SUMMARY ===")
        print(f"Total entries: {len(results)}")

        # Group by stage
        stages = {}
        for entry in results:
            stage_key = f"SS{entry.stage_number} {entry.stage_name}"
            if stage_key not in stages:
                stages[stage_key] = []
            stages[stage_key].append(entry)

        print(f"Stages: {len(stages)}")
        for stage_name, entries in stages.items():
            active_count = sum(1 for e in entries if e.active)
            retired_count = len(entries) - active_count
            print(f"  {stage_name}: {active_count} active, {retired_count} retired")

        # Save to JSON file
        output_file = f"ewrc_results_{rally_id.replace('/', '_')}.json"
        output_data = {
            'rally_info': asdict(rally_info),
            'results': [asdict(entry) for entry in results]
        }

        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, indent=2, ensure_ascii=False)

        print(f"\nResults saved to: {output_file}")

    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
