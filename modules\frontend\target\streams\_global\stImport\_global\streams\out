[0m[[0m[0mdebug[0m] [0m[0m[32mScalablyTypedConverterExternalNpmPlugin.scala:45[39m [32minput.asJson.spaces2[39m [92m{[0m
[0m[[0m[0mdebug[0m] [0m[0m  "converterVersion" : "1.0.0-beta44",[0m
[0m[[0m[0mdebug[0m] [0m[0m  "conversion" : {[0m
[0m[[0m[0mdebug[0m] [0m[0m    "useScalaJsDomTypes" : true,[0m
[0m[[0m[0mdebug[0m] [0m[0m    "flavour" : "normal",[0m
[0m[[0m[0mdebug[0m] [0m[0m    "outputPackage" : "typings",[0m
[0m[[0m[0mdebug[0m] [0m[0m    "enableScalaJsDefined" : {[0m
[0m[[0m[0mdebug[0m] [0m[0m      "AllExcept" : {[0m
[0m[[0m[0mdebug[0m] [0m[0m        "values" : [[0m
[0m[[0m[0mdebug[0m] [0m[0m        ][0m
[0m[[0m[0mdebug[0m] [0m[0m      }[0m
[0m[[0m[0mdebug[0m] [0m[0m    },[0m
[0m[[0m[0mdebug[0m] [0m[0m    "stdLibs" : [[0m
[0m[[0m[0mdebug[0m] [0m[0m      "dom",[0m
[0m[[0m[0mdebug[0m] [0m[0m      "es6"[0m
[0m[[0m[0mdebug[0m] [0m[0m    ],[0m
[0m[[0m[0mdebug[0m] [0m[0m    "expandTypeMappings" : {[0m
[0m[[0m[0mdebug[0m] [0m[0m      "AllExcept" : {[0m
[0m[[0m[0mdebug[0m] [0m[0m        "values" : [[0m
[0m[[0m[0mdebug[0m] [0m[0m          "prop-types",[0m
[0m[[0m[0mdebug[0m] [0m[0m          "react",[0m
[0m[[0m[0mdebug[0m] [0m[0m          "std"[0m
[0m[[0m[0mdebug[0m] [0m[0m        ][0m
[0m[[0m[0mdebug[0m] [0m[0m      }[0m
[0m[[0m[0mdebug[0m] [0m[0m    },[0m
[0m[[0m[0mdebug[0m] [0m[0m    "ignored" : [[0m
[0m[[0m[0mdebug[0m] [0m[0m      "typescript"[0m
[0m[[0m[0mdebug[0m] [0m[0m    ],[0m
[0m[[0m[0mdebug[0m] [0m[0m    "versions" : {[0m
[0m[[0m[0mdebug[0m] [0m[0m      "scala" : "3.6.4",[0m
[0m[[0m[0mdebug[0m] [0m[0m      "scalaJs" : "1.19.0"[0m
[0m[[0m[0mdebug[0m] [0m[0m    },[0m
[0m[[0m[0mdebug[0m] [0m[0m    "organization" : "org.scalablytyped",[0m
[0m[[0m[0mdebug[0m] [0m[0m    "enableReactTreeShaking" : {[0m
[0m[[0m[0mdebug[0m] [0m[0m      "NoneExcept" : {[0m
[0m[[0m[0mdebug[0m] [0m[0m        "values" : [[0m
[0m[[0m[0mdebug[0m] [0m[0m        ][0m
[0m[[0m[0mdebug[0m] [0m[0m      }[0m
[0m[[0m[0mdebug[0m] [0m[0m    },[0m
[0m[[0m[0mdebug[0m] [0m[0m    "enableLongApplyMethod" : false,[0m
[0m[[0m[0mdebug[0m] [0m[0m    "privateWithin" : null,[0m
[0m[[0m[0mdebug[0m] [0m[0m    "useDeprecatedModuleNames" : false[0m
[0m[[0m[0mdebug[0m] [0m[0m  },[0m
[0m[[0m[0mdebug[0m] [0m[0m  "wantedLibs" : {[0m
[0m[[0m[0mdebug[0m] [0m[0m    "@tailwindcss/postcss" : "^4.1.7",[0m
[0m[[0m[0mdebug[0m] [0m[0m    "country-emoji" : "^1.5.6",[0m
[0m[[0m[0mdebug[0m] [0m[0m    "country-flag-emoji-polyfill" : "^0.1.8",[0m
[0m[[0m[0mdebug[0m] [0m[0m    "d3-array" : "3.2.4",[0m
[0m[[0m[0mdebug[0m] [0m[0m    "d3-scale" : "4.0.2",[0m
[0m[[0m[0mdebug[0m] [0m[0m    "d3-scale-chromatic" : "3.1.0",[0m
[0m[[0m[0mdebug[0m] [0m[0m    "d3-selection" : "3.0.0",[0m
[0m[[0m[0mdebug[0m] [0m[0m    "d3-shape" : "3.2.0"[0m
[0m[[0m[0mdebug[0m] [0m[0m  }[0m
[0m[[0m[0mdebug[0m] [0m[0m}[39m [32m[project => frontend, ms => 1635][39m[0m
[0m[[0m[0mdebug[0m] [0m[0m[32mScalablyTypedConverterExternalNpmPlugin.scala:46[39m [32minput.packageJsonHash[39m [92md513e24d8993bfbeff028e641614fb3d[39m [32m[project => frontend, ms => 1850][39m[0m
[0m[[0m[0mdebug[0m] [0m[0m[32mScalablyTypedConverterExternalNpmPlugin.scala:47[39m [32mrunCacheKey[39m [92me3e417fa4e3c7b50be7565f25e4280c7[39m [32m[project => frontend, ms => 1863][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(intrinsic)))[39m [34m[thread => 91, project => frontend, ms => 38856, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclTypeAlias(Uppercase)), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify intrinsic[39m [34m[thread => 91, project => frontend, ms => 38865, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclTypeAlias(Uppercase)), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(intrinsic)))[39m [34m[thread => 91, project => frontend, ms => 38869, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclTypeAlias(Lowercase)), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify intrinsic[39m [34m[thread => 91, project => frontend, ms => 38876, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclTypeAlias(Lowercase)), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(intrinsic)))[39m [34m[thread => 91, project => frontend, ms => 38880, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclTypeAlias(Capitalize)), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify intrinsic[39m [34m[thread => 91, project => frontend, ms => 38883, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclTypeAlias(Capitalize)), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(intrinsic)))[39m [34m[thread => 91, project => frontend, ms => 38888, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclTypeAlias(Uncapitalize)), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify intrinsic[39m [34m[thread => 91, project => frontend, ms => 38893, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclTypeAlias(Uncapitalize)), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(intrinsic)))[39m [34m[thread => 91, project => frontend, ms => 38896, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclTypeAlias(NoInfer)), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify intrinsic[39m [34m[thread => 91, project => frontend, ms => 38899, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclTypeAlias(NoInfer)), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(intrinsic)))[39m [34m[thread => 91, project => frontend, ms => 38960, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclTypeAlias(BuiltinIteratorReturn)), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify intrinsic[39m [34m[thread => 91, project => frontend, ms => 38962, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclTypeAlias(BuiltinIteratorReturn)), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 39919, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclInterface(MIDIMessageEventInit) / TsMemberProperty(data) / TsTypeUnion() / TsTypeRef()), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 39926, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclInterface(PeriodicWaveOptions) / TsMemberProperty(imag) / TsTypeUnion() / TsTypeRef()), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 39929, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclInterface(PeriodicWaveOptions) / TsMemberProperty(real) / TsTypeUnion() / TsTypeRef()), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 39940, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclInterface(WaveShaperOptions) / TsMemberProperty(curve) / TsTypeUnion() / TsTypeRef()), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 39945, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclInterface(AnalyserNode) / TsMemberFunction(getByteFrequencyData) / TsFunSig() / TsFunParam() / TsTypeRef()), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 39947, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclInterface(AnalyserNode) / TsMemberFunction(getByteTimeDomainData) / TsFunSig() / TsFunParam() / TsTypeRef()), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 39949, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclInterface(AnalyserNode) / TsMemberFunction(getFloatFrequencyData) / TsFunSig() / TsFunParam() / TsTypeRef()), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 39952, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclInterface(AnalyserNode) / TsMemberFunction(getFloatTimeDomainData) / TsFunSig() / TsFunParam() / TsTypeRef()), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 39957, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclInterface(AudioBuffer) / TsMemberFunction(copyFromChannel) / TsFunSig() / TsFunParam() / TsTypeRef()), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 39960, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclInterface(AudioBuffer) / TsMemberFunction(copyToChannel) / TsFunSig() / TsFunParam() / TsTypeRef()), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 39963, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclInterface(AudioBuffer) / TsMemberFunction(getChannelData) / TsFunSig() / TsTypeRef()), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 39968, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclInterface(AudioParam) / TsMemberFunction(setValueCurveAtTime) / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 39975, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclInterface(BaseAudioContext) / TsMemberFunction(createPeriodicWave) / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 39978, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclInterface(BaseAudioContext) / TsMemberFunction(createPeriodicWave) / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 39981, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclInterface(BiquadFilterNode) / TsMemberFunction(getFrequencyResponse) / TsFunSig() / TsFunParam() / TsTypeRef()), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 39984, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclInterface(BiquadFilterNode) / TsMemberFunction(getFrequencyResponse) / TsFunSig() / TsFunParam() / TsTypeRef()), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 39986, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclInterface(BiquadFilterNode) / TsMemberFunction(getFrequencyResponse) / TsFunSig() / TsFunParam() / TsTypeRef()), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 39989, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclInterface(Blob) / TsMemberFunction(bytes) / TsFunSig() / TsTypeRef() / TsTypeRef()), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 39992, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclInterface(Blob) / TsMemberFunction(stream) / TsFunSig() / TsTypeRef() / TsTypeRef()), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 39996, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclInterface(Body) / TsMemberProperty(body) / TsTypeUnion() / TsTypeRef() / TsTypeRef()), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 39998, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclInterface(Body) / TsMemberFunction(bytes) / TsFunSig() / TsTypeRef() / TsTypeRef()), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 40010, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclInterface(CompressionStream) / TsMemberProperty(readable) / TsTypeRef() / TsTypeRef()), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 40017, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclInterface(DOMMatrixReadOnly) / TsMemberFunction(toFloat32Array) / TsFunSig() / TsTypeRef()), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 40019, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclInterface(DOMMatrixReadOnly) / TsMemberFunction(toFloat64Array) / TsFunSig() / TsTypeRef()), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 40023, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclInterface(DecompressionStream) / TsMemberProperty(readable) / TsTypeRef() / TsTypeRef()), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 40080, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclInterface(IIRFilterNode) / TsMemberFunction(getFrequencyResponse) / TsFunSig() / TsFunParam() / TsTypeRef()), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 40082, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclInterface(IIRFilterNode) / TsMemberFunction(getFrequencyResponse) / TsFunSig() / TsFunParam() / TsTypeRef()), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 40084, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclInterface(IIRFilterNode) / TsMemberFunction(getFrequencyResponse) / TsFunSig() / TsFunParam() / TsTypeRef()), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 40086, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclInterface(ImageData) / TsMemberProperty(data) / TsTypeRef()), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 40089, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclInterface(MIDIMessageEvent) / TsMemberProperty(data) / TsTypeUnion() / TsTypeRef()), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 40154, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclInterface(TextEncoder) / TsMemberFunction(encode) / TsFunSig() / TsTypeRef()), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 40157, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclInterface(TextEncoder) / TsMemberFunction(encodeInto) / TsFunSig() / TsFunParam() / TsTypeRef()), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 40159, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclInterface(TextEncoderStream) / TsMemberProperty(readable) / TsTypeRef() / TsTypeRef()), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 40168, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclInterface(WEBGL_multi_draw) / TsMemberFunction(multiDrawArraysInstancedWEBGL) / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 40170, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclInterface(WEBGL_multi_draw) / TsMemberFunction(multiDrawArraysInstancedWEBGL) / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 40173, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclInterface(WEBGL_multi_draw) / TsMemberFunction(multiDrawArraysInstancedWEBGL) / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 40177, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclInterface(WEBGL_multi_draw) / TsMemberFunction(multiDrawArraysWEBGL) / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 40179, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclInterface(WEBGL_multi_draw) / TsMemberFunction(multiDrawArraysWEBGL) / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 40182, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclInterface(WEBGL_multi_draw) / TsMemberFunction(multiDrawElementsInstancedWEBGL) / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 40185, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclInterface(WEBGL_multi_draw) / TsMemberFunction(multiDrawElementsInstancedWEBGL) / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 40187, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclInterface(WEBGL_multi_draw) / TsMemberFunction(multiDrawElementsInstancedWEBGL) / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 40190, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclInterface(WEBGL_multi_draw) / TsMemberFunction(multiDrawElementsWEBGL) / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 40194, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclInterface(WEBGL_multi_draw) / TsMemberFunction(multiDrawElementsWEBGL) / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 40197, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclInterface(WEBGL_multi_draw) / TsMemberFunction(multiDrawArraysInstancedWEBGL) / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 40200, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclInterface(WEBGL_multi_draw) / TsMemberFunction(multiDrawArraysInstancedWEBGL) / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 40203, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclInterface(WEBGL_multi_draw) / TsMemberFunction(multiDrawArraysInstancedWEBGL) / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 40206, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclInterface(WEBGL_multi_draw) / TsMemberFunction(multiDrawArraysWEBGL) / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 40208, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclInterface(WEBGL_multi_draw) / TsMemberFunction(multiDrawArraysWEBGL) / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 40212, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclInterface(WEBGL_multi_draw) / TsMemberFunction(multiDrawElementsInstancedWEBGL) / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 40215, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclInterface(WEBGL_multi_draw) / TsMemberFunction(multiDrawElementsInstancedWEBGL) / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 40217, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclInterface(WEBGL_multi_draw) / TsMemberFunction(multiDrawElementsInstancedWEBGL) / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 40220, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclInterface(WEBGL_multi_draw) / TsMemberFunction(multiDrawElementsWEBGL) / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 40223, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclInterface(WEBGL_multi_draw) / TsMemberFunction(multiDrawElementsWEBGL) / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 40225, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclInterface(WaveShaperNode) / TsMemberProperty(curve) / TsTypeUnion() / TsTypeRef()), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 40264, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclTypeAlias(BigInteger) / TsTypeRef()), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 40267, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclTypeAlias(Float32List) / TsTypeUnion() / TsTypeRef()), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 40269, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclTypeAlias(Int32List) / TsTypeUnion() / TsTypeRef()), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 40274, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclTypeAlias(Uint32List) / TsTypeUnion() / TsTypeRef()), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 40289, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclNamespace(<global>) / TsDeclVar(DOMMatrix) / TsTypeObject() / TsMemberFunction(fromFloat32Array) / TsFunSig() / TsFunParam() / TsTypeRef()), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 40294, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclNamespace(<global>) / TsDeclVar(DOMMatrix) / TsTypeObject() / TsMemberFunction(fromFloat64Array) / TsFunSig() / TsFunParam() / TsTypeRef()), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 40298, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclNamespace(<global>) / TsDeclVar(SVGMatrix) / TsTypeObject() / TsMemberFunction(fromFloat32Array) / TsFunSig() / TsFunParam() / TsTypeRef()), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 40301, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclNamespace(<global>) / TsDeclVar(SVGMatrix) / TsTypeObject() / TsMemberFunction(fromFloat64Array) / TsFunSig() / TsFunParam() / TsTypeRef()), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 40304, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclNamespace(<global>) / TsDeclVar(WebKitCSSMatrix) / TsTypeObject() / TsMemberFunction(fromFloat32Array) / TsFunSig() / TsFunParam() / TsTypeRef()), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 40307, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclNamespace(<global>) / TsDeclVar(WebKitCSSMatrix) / TsTypeObject() / TsMemberFunction(fromFloat64Array) / TsFunSig() / TsFunParam() / TsTypeRef()), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 40310, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclNamespace(<global>) / TsDeclVar(DOMMatrixReadOnly) / TsTypeObject() / TsMemberFunction(fromFloat32Array) / TsFunSig() / TsFunParam() / TsTypeRef()), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 40314, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclNamespace(<global>) / TsDeclVar(DOMMatrixReadOnly) / TsTypeObject() / TsMemberFunction(fromFloat64Array) / TsFunSig() / TsFunParam() / TsTypeRef()), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 40323, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclNamespace(<global>) / TsDeclVar(ImageData) / TsTypeObject() / TsMemberCtor() / TsFunSig() / TsFunParam() / TsTypeRef()), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 40330, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclNamespace(<global>) / TsDeclVar(ReadableStream) / TsTypeObject() / TsMemberCtor() / TsFunSig() / TsTypeRef() / TsTypeRef()), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 40333, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclNamespace(<global>) / TsDeclVar(ReadableStreamBYOBReader) / TsTypeObject() / TsMemberCtor() / TsFunSig() / TsFunParam() / TsTypeRef() / TsTypeRef()), id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mPhase1ReadTypescript.scala:140[39m  [94mCouldn't resolve inferred dependency node[39m [34m[thread => 91, project => frontend, ms => 47468, phase => typescript, id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mPhase1ReadTypescript.scala:250[39m  [94mCould not find typescript definitions for dependency graceful-fs[39m [34m[thread => 91, project => frontend, ms => 47476, phase => typescript, id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mReplaceExports.scala:55[39m  [94mDropping unexpected export in namespace TsExport(NoComments,false,Named,Names(IArray((TsQIdent(IArray(TsIdentSimple(CachedInputFileSystem))),None), (TsQIdent(IArray(TsIdentSimple(CloneBasenamePlugin))),None), (TsQIdent(IArray(TsIdentSimple(LogInfoPlugin))),None), (TsQIdent(IArray(TsIdentSimple(ResolveOptionsOptionalFS))),None), (TsQIdent(IArray(TsIdentSimple(PnpApi))),None), (TsQIdent(IArray(TsIdentSimple(Resolver))),None), (TsQIdent(IArray(TsIdentSimple(FileSystem))),None), (TsQIdent(IArray(TsIdentSimple(ResolveContext))),None), (TsQIdent(IArray(TsIdentSimple(ResolveRequest))),None), (TsQIdent(IArray(TsIdentSimple(Plugin))),None), (TsQIdent(IArray(TsIdentSimple(ResolveOptionsResolverFactoryObject_2))),Some(TsIdentSimple(ResolveOptions))), (TsQIdent(IArray(TsIdentSimple(ResolveFunctionAsync))),None), (TsQIdent(IArray(TsIdentSimple(ResolveFunction))),None)),None))[39m [34m[thread => 91, project => frontend, ms => 47612, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclNamespace(exports)), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(NodeJS), TsIdentSimple(ErrnoException)))[39m [34m[thread => 91, project => frontend, ms => 47717, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclClass(CachedInputFileSystem) / TsMemberProperty(readJson) / TsTypeUnion() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify NodeJS.ErrnoException[39m [34m[thread => 91, project => frontend, ms => 47721, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclClass(CachedInputFileSystem) / TsMemberProperty(readJson) / TsTypeUnion() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Buffer)))[39m [34m[thread => 91, project => frontend, ms => 47729, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclClass(CachedInputFileSystem) / TsMemberFunction(purge) / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Buffer[39m [34m[thread => 91, project => frontend, ms => 47732, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclClass(CachedInputFileSystem) / TsMemberFunction(purge) / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Buffer)))[39m [34m[thread => 91, project => frontend, ms => 47741, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclClass(CachedInputFileSystem) / TsMemberFunction(purge) / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Buffer[39m [34m[thread => 91, project => frontend, ms => 47745, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclClass(CachedInputFileSystem) / TsMemberFunction(purge) / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Buffer)))[39m [34m[thread => 91, project => frontend, ms => 47753, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclClass(CachedInputFileSystem) / TsMemberFunction(purge) / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Buffer[39m [34m[thread => 91, project => frontend, ms => 47757, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclClass(CachedInputFileSystem) / TsMemberFunction(purge) / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(NodeJS), TsIdentSimple(ErrnoException)))[39m [34m[thread => 91, project => frontend, ms => 47774, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(FileSystem) / TsMemberProperty(readJson) / TsTypeUnion() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify NodeJS.ErrnoException[39m [34m[thread => 91, project => frontend, ms => 47777, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(FileSystem) / TsMemberProperty(readJson) / TsTypeUnion() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(NodeJS), TsIdentSimple(ErrnoException)))[39m [34m[thread => 91, project => frontend, ms => 47785, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(LStat) / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify NodeJS.ErrnoException[39m [34m[thread => 91, project => frontend, ms => 47790, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(LStat) / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(NodeJS), TsIdentSimple(ErrnoException)))[39m [34m[thread => 91, project => frontend, ms => 47794, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(LStat) / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify NodeJS.ErrnoException[39m [34m[thread => 91, project => frontend, ms => 47797, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(LStat) / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(NodeJS), TsIdentSimple(ErrnoException)))[39m [34m[thread => 91, project => frontend, ms => 47808, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(LStat) / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify NodeJS.ErrnoException[39m [34m[thread => 91, project => frontend, ms => 47814, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(LStat) / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(NodeJS), TsIdentSimple(ErrnoException)))[39m [34m[thread => 91, project => frontend, ms => 47820, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(LStat) / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify NodeJS.ErrnoException[39m [34m[thread => 91, project => frontend, ms => 47825, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(LStat) / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Buffer)))[39m [34m[thread => 91, project => frontend, ms => 47832, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclTypeAlias(PathLike) / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Buffer[39m [34m[thread => 91, project => frontend, ms => 47836, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclTypeAlias(PathLike) / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Buffer)))[39m [34m[thread => 91, project => frontend, ms => 47840, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclTypeAlias(PathOrFileDescriptor) / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Buffer[39m [34m[thread => 91, project => frontend, ms => 47843, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclTypeAlias(PathOrFileDescriptor) / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(NodeJS), TsIdentSimple(ErrnoException)))[39m [34m[thread => 91, project => frontend, ms => 47847, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(ReadFile) / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify NodeJS.ErrnoException[39m [34m[thread => 91, project => frontend, ms => 47850, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(ReadFile) / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Buffer)))[39m [34m[thread => 91, project => frontend, ms => 47855, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(ReadFile) / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Buffer[39m [34m[thread => 91, project => frontend, ms => 47857, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(ReadFile) / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(NodeJS), TsIdentSimple(ErrnoException)))[39m [34m[thread => 91, project => frontend, ms => 47860, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(ReadFile) / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify NodeJS.ErrnoException[39m [34m[thread => 91, project => frontend, ms => 47862, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(ReadFile) / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(NodeJS), TsIdentSimple(ErrnoException)))[39m [34m[thread => 91, project => frontend, ms => 47866, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(ReadFile) / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify NodeJS.ErrnoException[39m [34m[thread => 91, project => frontend, ms => 47868, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(ReadFile) / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Buffer)))[39m [34m[thread => 91, project => frontend, ms => 47873, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(ReadFile) / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Buffer[39m [34m[thread => 91, project => frontend, ms => 47876, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(ReadFile) / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(NodeJS), TsIdentSimple(ErrnoException)))[39m [34m[thread => 91, project => frontend, ms => 47879, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(ReadFile) / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify NodeJS.ErrnoException[39m [34m[thread => 91, project => frontend, ms => 47880, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(ReadFile) / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Buffer)))[39m [34m[thread => 91, project => frontend, ms => 47883, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(ReadFile) / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Buffer[39m [34m[thread => 91, project => frontend, ms => 47885, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(ReadFile) / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Buffer)))[39m [34m[thread => 91, project => frontend, ms => 47889, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(ReadFileSync) / TsMemberCall() / TsFunSig() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Buffer[39m [34m[thread => 91, project => frontend, ms => 47892, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(ReadFileSync) / TsMemberCall() / TsFunSig() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Buffer)))[39m [34m[thread => 91, project => frontend, ms => 47897, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(ReadFileSync) / TsMemberCall() / TsFunSig() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Buffer[39m [34m[thread => 91, project => frontend, ms => 47899, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(ReadFileSync) / TsMemberCall() / TsFunSig() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(NodeJS), TsIdentSimple(ErrnoException)))[39m [34m[thread => 91, project => frontend, ms => 47902, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(Readdir) / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify NodeJS.ErrnoException[39m [34m[thread => 91, project => frontend, ms => 47905, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(Readdir) / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(NodeJS), TsIdentSimple(ErrnoException)))[39m [34m[thread => 91, project => frontend, ms => 47908, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(Readdir) / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify NodeJS.ErrnoException[39m [34m[thread => 91, project => frontend, ms => 47910, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(Readdir) / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Buffer)))[39m [34m[thread => 91, project => frontend, ms => 47916, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(Readdir) / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Buffer[39m [34m[thread => 91, project => frontend, ms => 47919, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(Readdir) / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(NodeJS), TsIdentSimple(ErrnoException)))[39m [34m[thread => 91, project => frontend, ms => 47923, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(Readdir) / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify NodeJS.ErrnoException[39m [34m[thread => 91, project => frontend, ms => 47925, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(Readdir) / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(NodeJS), TsIdentSimple(ErrnoException)))[39m [34m[thread => 91, project => frontend, ms => 47928, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(Readdir) / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify NodeJS.ErrnoException[39m [34m[thread => 91, project => frontend, ms => 47930, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(Readdir) / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Buffer)))[39m [34m[thread => 91, project => frontend, ms => 47935, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(Readdir) / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Buffer[39m [34m[thread => 91, project => frontend, ms => 47937, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(Readdir) / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(NodeJS), TsIdentSimple(ErrnoException)))[39m [34m[thread => 91, project => frontend, ms => 47941, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(Readdir) / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify NodeJS.ErrnoException[39m [34m[thread => 91, project => frontend, ms => 47943, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(Readdir) / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Buffer)))[39m [34m[thread => 91, project => frontend, ms => 47947, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(ReaddirSync) / TsMemberCall() / TsFunSig() / TsTypeRef() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Buffer[39m [34m[thread => 91, project => frontend, ms => 47948, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(ReaddirSync) / TsMemberCall() / TsFunSig() / TsTypeRef() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Buffer)))[39m [34m[thread => 91, project => frontend, ms => 47953, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(ReaddirSync) / TsMemberCall() / TsFunSig() / TsTypeUnion() / TsTypeRef() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Buffer[39m [34m[thread => 91, project => frontend, ms => 47956, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(ReaddirSync) / TsMemberCall() / TsFunSig() / TsTypeUnion() / TsTypeRef() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(NodeJS), TsIdentSimple(ErrnoException)))[39m [34m[thread => 91, project => frontend, ms => 47959, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(Readlink) / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify NodeJS.ErrnoException[39m [34m[thread => 91, project => frontend, ms => 47961, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(Readlink) / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(NodeJS), TsIdentSimple(ErrnoException)))[39m [34m[thread => 91, project => frontend, ms => 47964, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(Readlink) / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify NodeJS.ErrnoException[39m [34m[thread => 91, project => frontend, ms => 47966, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(Readlink) / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Buffer)))[39m [34m[thread => 91, project => frontend, ms => 47969, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(Readlink) / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Buffer[39m [34m[thread => 91, project => frontend, ms => 47977, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(Readlink) / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(NodeJS), TsIdentSimple(ErrnoException)))[39m [34m[thread => 91, project => frontend, ms => 47983, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(Readlink) / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify NodeJS.ErrnoException[39m [34m[thread => 91, project => frontend, ms => 47988, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(Readlink) / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Buffer)))[39m [34m[thread => 91, project => frontend, ms => 47992, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(Readlink) / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Buffer[39m [34m[thread => 91, project => frontend, ms => 47995, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(Readlink) / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(NodeJS), TsIdentSimple(ErrnoException)))[39m [34m[thread => 91, project => frontend, ms => 48004, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(Readlink) / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify NodeJS.ErrnoException[39m [34m[thread => 91, project => frontend, ms => 48010, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(Readlink) / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Buffer)))[39m [34m[thread => 91, project => frontend, ms => 48016, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(ReadlinkSync) / TsMemberCall() / TsFunSig() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Buffer[39m [34m[thread => 91, project => frontend, ms => 48019, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(ReadlinkSync) / TsMemberCall() / TsFunSig() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Buffer)))[39m [34m[thread => 91, project => frontend, ms => 48033, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(ReadlinkSync) / TsMemberCall() / TsFunSig() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Buffer[39m [34m[thread => 91, project => frontend, ms => 48040, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(ReadlinkSync) / TsMemberCall() / TsFunSig() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(NodeJS), TsIdentSimple(ErrnoException)))[39m [34m[thread => 91, project => frontend, ms => 48047, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(RealPath) / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify NodeJS.ErrnoException[39m [34m[thread => 91, project => frontend, ms => 48050, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(RealPath) / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(NodeJS), TsIdentSimple(ErrnoException)))[39m [34m[thread => 91, project => frontend, ms => 48055, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(RealPath) / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify NodeJS.ErrnoException[39m [34m[thread => 91, project => frontend, ms => 48058, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(RealPath) / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Buffer)))[39m [34m[thread => 91, project => frontend, ms => 48063, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(RealPath) / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Buffer[39m [34m[thread => 91, project => frontend, ms => 48066, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(RealPath) / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(NodeJS), TsIdentSimple(ErrnoException)))[39m [34m[thread => 91, project => frontend, ms => 48070, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(RealPath) / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify NodeJS.ErrnoException[39m [34m[thread => 91, project => frontend, ms => 48076, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(RealPath) / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Buffer)))[39m [34m[thread => 91, project => frontend, ms => 48080, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(RealPath) / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Buffer[39m [34m[thread => 91, project => frontend, ms => 48086, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(RealPath) / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(NodeJS), TsIdentSimple(ErrnoException)))[39m [34m[thread => 91, project => frontend, ms => 48091, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(RealPath) / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify NodeJS.ErrnoException[39m [34m[thread => 91, project => frontend, ms => 48098, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(RealPath) / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Buffer)))[39m [34m[thread => 91, project => frontend, ms => 48104, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(RealPathSync) / TsMemberCall() / TsFunSig() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Buffer[39m [34m[thread => 91, project => frontend, ms => 48111, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(RealPathSync) / TsMemberCall() / TsFunSig() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Buffer)))[39m [34m[thread => 91, project => frontend, ms => 48117, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(RealPathSync) / TsMemberCall() / TsFunSig() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Buffer[39m [34m[thread => 91, project => frontend, ms => 48122, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(RealPathSync) / TsMemberCall() / TsFunSig() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(NodeJS), TsIdentSimple(ErrnoException)))[39m [34m[thread => 91, project => frontend, ms => 48144, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(Stat) / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify NodeJS.ErrnoException[39m [34m[thread => 91, project => frontend, ms => 48148, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(Stat) / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(NodeJS), TsIdentSimple(ErrnoException)))[39m [34m[thread => 91, project => frontend, ms => 48155, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(Stat) / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify NodeJS.ErrnoException[39m [34m[thread => 91, project => frontend, ms => 48157, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(Stat) / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(NodeJS), TsIdentSimple(ErrnoException)))[39m [34m[thread => 91, project => frontend, ms => 48164, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(Stat) / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify NodeJS.ErrnoException[39m [34m[thread => 91, project => frontend, ms => 48169, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(Stat) / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(NodeJS), TsIdentSimple(ErrnoException)))[39m [34m[thread => 91, project => frontend, ms => 48175, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(Stat) / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify NodeJS.ErrnoException[39m [34m[thread => 91, project => frontend, ms => 48178, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(Stat) / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(URL_Import)))[39m [34m[thread => 91, project => frontend, ms => 48185, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(URL_url) / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify URL_Import[39m [34m[thread => 91, project => frontend, ms => 48189, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(enhanced-resolve) / TsDeclInterface(URL_url) / TsTypeRef()), id => enhanced-resolve][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mExports.scala:101[39m  [94mCouldn't find expected module TsIdentModule(None,List(jiti, lib, jiti-register.d.mts))[39m [34m[thread => 91, project => frontend, ms => 48859, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(jiti/register)), id => jiti][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mExports.scala:101[39m  [94mCouldn't find expected module TsIdentModule(None,List(jiti, lib, jiti.d.mts))[39m [34m[thread => 91, project => frontend, ms => 48861, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(jiti/native)), id => jiti][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 49483, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(lightningcss) / TsDeclInterface(TransformOptions) / TsMemberProperty(code) / TsTypeRef()), id => lightningcss][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 49487, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(lightningcss) / TsDeclInterface(TransformResult) / TsMemberProperty(code) / TsTypeRef()), id => lightningcss][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 49489, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(lightningcss) / TsDeclInterface(TransformResult) / TsMemberProperty(map) / TsTypeUnion() / TsTypeRef()), id => lightningcss][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 49492, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(lightningcss) / TsDeclInterface(TransformAttributeOptions) / TsMemberProperty(code) / TsTypeRef()), id => lightningcss][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 49495, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(lightningcss) / TsDeclInterface(TransformAttributeResult) / TsMemberProperty(code) / TsTypeRef()), id => lightningcss][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mExports.scala:69[39m  [94mCouldn't find expected module TsIdentModule(None,List(..))[39m [34m[thread => 91, project => frontend, ms => 51500, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(source-map-js/lib/source-map-consumer)), id => source-map-js][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mExports.scala:69[39m  [94mCouldn't find expected module TsIdentModule(None,List(..))[39m [34m[thread => 91, project => frontend, ms => 51502, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(source-map-js/lib/source-map-consumer)), id => source-map-js][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mExports.scala:69[39m  [94mCouldn't find expected module TsIdentModule(None,List(..))[39m [34m[thread => 91, project => frontend, ms => 51504, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(source-map-js/lib/source-map-generator)), id => source-map-js][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mExports.scala:69[39m  [94mCouldn't find expected module TsIdentModule(None,List(..))[39m [34m[thread => 91, project => frontend, ms => 51505, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(source-map-js/lib/source-map-generator)), id => source-map-js][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mExports.scala:69[39m  [94mCouldn't find expected module TsIdentModule(None,List(..))[39m [34m[thread => 91, project => frontend, ms => 51507, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(source-map-js/lib/source-node)), id => source-map-js][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mExports.scala:69[39m  [94mCouldn't find expected module TsIdentModule(None,List(..))[39m [34m[thread => 91, project => frontend, ms => 51509, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(source-map-js/lib/source-node)), id => source-map-js][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mExports.scala:101[39m  [94mCouldn't find expected module TsIdentModule(None,List(tailwindcss, dist, lib.d.mts))[39m [34m[thread => 91, project => frontend, ms => 51691, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tailwindcss)), id => tailwindcss][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Theme)))[39m [34m[thread => 91, project => frontend, ms => 51956, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(@tailwindcss/node/dist) / TsDeclTypeAlias(DesignSystem) / TsTypeObject() / TsMemberProperty(theme) / TsTypeRef()), id => @tailwindcss/node][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Theme[39m [34m[thread => 91, project => frontend, ms => 51957, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(@tailwindcss/node/dist) / TsDeclTypeAlias(DesignSystem) / TsTypeObject() / TsMemberProperty(theme) / TsTypeRef()), id => @tailwindcss/node][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Utilities)))[39m [34m[thread => 91, project => frontend, ms => 51959, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(@tailwindcss/node/dist) / TsDeclTypeAlias(DesignSystem) / TsTypeObject() / TsMemberProperty(utilities) / TsTypeRef()), id => @tailwindcss/node][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Utilities[39m [34m[thread => 91, project => frontend, ms => 51961, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(@tailwindcss/node/dist) / TsDeclTypeAlias(DesignSystem) / TsTypeObject() / TsMemberProperty(utilities) / TsTypeRef()), id => @tailwindcss/node][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Variants)))[39m [34m[thread => 91, project => frontend, ms => 51964, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(@tailwindcss/node/dist) / TsDeclTypeAlias(DesignSystem) / TsTypeObject() / TsMemberProperty(variants) / TsTypeRef()), id => @tailwindcss/node][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Variants[39m [34m[thread => 91, project => frontend, ms => 51966, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(@tailwindcss/node/dist) / TsDeclTypeAlias(DesignSystem) / TsTypeObject() / TsMemberProperty(variants) / TsTypeRef()), id => @tailwindcss/node][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(ClassEntry)))[39m [34m[thread => 91, project => frontend, ms => 51969, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(@tailwindcss/node/dist) / TsDeclTypeAlias(DesignSystem) / TsTypeObject() / TsMemberFunction(getClassList) / TsFunSig() / TsTypeRef() / TsTypeRef()), id => @tailwindcss/node][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify ClassEntry[39m [34m[thread => 91, project => frontend, ms => 51972, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(@tailwindcss/node/dist) / TsDeclTypeAlias(DesignSystem) / TsTypeObject() / TsMemberFunction(getClassList) / TsFunSig() / TsTypeRef() / TsTypeRef()), id => @tailwindcss/node][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(VariantEntry)))[39m [34m[thread => 91, project => frontend, ms => 51975, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(@tailwindcss/node/dist) / TsDeclTypeAlias(DesignSystem) / TsTypeObject() / TsMemberFunction(getVariants) / TsFunSig() / TsTypeRef() / TsTypeRef()), id => @tailwindcss/node][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify VariantEntry[39m [34m[thread => 91, project => frontend, ms => 51977, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(@tailwindcss/node/dist) / TsDeclTypeAlias(DesignSystem) / TsTypeObject() / TsMemberFunction(getVariants) / TsFunSig() / TsTypeRef() / TsTypeRef()), id => @tailwindcss/node][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Candidate)))[39m [34m[thread => 91, project => frontend, ms => 51981, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(@tailwindcss/node/dist) / TsDeclTypeAlias(DesignSystem) / TsTypeObject() / TsMemberFunction(parseCandidate) / TsFunSig() / TsTypeRef() / TsTypeRef() / TsTypeRef()), id => @tailwindcss/node][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Candidate[39m [34m[thread => 91, project => frontend, ms => 51984, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(@tailwindcss/node/dist) / TsDeclTypeAlias(DesignSystem) / TsTypeObject() / TsMemberFunction(parseCandidate) / TsFunSig() / TsTypeRef() / TsTypeRef() / TsTypeRef()), id => @tailwindcss/node][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Variant)))[39m [34m[thread => 91, project => frontend, ms => 51988, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(@tailwindcss/node/dist) / TsDeclTypeAlias(DesignSystem) / TsTypeObject() / TsMemberFunction(parseVariant) / TsFunSig() / TsTypeUnion() / TsTypeRef() / TsTypeRef()), id => @tailwindcss/node][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Variant[39m [34m[thread => 91, project => frontend, ms => 51994, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(@tailwindcss/node/dist) / TsDeclTypeAlias(DesignSystem) / TsTypeObject() / TsMemberFunction(parseVariant) / TsFunSig() / TsTypeUnion() / TsTypeRef() / TsTypeRef()), id => @tailwindcss/node][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Candidate)))[39m [34m[thread => 91, project => frontend, ms => 51997, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(@tailwindcss/node/dist) / TsDeclTypeAlias(DesignSystem) / TsTypeObject() / TsMemberFunction(compileAstNodes) / TsFunSig() / TsFunParam() / TsTypeRef()), id => @tailwindcss/node][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Candidate[39m [34m[thread => 91, project => frontend, ms => 52001, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(@tailwindcss/node/dist) / TsDeclTypeAlias(DesignSystem) / TsTypeObject() / TsMemberFunction(compileAstNodes) / TsFunSig() / TsFunParam() / TsTypeRef()), id => @tailwindcss/node][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Candidate)))[39m [34m[thread => 91, project => frontend, ms => 52003, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(@tailwindcss/node/dist) / TsDeclTypeAlias(DesignSystem) / TsTypeObject() / TsMemberFunction(printCandidate) / TsFunSig() / TsFunParam() / TsTypeRef()), id => @tailwindcss/node][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Candidate[39m [34m[thread => 91, project => frontend, ms => 52005, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(@tailwindcss/node/dist) / TsDeclTypeAlias(DesignSystem) / TsTypeObject() / TsMemberFunction(printCandidate) / TsFunSig() / TsFunParam() / TsTypeRef()), id => @tailwindcss/node][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Variant)))[39m [34m[thread => 91, project => frontend, ms => 52008, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(@tailwindcss/node/dist) / TsDeclTypeAlias(DesignSystem) / TsTypeObject() / TsMemberFunction(printVariant) / TsFunSig() / TsFunParam() / TsTypeRef()), id => @tailwindcss/node][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Variant[39m [34m[thread => 91, project => frontend, ms => 52010, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(@tailwindcss/node/dist) / TsDeclTypeAlias(DesignSystem) / TsTypeObject() / TsMemberFunction(printVariant) / TsFunSig() / TsFunParam() / TsTypeRef()), id => @tailwindcss/node][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Variant)))[39m [34m[thread => 91, project => frontend, ms => 52013, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(@tailwindcss/node/dist) / TsDeclTypeAlias(DesignSystem) / TsTypeObject() / TsMemberFunction(getVariantOrder) / TsFunSig() / TsTypeRef() / TsTypeRef()), id => @tailwindcss/node][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Variant[39m [34m[thread => 91, project => frontend, ms => 52016, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(@tailwindcss/node/dist) / TsDeclTypeAlias(DesignSystem) / TsTypeObject() / TsMemberFunction(getVariantOrder) / TsFunSig() / TsTypeRef() / TsTypeRef()), id => @tailwindcss/node][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Polyfills)))[39m [34m[thread => 91, project => frontend, ms => 52023, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(@tailwindcss/node/dist) / TsDeclInterface(CompileOptions) / TsMemberProperty(polyfills) / TsTypeUnion() / TsTypeRef()), id => @tailwindcss/node][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Polyfills[39m [34m[thread => 91, project => frontend, ms => 52027, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(@tailwindcss/node/dist) / TsDeclInterface(CompileOptions) / TsMemberProperty(polyfills) / TsTypeUnion() / TsTypeRef()), id => @tailwindcss/node][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Features)))[39m [34m[thread => 91, project => frontend, ms => 52031, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(@tailwindcss/node/dist) / TsDeclFunction(compileAst) / TsFunSig() / TsTypeRef() / TsTypeObject() / TsMemberProperty(features) / TsTypeRef()), id => @tailwindcss/node][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Features[39m [34m[thread => 91, project => frontend, ms => 52036, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(@tailwindcss/node/dist) / TsDeclFunction(compileAst) / TsFunSig() / TsTypeRef() / TsTypeObject() / TsMemberProperty(features) / TsTypeRef()), id => @tailwindcss/node][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Features)))[39m [34m[thread => 91, project => frontend, ms => 52041, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(@tailwindcss/node/dist) / TsDeclFunction(compile) / TsFunSig() / TsTypeRef() / TsTypeObject() / TsMemberProperty(features) / TsTypeRef()), id => @tailwindcss/node][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Features[39m [34m[thread => 91, project => frontend, ms => 52043, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(@tailwindcss/node/dist) / TsDeclFunction(compile) / TsFunSig() / TsTypeRef() / TsTypeObject() / TsMemberProperty(features) / TsTypeRef()), id => @tailwindcss/node][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Disposable)))[39m [34m[thread => 91, project => frontend, ms => 52047, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(@tailwindcss/node/dist) / TsDeclClass(Instrumentation)), id => @tailwindcss/node][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Disposable[39m [34m[thread => 91, project => frontend, ms => 52049, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(@tailwindcss/node/dist) / TsDeclClass(Instrumentation)), id => @tailwindcss/node][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mResolveTypeQueries.scala:232[39m [34mmsg[39m [94mCouldn't resolve typeof compileAstNodes[39m [34m[thread => 91, project => frontend, ms => 52066, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(@tailwindcss/node/dist) / TsDeclTypeAlias(DesignSystem) / TsTypeObject() / TsMemberFunction(compileAstNodes) / TsFunSig() / TsTypeRef() / TsTypeQuery()), id => @tailwindcss/node][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mReplaceExports.scala:55[39m  [94mDropping unexpected export in namespace TsExport(NoComments,false,Named,Names(IArray((TsQIdent(IArray(TsIdentSimple(env_DEBUG))),Some(TsIdentSimple(DEBUG)))),None))[39m [34m[thread => 91, project => frontend, ms => 52073, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(@tailwindcss/node/dist) / TsDeclNamespace(env)), id => @tailwindcss/node][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mReplaceExports.scala:55[39m  [94mDropping unexpected export in namespace TsExport(NoComments,false,Named,Names(IArray((TsQIdent(IArray(TsIdentSimple(env_DEBUG))),Some(TsIdentSimple(DEBUG)))),None))[39m [34m[thread => 91, project => frontend, ms => 52077, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(@tailwindcss/node/dist) / TsDeclNamespace(env)), id => @tailwindcss/node][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mExports.scala:101[39m  [94mCouldn't find expected module TsIdentModule(Some(tailwindcss),List(node, dist, esm-cache.loader.d.mts))[39m [34m[thread => 91, project => frontend, ms => 52084, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(@tailwindcss/node/esm-cache-loader)), id => @tailwindcss/node][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mReplaceExports.scala:55[39m  [94mDropping unexpected export in namespace TsExport(NoComments,false,Named,Names(IArray((TsQIdent(IArray(TsIdentSimple(env_DEBUG))),Some(TsIdentSimple(DEBUG)))),None))[39m [34m[thread => 91, project => frontend, ms => 52087, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(@tailwindcss/node/dist) / TsDeclNamespace(env)), id => @tailwindcss/node][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mPhase1ReadTypescript.scala:99[39m  [94mdirectives: couldn't resolve TypesRef(node)[39m [34m[thread => 91, project => frontend, ms => 52257, phase => typescript, id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mPhase1ReadTypescript.scala:140[39m  [94mCouldn't resolve inferred dependency node[39m [34m[thread => 91, project => frontend, ms => 52261, phase => typescript, id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mPhase1ReadTypescript.scala:99[39m  [94mdirectives: couldn't resolve TypesRef(node)[39m [34m[thread => 91, project => frontend, ms => 52304, phase => typescript, id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mPhase1ReadTypescript.scala:140[39m  [94mCouldn't resolve inferred dependency node[39m [34m[thread => 91, project => frontend, ms => 52306, phase => typescript, id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mPhase1ReadTypescript.scala:99[39m  [94mdirectives: couldn't resolve TypesRef(node)[39m [34m[thread => 91, project => frontend, ms => 52339, phase => typescript, id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mPhase1ReadTypescript.scala:140[39m  [94mCouldn't resolve inferred dependency node[39m [34m[thread => 91, project => frontend, ms => 52343, phase => typescript, id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mPhase1ReadTypescript.scala:99[39m  [94mdirectives: couldn't resolve TypesRef(node)[39m [34m[thread => 91, project => frontend, ms => 52378, phase => typescript, id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mPhase1ReadTypescript.scala:99[39m  [94mdirectives: couldn't resolve TypesRef(node)[39m [34m[thread => 91, project => frontend, ms => 52389, phase => typescript, id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mPhase1ReadTypescript.scala:99[39m  [94mdirectives: couldn't resolve TypesRef(node)[39m [34m[thread => 91, project => frontend, ms => 52393, phase => typescript, id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mPhase1ReadTypescript.scala:140[39m  [94mCouldn't resolve inferred dependency node[39m [34m[thread => 91, project => frontend, ms => 52408, phase => typescript, id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mPhase1ReadTypescript.scala:99[39m  [94mdirectives: couldn't resolve TypesRef(node)[39m [34m[thread => 91, project => frontend, ms => 52416, phase => typescript, id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mPhase1ReadTypescript.scala:99[39m  [94mdirectives: couldn't resolve TypesRef(node)[39m [34m[thread => 91, project => frontend, ms => 52418, phase => typescript, id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mPhase1ReadTypescript.scala:140[39m  [94mCouldn't resolve inferred dependency node[39m [34m[thread => 91, project => frontend, ms => 52428, phase => typescript, id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mPhase1ReadTypescript.scala:99[39m  [94mdirectives: couldn't resolve TypesRef(node)[39m [34m[thread => 91, project => frontend, ms => 52441, phase => typescript, id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mPhase1ReadTypescript.scala:140[39m  [94mCouldn't resolve inferred dependency node[39m [34m[thread => 91, project => frontend, ms => 52445, phase => typescript, id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mPhase1ReadTypescript.scala:99[39m  [94mdirectives: couldn't resolve TypesRef(node)[39m [34m[thread => 91, project => frontend, ms => 52452, phase => typescript, id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mPhase1ReadTypescript.scala:140[39m  [94mCouldn't resolve inferred dependency node[39m [34m[thread => 91, project => frontend, ms => 52458, phase => typescript, id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mPhase1ReadTypescript.scala:99[39m  [94mdirectives: couldn't resolve TypesRef(node)[39m [34m[thread => 91, project => frontend, ms => 52505, phase => typescript, id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mPhase1ReadTypescript.scala:99[39m  [94mdirectives: couldn't resolve TypesRef(node)[39m [34m[thread => 91, project => frontend, ms => 52527, phase => typescript, id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mPhase1ReadTypescript.scala:140[39m  [94mCouldn't resolve inferred dependency node[39m [34m[thread => 91, project => frontend, ms => 52529, phase => typescript, id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mPhase1ReadTypescript.scala:99[39m  [94mdirectives: couldn't resolve TypesRef(node)[39m [34m[thread => 91, project => frontend, ms => 52546, phase => typescript, id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mPhase1ReadTypescript.scala:99[39m  [94mdirectives: couldn't resolve TypesRef(node)[39m [34m[thread => 91, project => frontend, ms => 52547, phase => typescript, id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mPhase1ReadTypescript.scala:99[39m  [94mdirectives: couldn't resolve TypesRef(node)[39m [34m[thread => 91, project => frontend, ms => 52548, phase => typescript, id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mPhase1ReadTypescript.scala:140[39m  [94mCouldn't resolve inferred dependency node[39m [34m[thread => 91, project => frontend, ms => 52557, phase => typescript, id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mPhase1ReadTypescript.scala:99[39m  [94mdirectives: couldn't resolve TypesRef(node)[39m [34m[thread => 91, project => frontend, ms => 52581, phase => typescript, id => @isaacs/fs-minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mPhase1ReadTypescript.scala:99[39m  [94mdirectives: couldn't resolve TypesRef(node)[39m [34m[thread => 91, project => frontend, ms => 52583, phase => typescript, id => @isaacs/fs-minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mPhase1ReadTypescript.scala:99[39m  [94mdirectives: couldn't resolve TypesRef(node)[39m [34m[thread => 91, project => frontend, ms => 52586, phase => typescript, id => @isaacs/fs-minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mPhase1ReadTypescript.scala:140[39m  [94mCouldn't resolve inferred dependency node[39m [34m[thread => 91, project => frontend, ms => 52589, phase => typescript, id => @isaacs/fs-minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mPhase1ReadTypescript.scala:99[39m  [94mdirectives: couldn't resolve TypesRef(node)[39m [34m[thread => 91, project => frontend, ms => 52621, phase => typescript, id => minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mPhase1ReadTypescript.scala:99[39m  [94mdirectives: couldn't resolve TypesRef(node)[39m [34m[thread => 91, project => frontend, ms => 52624, phase => typescript, id => minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mPhase1ReadTypescript.scala:99[39m  [94mdirectives: couldn't resolve TypesRef(node)[39m [34m[thread => 91, project => frontend, ms => 52626, phase => typescript, id => minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mPhase1ReadTypescript.scala:99[39m  [94mdirectives: couldn't resolve TypesRef(node)[39m [34m[thread => 91, project => frontend, ms => 52630, phase => typescript, id => minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mPhase1ReadTypescript.scala:140[39m  [94mCouldn't resolve inferred dependency node[39m [34m[thread => 91, project => frontend, ms => 52636, phase => typescript, id => minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(NodeJS), TsIdentSimple(WriteStream)))[39m [34m[thread => 91, project => frontend, ms => 52649, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minipass) / TsExport() / Tree() / TsDeclFunction(isStream) / TsFunSig() / TsTypeIs() / TsTypeUnion() / TsTypeRef()), id => minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify NodeJS.WriteStream[39m [34m[thread => 91, project => frontend, ms => 52652, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minipass) / TsExport() / Tree() / TsDeclFunction(isStream) / TsFunSig() / TsTypeIs() / TsTypeUnion() / TsTypeRef()), id => minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(NodeJS), TsIdentSimple(ReadStream)))[39m [34m[thread => 91, project => frontend, ms => 52655, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minipass) / TsExport() / Tree() / TsDeclFunction(isStream) / TsFunSig() / TsTypeIs() / TsTypeUnion() / TsTypeRef()), id => minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify NodeJS.ReadStream[39m [34m[thread => 91, project => frontend, ms => 52658, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minipass) / TsExport() / Tree() / TsDeclFunction(isStream) / TsFunSig() / TsTypeIs() / TsTypeUnion() / TsTypeRef()), id => minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(NodeJS), TsIdentSimple(ReadStream)))[39m [34m[thread => 91, project => frontend, ms => 52661, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minipass) / TsExport() / Tree() / TsDeclFunction(isStream) / TsFunSig() / TsTypeIs() / TsTypeUnion() / TsTypeIntersect() / TsTypeRef()), id => minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify NodeJS.ReadStream[39m [34m[thread => 91, project => frontend, ms => 52664, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minipass) / TsExport() / Tree() / TsDeclFunction(isStream) / TsFunSig() / TsTypeIs() / TsTypeUnion() / TsTypeIntersect() / TsTypeRef()), id => minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(EventEmitter)))[39m [34m[thread => 91, project => frontend, ms => 52667, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minipass) / TsExport() / Tree() / TsDeclFunction(isStream) / TsFunSig() / TsTypeIs() / TsTypeUnion() / TsTypeIntersect() / TsTypeRef()), id => minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify EventEmitter[39m [34m[thread => 91, project => frontend, ms => 52669, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minipass) / TsExport() / Tree() / TsDeclFunction(isStream) / TsFunSig() / TsTypeIs() / TsTypeUnion() / TsTypeIntersect() / TsTypeRef()), id => minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(NodeJS), TsIdentSimple(WriteStream)))[39m [34m[thread => 91, project => frontend, ms => 52672, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minipass) / TsExport() / Tree() / TsDeclFunction(isStream) / TsFunSig() / TsTypeIs() / TsTypeUnion() / TsTypeIntersect() / TsTypeRef()), id => minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify NodeJS.WriteStream[39m [34m[thread => 91, project => frontend, ms => 52675, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minipass) / TsExport() / Tree() / TsDeclFunction(isStream) / TsFunSig() / TsTypeIs() / TsTypeUnion() / TsTypeIntersect() / TsTypeRef()), id => minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(EventEmitter)))[39m [34m[thread => 91, project => frontend, ms => 52678, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minipass) / TsExport() / Tree() / TsDeclFunction(isStream) / TsFunSig() / TsTypeIs() / TsTypeUnion() / TsTypeIntersect() / TsTypeRef()), id => minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify EventEmitter[39m [34m[thread => 91, project => frontend, ms => 52681, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minipass) / TsExport() / Tree() / TsDeclFunction(isStream) / TsFunSig() / TsTypeIs() / TsTypeUnion() / TsTypeIntersect() / TsTypeRef()), id => minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(BufferEncoding)))[39m [34m[thread => 91, project => frontend, ms => 52686, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minipass) / TsExport() / Tree() / TsDeclNamespace(Minipass) / TsExport() / Tree() / TsDeclTypeAlias(Encoding) / TsTypeUnion() / TsTypeRef()), id => minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify BufferEncoding[39m [34m[thread => 91, project => frontend, ms => 52689, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minipass) / TsExport() / Tree() / TsDeclNamespace(Minipass) / TsExport() / Tree() / TsDeclTypeAlias(Encoding) / TsTypeUnion() / TsTypeRef()), id => minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(NodeJS), TsIdentSimple(WriteStream)))[39m [34m[thread => 91, project => frontend, ms => 52696, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minipass) / TsExport() / Tree() / TsDeclNamespace(Minipass) / TsExport() / Tree() / TsDeclTypeAlias(Writable) / TsTypeUnion() / TsTypeRef()), id => minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify NodeJS.WriteStream[39m [34m[thread => 91, project => frontend, ms => 52698, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minipass) / TsExport() / Tree() / TsDeclNamespace(Minipass) / TsExport() / Tree() / TsDeclTypeAlias(Writable) / TsTypeUnion() / TsTypeRef()), id => minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(NodeJS), TsIdentSimple(WriteStream)))[39m [34m[thread => 91, project => frontend, ms => 52701, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minipass) / TsExport() / Tree() / TsDeclNamespace(Minipass) / TsExport() / Tree() / TsDeclTypeAlias(Writable) / TsTypeUnion() / TsTypeIntersect() / TsTypeRef()), id => minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify NodeJS.WriteStream[39m [34m[thread => 91, project => frontend, ms => 52703, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minipass) / TsExport() / Tree() / TsDeclNamespace(Minipass) / TsExport() / Tree() / TsDeclTypeAlias(Writable) / TsTypeUnion() / TsTypeIntersect() / TsTypeRef()), id => minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(EventEmitter)))[39m [34m[thread => 91, project => frontend, ms => 52706, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minipass) / TsExport() / Tree() / TsDeclNamespace(Minipass) / TsExport() / Tree() / TsDeclTypeAlias(Writable) / TsTypeUnion() / TsTypeIntersect() / TsTypeRef()), id => minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify EventEmitter[39m [34m[thread => 91, project => frontend, ms => 52709, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minipass) / TsExport() / Tree() / TsDeclNamespace(Minipass) / TsExport() / Tree() / TsDeclTypeAlias(Writable) / TsTypeUnion() / TsTypeIntersect() / TsTypeRef()), id => minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(NodeJS), TsIdentSimple(ReadStream)))[39m [34m[thread => 91, project => frontend, ms => 52712, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minipass) / TsExport() / Tree() / TsDeclNamespace(Minipass) / TsExport() / Tree() / TsDeclTypeAlias(Readable) / TsTypeUnion() / TsTypeRef()), id => minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify NodeJS.ReadStream[39m [34m[thread => 91, project => frontend, ms => 52716, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minipass) / TsExport() / Tree() / TsDeclNamespace(Minipass) / TsExport() / Tree() / TsDeclTypeAlias(Readable) / TsTypeUnion() / TsTypeRef()), id => minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(NodeJS), TsIdentSimple(ReadStream)))[39m [34m[thread => 91, project => frontend, ms => 52719, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minipass) / TsExport() / Tree() / TsDeclNamespace(Minipass) / TsExport() / Tree() / TsDeclTypeAlias(Readable) / TsTypeUnion() / TsTypeIntersect() / TsTypeRef()), id => minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify NodeJS.ReadStream[39m [34m[thread => 91, project => frontend, ms => 52722, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minipass) / TsExport() / Tree() / TsDeclNamespace(Minipass) / TsExport() / Tree() / TsDeclTypeAlias(Readable) / TsTypeUnion() / TsTypeIntersect() / TsTypeRef()), id => minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(EventEmitter)))[39m [34m[thread => 91, project => frontend, ms => 52725, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minipass) / TsExport() / Tree() / TsDeclNamespace(Minipass) / TsExport() / Tree() / TsDeclTypeAlias(Readable) / TsTypeUnion() / TsTypeIntersect() / TsTypeRef()), id => minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify EventEmitter[39m [34m[thread => 91, project => frontend, ms => 52727, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minipass) / TsExport() / Tree() / TsDeclNamespace(Minipass) / TsExport() / Tree() / TsDeclTypeAlias(Readable) / TsTypeUnion() / TsTypeIntersect() / TsTypeRef()), id => minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(AsyncIterable)))[39m [34m[thread => 91, project => frontend, ms => 52731, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minipass) / TsExport() / Tree() / TsDeclNamespace(Minipass) / TsExport() / Tree() / TsDeclTypeAlias(DualIterable) / TsTypeIntersect() / TsTypeRef()), id => minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify AsyncIterable<T>[39m [34m[thread => 91, project => frontend, ms => 52736, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minipass) / TsExport() / Tree() / TsDeclNamespace(Minipass) / TsExport() / Tree() / TsDeclTypeAlias(DualIterable) / TsTypeIntersect() / TsTypeRef()), id => minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Buffer)))[39m [34m[thread => 91, project => frontend, ms => 52741, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minipass) / TsExport() / Tree() / TsDeclNamespace(Minipass) / TsExport() / Tree() / TsDeclInterface(Events) / TsTypeParam() / TsTypeRef()), id => minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Buffer[39m [34m[thread => 91, project => frontend, ms => 52744, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minipass) / TsExport() / Tree() / TsDeclNamespace(Minipass) / TsExport() / Tree() / TsDeclInterface(Events) / TsTypeParam() / TsTypeRef()), id => minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Buffer)))[39m [34m[thread => 91, project => frontend, ms => 52746, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minipass) / TsExport() / Tree() / TsDeclNamespace(Minipass) / TsExport() / Tree() / TsDeclTypeAlias(ContiguousData) / TsTypeUnion() / TsTypeRef()), id => minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Buffer[39m [34m[thread => 91, project => frontend, ms => 52748, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minipass) / TsExport() / Tree() / TsDeclNamespace(Minipass) / TsExport() / Tree() / TsDeclTypeAlias(ContiguousData) / TsTypeUnion() / TsTypeRef()), id => minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Buffer)))[39m [34m[thread => 91, project => frontend, ms => 52753, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minipass) / TsExport() / Tree() / TsDeclNamespace(Minipass) / TsExport() / Tree() / TsDeclTypeAlias(BufferOrString) / TsTypeUnion() / TsTypeRef()), id => minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Buffer[39m [34m[thread => 91, project => frontend, ms => 52760, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minipass) / TsExport() / Tree() / TsDeclNamespace(Minipass) / TsExport() / Tree() / TsDeclTypeAlias(BufferOrString) / TsTypeUnion() / TsTypeRef()), id => minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(BufferEncoding)))[39m [34m[thread => 91, project => frontend, ms => 52764, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minipass) / TsExport() / Tree() / TsDeclNamespace(Minipass) / TsExport() / Tree() / TsDeclTypeAlias(SharedOptions) / TsTypeObject() / TsMemberProperty(encoding) / TsTypeUnion() / TsTypeRef()), id => minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify BufferEncoding[39m [34m[thread => 91, project => frontend, ms => 52767, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minipass) / TsExport() / Tree() / TsDeclNamespace(Minipass) / TsExport() / Tree() / TsDeclTypeAlias(SharedOptions) / TsTypeObject() / TsMemberProperty(encoding) / TsTypeUnion() / TsTypeRef()), id => minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(BufferEncoding)))[39m [34m[thread => 91, project => frontend, ms => 52771, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minipass) / TsExport() / Tree() / TsDeclNamespace(Minipass) / TsExport() / Tree() / TsDeclTypeAlias(EncodingOptions) / TsTypeIntersect() / TsTypeObject() / TsMemberProperty(encoding) / TsTypeRef()), id => minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify BufferEncoding[39m [34m[thread => 91, project => frontend, ms => 52775, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minipass) / TsExport() / Tree() / TsDeclNamespace(Minipass) / TsExport() / Tree() / TsDeclTypeAlias(EncodingOptions) / TsTypeIntersect() / TsTypeObject() / TsMemberProperty(encoding) / TsTypeRef()), id => minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Buffer)))[39m [34m[thread => 91, project => frontend, ms => 52779, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minipass) / TsExport() / Tree() / TsDeclNamespace(Minipass) / TsExport() / Tree() / TsDeclTypeAlias(Options) / TsTypeUnion() / TsTypeConditional() / TsTypeConditional() / TsTypeExtends() / TsTypeRef()), id => minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Buffer[39m [34m[thread => 91, project => frontend, ms => 52783, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minipass) / TsExport() / Tree() / TsDeclNamespace(Minipass) / TsExport() / Tree() / TsDeclTypeAlias(Options) / TsTypeUnion() / TsTypeConditional() / TsTypeConditional() / TsTypeExtends() / TsTypeRef()), id => minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(EventEmitter)))[39m [34m[thread => 91, project => frontend, ms => 52788, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minipass) / TsExport() / Tree() / TsDeclClass(Minipass)), id => minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify EventEmitter[39m [34m[thread => 91, project => frontend, ms => 52791, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minipass) / TsExport() / Tree() / TsDeclClass(Minipass)), id => minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Buffer)))[39m [34m[thread => 91, project => frontend, ms => 52794, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minipass) / TsExport() / Tree() / TsDeclClass(Minipass) / TsTypeParam() / TsTypeRef()), id => minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Buffer[39m [34m[thread => 91, project => frontend, ms => 52797, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minipass) / TsExport() / Tree() / TsDeclClass(Minipass) / TsTypeParam() / TsTypeRef()), id => minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(BufferEncoding)))[39m [34m[thread => 91, project => frontend, ms => 52807, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minipass) / TsExport() / Tree() / TsDeclClass(Minipass) / TsMemberIndex() / TsTypeUnion() / TsTypeRef()), id => minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify BufferEncoding[39m [34m[thread => 91, project => frontend, ms => 52810, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minipass) / TsExport() / Tree() / TsDeclClass(Minipass) / TsMemberIndex() / TsTypeUnion() / TsTypeRef()), id => minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Buffer)))[39m [34m[thread => 91, project => frontend, ms => 52813, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minipass) / TsExport() / Tree() / TsDeclClass(Minipass) / TsMemberFunction(constructor) / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeConditional() / TsTypeExtends() / TsTypeRef()), id => minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Buffer[39m [34m[thread => 91, project => frontend, ms => 52817, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minipass) / TsExport() / Tree() / TsDeclClass(Minipass) / TsMemberFunction(constructor) / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeConditional() / TsTypeExtends() / TsTypeRef()), id => minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(BufferEncoding)))[39m [34m[thread => 91, project => frontend, ms => 52824, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minipass) / TsExport() / Tree() / TsDeclClass(Minipass) / TsMemberFunction(encoding) / TsFunSig() / TsTypeUnion() / TsTypeRef()), id => minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify BufferEncoding[39m [34m[thread => 91, project => frontend, ms => 52827, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minipass) / TsExport() / Tree() / TsDeclClass(Minipass) / TsMemberFunction(encoding) / TsFunSig() / TsTypeUnion() / TsTypeRef()), id => minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(BufferEncoding)))[39m [34m[thread => 91, project => frontend, ms => 52830, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minipass) / TsExport() / Tree() / TsDeclClass(Minipass) / TsMemberFunction(encoding) / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify BufferEncoding[39m [34m[thread => 91, project => frontend, ms => 52833, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minipass) / TsExport() / Tree() / TsDeclClass(Minipass) / TsMemberFunction(encoding) / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(AsyncGenerator)))[39m [34m[thread => 91, project => frontend, ms => 52836, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minipass) / TsExport() / Tree() / TsDeclClass(Minipass) / TsMemberIndex() / TsTypeFunction() / TsFunSig() / TsTypeRef()), id => minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify AsyncGenerator<RType, void, void>[39m [34m[thread => 91, project => frontend, ms => 52838, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minipass) / TsExport() / Tree() / TsDeclClass(Minipass) / TsMemberIndex() / TsTypeFunction() / TsFunSig() / TsTypeRef()), id => minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(NodeJS), TsIdentSimple(WriteStream)))[39m [34m[thread => 91, project => frontend, ms => 52843, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minipass) / TsExport() / Tree() / TsDeclClass(Minipass) / TsMemberFunction(isStream) / TsFunSig() / TsTypeFunction() / TsFunSig() / TsTypeIs() / TsTypeUnion() / TsTypeRef()), id => minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify NodeJS.WriteStream[39m [34m[thread => 91, project => frontend, ms => 52845, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minipass) / TsExport() / Tree() / TsDeclClass(Minipass) / TsMemberFunction(isStream) / TsFunSig() / TsTypeFunction() / TsFunSig() / TsTypeIs() / TsTypeUnion() / TsTypeRef()), id => minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(NodeJS), TsIdentSimple(ReadStream)))[39m [34m[thread => 91, project => frontend, ms => 52848, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minipass) / TsExport() / Tree() / TsDeclClass(Minipass) / TsMemberFunction(isStream) / TsFunSig() / TsTypeFunction() / TsFunSig() / TsTypeIs() / TsTypeUnion() / TsTypeRef()), id => minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify NodeJS.ReadStream[39m [34m[thread => 91, project => frontend, ms => 52856, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minipass) / TsExport() / Tree() / TsDeclClass(Minipass) / TsMemberFunction(isStream) / TsFunSig() / TsTypeFunction() / TsFunSig() / TsTypeIs() / TsTypeUnion() / TsTypeRef()), id => minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(NodeJS), TsIdentSimple(ReadStream)))[39m [34m[thread => 91, project => frontend, ms => 52865, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minipass) / TsExport() / Tree() / TsDeclClass(Minipass) / TsMemberFunction(isStream) / TsFunSig() / TsTypeFunction() / TsFunSig() / TsTypeIs() / TsTypeUnion() / TsTypeIntersect() / TsTypeRef()), id => minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify NodeJS.ReadStream[39m [34m[thread => 91, project => frontend, ms => 52869, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minipass) / TsExport() / Tree() / TsDeclClass(Minipass) / TsMemberFunction(isStream) / TsFunSig() / TsTypeFunction() / TsFunSig() / TsTypeIs() / TsTypeUnion() / TsTypeIntersect() / TsTypeRef()), id => minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(EventEmitter)))[39m [34m[thread => 91, project => frontend, ms => 52877, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minipass) / TsExport() / Tree() / TsDeclClass(Minipass) / TsMemberFunction(isStream) / TsFunSig() / TsTypeFunction() / TsFunSig() / TsTypeIs() / TsTypeUnion() / TsTypeIntersect() / TsTypeRef()), id => minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify EventEmitter[39m [34m[thread => 91, project => frontend, ms => 52881, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minipass) / TsExport() / Tree() / TsDeclClass(Minipass) / TsMemberFunction(isStream) / TsFunSig() / TsTypeFunction() / TsFunSig() / TsTypeIs() / TsTypeUnion() / TsTypeIntersect() / TsTypeRef()), id => minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(NodeJS), TsIdentSimple(WriteStream)))[39m [34m[thread => 91, project => frontend, ms => 52883, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minipass) / TsExport() / Tree() / TsDeclClass(Minipass) / TsMemberFunction(isStream) / TsFunSig() / TsTypeFunction() / TsFunSig() / TsTypeIs() / TsTypeUnion() / TsTypeIntersect() / TsTypeRef()), id => minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify NodeJS.WriteStream[39m [34m[thread => 91, project => frontend, ms => 52888, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minipass) / TsExport() / Tree() / TsDeclClass(Minipass) / TsMemberFunction(isStream) / TsFunSig() / TsTypeFunction() / TsFunSig() / TsTypeIs() / TsTypeUnion() / TsTypeIntersect() / TsTypeRef()), id => minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(EventEmitter)))[39m [34m[thread => 91, project => frontend, ms => 52898, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minipass) / TsExport() / Tree() / TsDeclClass(Minipass) / TsMemberFunction(isStream) / TsFunSig() / TsTypeFunction() / TsFunSig() / TsTypeIs() / TsTypeUnion() / TsTypeIntersect() / TsTypeRef()), id => minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify EventEmitter[39m [34m[thread => 91, project => frontend, ms => 52900, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minipass) / TsExport() / Tree() / TsDeclClass(Minipass) / TsMemberFunction(isStream) / TsFunSig() / TsTypeFunction() / TsFunSig() / TsTypeIs() / TsTypeUnion() / TsTypeIntersect() / TsTypeRef()), id => minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(StringDecoder)))[39m [34m[thread => 91, project => frontend, ms => 52903, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minipass) / TsDeclTypeAlias(SD) / TsTypeIntersect() / TsTypeRef()), id => minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify StringDecoder[39m [34m[thread => 91, project => frontend, ms => 52905, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minipass) / TsDeclTypeAlias(SD) / TsTypeIntersect() / TsTypeRef()), id => minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Buffer)))[39m [34m[thread => 91, project => frontend, ms => 53004, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(@isaacs/fs-minipass) / TsExport() / Tree() / TsDeclClass(ReadStream) / TsTypeRef() / TsTypeRef()), id => @isaacs/fs-minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Buffer[39m [34m[thread => 91, project => frontend, ms => 53007, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(@isaacs/fs-minipass) / TsExport() / Tree() / TsDeclClass(ReadStream) / TsTypeRef() / TsTypeRef()), id => @isaacs/fs-minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(NodeJS), TsIdentSimple(ErrnoException)))[39m [34m[thread => 91, project => frontend, ms => 53010, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(@isaacs/fs-minipass) / TsExport() / Tree() / TsDeclClass(ReadStream) / TsMemberIndex() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => @isaacs/fs-minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify NodeJS.ErrnoException[39m [34m[thread => 91, project => frontend, ms => 53013, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(@isaacs/fs-minipass) / TsExport() / Tree() / TsDeclClass(ReadStream) / TsMemberIndex() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => @isaacs/fs-minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Buffer)))[39m [34m[thread => 91, project => frontend, ms => 53016, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(@isaacs/fs-minipass) / TsExport() / Tree() / TsDeclClass(ReadStream) / TsMemberIndex() / TsTypeFunction() / TsFunSig() / TsTypeRef()), id => @isaacs/fs-minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Buffer[39m [34m[thread => 91, project => frontend, ms => 53020, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(@isaacs/fs-minipass) / TsExport() / Tree() / TsDeclClass(ReadStream) / TsMemberIndex() / TsTypeFunction() / TsFunSig() / TsTypeRef()), id => @isaacs/fs-minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(NodeJS), TsIdentSimple(ErrnoException)))[39m [34m[thread => 91, project => frontend, ms => 53026, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(@isaacs/fs-minipass) / TsExport() / Tree() / TsDeclClass(ReadStream) / TsMemberIndex() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => @isaacs/fs-minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify NodeJS.ErrnoException[39m [34m[thread => 91, project => frontend, ms => 53029, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(@isaacs/fs-minipass) / TsExport() / Tree() / TsDeclClass(ReadStream) / TsMemberIndex() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => @isaacs/fs-minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Buffer)))[39m [34m[thread => 91, project => frontend, ms => 53035, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(@isaacs/fs-minipass) / TsExport() / Tree() / TsDeclClass(ReadStream) / TsMemberIndex() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => @isaacs/fs-minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Buffer[39m [34m[thread => 91, project => frontend, ms => 53041, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(@isaacs/fs-minipass) / TsExport() / Tree() / TsDeclClass(ReadStream) / TsMemberIndex() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => @isaacs/fs-minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(NodeJS), TsIdentSimple(ErrnoException)))[39m [34m[thread => 91, project => frontend, ms => 53047, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(@isaacs/fs-minipass) / TsExport() / Tree() / TsDeclClass(ReadStream) / TsMemberIndex() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeRef()), id => @isaacs/fs-minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify NodeJS.ErrnoException[39m [34m[thread => 91, project => frontend, ms => 53057, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(@isaacs/fs-minipass) / TsExport() / Tree() / TsDeclClass(ReadStream) / TsMemberIndex() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeRef()), id => @isaacs/fs-minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Buffer)))[39m [34m[thread => 91, project => frontend, ms => 53061, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(@isaacs/fs-minipass) / TsExport() / Tree() / TsDeclClass(ReadStream) / TsMemberIndex() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeRef()), id => @isaacs/fs-minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Buffer[39m [34m[thread => 91, project => frontend, ms => 53065, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(@isaacs/fs-minipass) / TsExport() / Tree() / TsDeclClass(ReadStream) / TsMemberIndex() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeRef()), id => @isaacs/fs-minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(EE)))[39m [34m[thread => 91, project => frontend, ms => 53073, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(@isaacs/fs-minipass) / TsExport() / Tree() / TsDeclClass(WriteStream)), id => @isaacs/fs-minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify EE[39m [34m[thread => 91, project => frontend, ms => 53083, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(@isaacs/fs-minipass) / TsExport() / Tree() / TsDeclClass(WriteStream)), id => @isaacs/fs-minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Buffer)))[39m [34m[thread => 91, project => frontend, ms => 53087, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(@isaacs/fs-minipass) / TsExport() / Tree() / TsDeclClass(WriteStream) / TsMemberIndex() / TsTypeRef() / TsTypeRef()), id => @isaacs/fs-minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Buffer[39m [34m[thread => 91, project => frontend, ms => 53089, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(@isaacs/fs-minipass) / TsExport() / Tree() / TsDeclClass(WriteStream) / TsMemberIndex() / TsTypeRef() / TsTypeRef()), id => @isaacs/fs-minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(NodeJS), TsIdentSimple(ErrnoException)))[39m [34m[thread => 91, project => frontend, ms => 53092, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(@isaacs/fs-minipass) / TsExport() / Tree() / TsDeclClass(WriteStream) / TsMemberIndex() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeRef()), id => @isaacs/fs-minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify NodeJS.ErrnoException[39m [34m[thread => 91, project => frontend, ms => 53097, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(@isaacs/fs-minipass) / TsExport() / Tree() / TsDeclClass(WriteStream) / TsMemberIndex() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeRef()), id => @isaacs/fs-minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(NodeJS), TsIdentSimple(ErrnoException)))[39m [34m[thread => 91, project => frontend, ms => 53110, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(@isaacs/fs-minipass) / TsExport() / Tree() / TsDeclClass(WriteStream) / TsMemberIndex() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => @isaacs/fs-minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify NodeJS.ErrnoException[39m [34m[thread => 91, project => frontend, ms => 53115, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(@isaacs/fs-minipass) / TsExport() / Tree() / TsDeclClass(WriteStream) / TsMemberIndex() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => @isaacs/fs-minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(BufferEncoding)))[39m [34m[thread => 91, project => frontend, ms => 53121, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(@isaacs/fs-minipass) / TsExport() / Tree() / TsDeclClass(WriteStream) / TsMemberFunction(end) / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => @isaacs/fs-minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify BufferEncoding[39m [34m[thread => 91, project => frontend, ms => 53133, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(@isaacs/fs-minipass) / TsExport() / Tree() / TsDeclClass(WriteStream) / TsMemberFunction(end) / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => @isaacs/fs-minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Buffer)))[39m [34m[thread => 91, project => frontend, ms => 53137, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(@isaacs/fs-minipass) / TsExport() / Tree() / TsDeclClass(WriteStream) / TsMemberFunction(end) / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => @isaacs/fs-minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Buffer[39m [34m[thread => 91, project => frontend, ms => 53143, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(@isaacs/fs-minipass) / TsExport() / Tree() / TsDeclClass(WriteStream) / TsMemberFunction(end) / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => @isaacs/fs-minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(BufferEncoding)))[39m [34m[thread => 91, project => frontend, ms => 53146, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(@isaacs/fs-minipass) / TsExport() / Tree() / TsDeclClass(WriteStream) / TsMemberFunction(write) / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => @isaacs/fs-minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify BufferEncoding[39m [34m[thread => 91, project => frontend, ms => 53150, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(@isaacs/fs-minipass) / TsExport() / Tree() / TsDeclClass(WriteStream) / TsMemberFunction(write) / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => @isaacs/fs-minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Buffer)))[39m [34m[thread => 91, project => frontend, ms => 53155, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(@isaacs/fs-minipass) / TsExport() / Tree() / TsDeclClass(WriteStream) / TsMemberFunction(write) / TsFunSig() / TsFunParam() / TsTypeRef()), id => @isaacs/fs-minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Buffer[39m [34m[thread => 91, project => frontend, ms => 53166, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(@isaacs/fs-minipass) / TsExport() / Tree() / TsDeclClass(WriteStream) / TsMemberFunction(write) / TsFunSig() / TsFunParam() / TsTypeRef()), id => @isaacs/fs-minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Buffer)))[39m [34m[thread => 91, project => frontend, ms => 53169, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(@isaacs/fs-minipass) / TsExport() / Tree() / TsDeclClass(WriteStream) / TsMemberIndex() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeRef()), id => @isaacs/fs-minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Buffer[39m [34m[thread => 91, project => frontend, ms => 53172, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(@isaacs/fs-minipass) / TsExport() / Tree() / TsDeclClass(WriteStream) / TsMemberIndex() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeRef()), id => @isaacs/fs-minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(NodeJS), TsIdentSimple(ErrnoException)))[39m [34m[thread => 91, project => frontend, ms => 53175, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(@isaacs/fs-minipass) / TsExport() / Tree() / TsDeclClass(WriteStream) / TsMemberIndex() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => @isaacs/fs-minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify NodeJS.ErrnoException[39m [34m[thread => 91, project => frontend, ms => 53177, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(@isaacs/fs-minipass) / TsExport() / Tree() / TsDeclClass(WriteStream) / TsMemberIndex() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => @isaacs/fs-minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Buffer)))[39m [34m[thread => 91, project => frontend, ms => 53181, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(@isaacs/fs-minipass) / TsExport() / Tree() / TsDeclClass(WriteStreamSync) / TsMemberIndex() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeRef()), id => @isaacs/fs-minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Buffer[39m [34m[thread => 91, project => frontend, ms => 53183, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(@isaacs/fs-minipass) / TsExport() / Tree() / TsDeclClass(WriteStreamSync) / TsMemberIndex() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeRef()), id => @isaacs/fs-minipass][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mPhase1ReadTypescript.scala:140[39m  [94mCouldn't resolve inferred dependency node[39m [34m[thread => 91, project => frontend, ms => 53345, phase => typescript, id => minizlib][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(NodeJS), TsIdentSimple(ErrnoException)))[39m [34m[thread => 91, project => frontend, ms => 53357, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minizlib) / TsExport() / Tree() / TsDeclClass(ZlibError) / TsMemberFunction(constructor) / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => minizlib][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify NodeJS.ErrnoException[39m [34m[thread => 91, project => frontend, ms => 53359, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minizlib) / TsExport() / Tree() / TsDeclClass(ZlibError) / TsMemberFunction(constructor) / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => minizlib][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(realZlib), TsIdentSimple(Gzip)))[39m [34m[thread => 91, project => frontend, ms => 53364, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minizlib) / TsExport() / Tree() / TsDeclTypeAlias(ZlibHandle) / TsTypeUnion() / TsTypeRef()), id => minizlib][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify realZlib.Gzip[39m [34m[thread => 91, project => frontend, ms => 53368, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minizlib) / TsExport() / Tree() / TsDeclTypeAlias(ZlibHandle) / TsTypeUnion() / TsTypeRef()), id => minizlib][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(realZlib), TsIdentSimple(Gunzip)))[39m [34m[thread => 91, project => frontend, ms => 53377, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minizlib) / TsExport() / Tree() / TsDeclTypeAlias(ZlibHandle) / TsTypeUnion() / TsTypeRef()), id => minizlib][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify realZlib.Gunzip[39m [34m[thread => 91, project => frontend, ms => 53385, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minizlib) / TsExport() / Tree() / TsDeclTypeAlias(ZlibHandle) / TsTypeUnion() / TsTypeRef()), id => minizlib][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(realZlib), TsIdentSimple(Deflate)))[39m [34m[thread => 91, project => frontend, ms => 53401, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minizlib) / TsExport() / Tree() / TsDeclTypeAlias(ZlibHandle) / TsTypeUnion() / TsTypeRef()), id => minizlib][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify realZlib.Deflate[39m [34m[thread => 91, project => frontend, ms => 53403, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minizlib) / TsExport() / Tree() / TsDeclTypeAlias(ZlibHandle) / TsTypeUnion() / TsTypeRef()), id => minizlib][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(realZlib), TsIdentSimple(Inflate)))[39m [34m[thread => 91, project => frontend, ms => 53408, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minizlib) / TsExport() / Tree() / TsDeclTypeAlias(ZlibHandle) / TsTypeUnion() / TsTypeRef()), id => minizlib][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify realZlib.Inflate[39m [34m[thread => 91, project => frontend, ms => 53419, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minizlib) / TsExport() / Tree() / TsDeclTypeAlias(ZlibHandle) / TsTypeUnion() / TsTypeRef()), id => minizlib][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(realZlib), TsIdentSimple(DeflateRaw)))[39m [34m[thread => 91, project => frontend, ms => 53556, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minizlib) / TsExport() / Tree() / TsDeclTypeAlias(ZlibHandle) / TsTypeUnion() / TsTypeRef()), id => minizlib][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify realZlib.DeflateRaw[39m [34m[thread => 91, project => frontend, ms => 53561, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minizlib) / TsExport() / Tree() / TsDeclTypeAlias(ZlibHandle) / TsTypeUnion() / TsTypeRef()), id => minizlib][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(realZlib), TsIdentSimple(InflateRaw)))[39m [34m[thread => 91, project => frontend, ms => 53569, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minizlib) / TsExport() / Tree() / TsDeclTypeAlias(ZlibHandle) / TsTypeUnion() / TsTypeRef()), id => minizlib][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify realZlib.InflateRaw[39m [34m[thread => 91, project => frontend, ms => 53579, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minizlib) / TsExport() / Tree() / TsDeclTypeAlias(ZlibHandle) / TsTypeUnion() / TsTypeRef()), id => minizlib][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Buffer)))[39m [34m[thread => 91, project => frontend, ms => 53583, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minizlib) / TsExport() / Tree() / TsDeclClass(Gzip) / TsMemberIndex() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeIntersect() / TsTypeRef()), id => minizlib][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Buffer[39m [34m[thread => 91, project => frontend, ms => 53588, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minizlib) / TsExport() / Tree() / TsDeclClass(Gzip) / TsMemberIndex() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeIntersect() / TsTypeRef()), id => minizlib][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Buffer)))[39m [34m[thread => 91, project => frontend, ms => 53597, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minizlib) / TsDeclClass(ZlibBase) / TsTypeRef() / TsTypeRef()), id => minizlib][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Buffer[39m [34m[thread => 91, project => frontend, ms => 53635, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minizlib) / TsDeclClass(ZlibBase) / TsTypeRef() / TsTypeRef()), id => minizlib][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Buffer)))[39m [34m[thread => 91, project => frontend, ms => 53670, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minizlib) / TsDeclClass(ZlibBase) / TsMemberIndex() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeIntersect() / TsTypeRef()), id => minizlib][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Buffer[39m [34m[thread => 91, project => frontend, ms => 53675, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(minizlib) / TsDeclClass(ZlibBase) / TsMemberIndex() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeIntersect() / TsTypeRef()), id => minizlib][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mPhase1ReadTypescript.scala:99[39m  [94mdirectives: couldn't resolve TypesRef(node)[39m [34m[thread => 91, project => frontend, ms => 53880, phase => typescript, id => mkdirp][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mPhase1ReadTypescript.scala:99[39m  [94mdirectives: couldn't resolve TypesRef(node)[39m [34m[thread => 91, project => frontend, ms => 53882, phase => typescript, id => mkdirp][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mPhase1ReadTypescript.scala:140[39m  [94mCouldn't resolve inferred dependency node[39m [34m[thread => 91, project => frontend, ms => 53886, phase => typescript, id => mkdirp][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(NodeJS), TsIdentSimple(ErrnoException)))[39m [34m[thread => 91, project => frontend, ms => 53922, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(mkdirp/dist/mjs/opts-arg) / TsExport() / Tree() / TsDeclInterface(FsProvider) / TsMemberProperty(stat) / TsTypeUnion() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => mkdirp][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify NodeJS.ErrnoException[39m [34m[thread => 91, project => frontend, ms => 53927, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(mkdirp/dist/mjs/opts-arg) / TsExport() / Tree() / TsDeclInterface(FsProvider) / TsMemberProperty(stat) / TsTypeUnion() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => mkdirp][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Stats)))[39m [34m[thread => 91, project => frontend, ms => 53930, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(mkdirp/dist/mjs/opts-arg) / TsExport() / Tree() / TsDeclInterface(FsProvider) / TsMemberProperty(stat) / TsTypeUnion() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeRef()), id => mkdirp][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Stats[39m [34m[thread => 91, project => frontend, ms => 53932, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(mkdirp/dist/mjs/opts-arg) / TsExport() / Tree() / TsDeclInterface(FsProvider) / TsMemberProperty(stat) / TsTypeUnion() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeRef()), id => mkdirp][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(MakeDirectoryOptions)))[39m [34m[thread => 91, project => frontend, ms => 53935, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(mkdirp/dist/mjs/opts-arg) / TsExport() / Tree() / TsDeclInterface(FsProvider) / TsMemberProperty(mkdir) / TsTypeUnion() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeIntersect() / TsTypeRef()), id => mkdirp][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify MakeDirectoryOptions[39m [34m[thread => 91, project => frontend, ms => 53937, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(mkdirp/dist/mjs/opts-arg) / TsExport() / Tree() / TsDeclInterface(FsProvider) / TsMemberProperty(mkdir) / TsTypeUnion() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeIntersect() / TsTypeRef()), id => mkdirp][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(NodeJS), TsIdentSimple(ErrnoException)))[39m [34m[thread => 91, project => frontend, ms => 53944, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(mkdirp/dist/mjs/opts-arg) / TsExport() / Tree() / TsDeclInterface(FsProvider) / TsMemberProperty(mkdir) / TsTypeUnion() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => mkdirp][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify NodeJS.ErrnoException[39m [34m[thread => 91, project => frontend, ms => 53947, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(mkdirp/dist/mjs/opts-arg) / TsExport() / Tree() / TsDeclInterface(FsProvider) / TsMemberProperty(mkdir) / TsTypeUnion() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => mkdirp][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Stats)))[39m [34m[thread => 91, project => frontend, ms => 53950, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(mkdirp/dist/mjs/opts-arg) / TsExport() / Tree() / TsDeclInterface(FsProvider) / TsMemberProperty(statSync) / TsTypeUnion() / TsTypeFunction() / TsFunSig() / TsTypeRef()), id => mkdirp][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Stats[39m [34m[thread => 91, project => frontend, ms => 53953, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(mkdirp/dist/mjs/opts-arg) / TsExport() / Tree() / TsDeclInterface(FsProvider) / TsMemberProperty(statSync) / TsTypeUnion() / TsTypeFunction() / TsFunSig() / TsTypeRef()), id => mkdirp][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(MakeDirectoryOptions)))[39m [34m[thread => 91, project => frontend, ms => 53956, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(mkdirp/dist/mjs/opts-arg) / TsExport() / Tree() / TsDeclInterface(FsProvider) / TsMemberProperty(mkdirSync) / TsTypeUnion() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeIntersect() / TsTypeRef()), id => mkdirp][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify MakeDirectoryOptions[39m [34m[thread => 91, project => frontend, ms => 53963, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(mkdirp/dist/mjs/opts-arg) / TsExport() / Tree() / TsDeclInterface(FsProvider) / TsMemberProperty(mkdirSync) / TsTypeUnion() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeIntersect() / TsTypeRef()), id => mkdirp][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(MakeDirectoryOptions)))[39m [34m[thread => 91, project => frontend, ms => 53970, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(mkdirp/dist/mjs/opts-arg) / TsExport() / Tree() / TsDeclInterface(MkdirpOptionsResolved) / TsMemberFunction(mkdirAsync) / TsFunSig() / TsFunParam() / TsTypeIntersect() / TsTypeRef()), id => mkdirp][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify MakeDirectoryOptions[39m [34m[thread => 91, project => frontend, ms => 53976, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(mkdirp/dist/mjs/opts-arg) / TsExport() / Tree() / TsDeclInterface(MkdirpOptionsResolved) / TsMemberFunction(mkdirAsync) / TsFunSig() / TsFunParam() / TsTypeIntersect() / TsTypeRef()), id => mkdirp][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Stats)))[39m [34m[thread => 91, project => frontend, ms => 53980, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(mkdirp/dist/mjs/opts-arg) / TsExport() / Tree() / TsDeclInterface(MkdirpOptionsResolved) / TsMemberFunction(statAsync) / TsFunSig() / TsTypeRef() / TsTypeRef()), id => mkdirp][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Stats[39m [34m[thread => 91, project => frontend, ms => 53984, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(mkdirp/dist/mjs/opts-arg) / TsExport() / Tree() / TsDeclInterface(MkdirpOptionsResolved) / TsMemberFunction(statAsync) / TsFunSig() / TsTypeRef() / TsTypeRef()), id => mkdirp][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(NodeJS), TsIdentSimple(ErrnoException)))[39m [34m[thread => 91, project => frontend, ms => 53990, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(mkdirp/dist/mjs/opts-arg) / TsExport() / Tree() / TsDeclInterface(MkdirpOptionsResolved) / TsMemberFunction(stat) / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => mkdirp][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify NodeJS.ErrnoException[39m [34m[thread => 91, project => frontend, ms => 53999, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(mkdirp/dist/mjs/opts-arg) / TsExport() / Tree() / TsDeclInterface(MkdirpOptionsResolved) / TsMemberFunction(stat) / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => mkdirp][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Stats)))[39m [34m[thread => 91, project => frontend, ms => 54004, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(mkdirp/dist/mjs/opts-arg) / TsExport() / Tree() / TsDeclInterface(MkdirpOptionsResolved) / TsMemberFunction(stat) / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeRef()), id => mkdirp][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Stats[39m [34m[thread => 91, project => frontend, ms => 54007, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(mkdirp/dist/mjs/opts-arg) / TsExport() / Tree() / TsDeclInterface(MkdirpOptionsResolved) / TsMemberFunction(stat) / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeRef()), id => mkdirp][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(MakeDirectoryOptions)))[39m [34m[thread => 91, project => frontend, ms => 54012, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(mkdirp/dist/mjs/opts-arg) / TsExport() / Tree() / TsDeclInterface(MkdirpOptionsResolved) / TsMemberFunction(mkdir) / TsFunSig() / TsFunParam() / TsTypeIntersect() / TsTypeRef()), id => mkdirp][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify MakeDirectoryOptions[39m [34m[thread => 91, project => frontend, ms => 54019, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(mkdirp/dist/mjs/opts-arg) / TsExport() / Tree() / TsDeclInterface(MkdirpOptionsResolved) / TsMemberFunction(mkdir) / TsFunSig() / TsFunParam() / TsTypeIntersect() / TsTypeRef()), id => mkdirp][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(NodeJS), TsIdentSimple(ErrnoException)))[39m [34m[thread => 91, project => frontend, ms => 54022, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(mkdirp/dist/mjs/opts-arg) / TsExport() / Tree() / TsDeclInterface(MkdirpOptionsResolved) / TsMemberFunction(mkdir) / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => mkdirp][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify NodeJS.ErrnoException[39m [34m[thread => 91, project => frontend, ms => 54024, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(mkdirp/dist/mjs/opts-arg) / TsExport() / Tree() / TsDeclInterface(MkdirpOptionsResolved) / TsMemberFunction(mkdir) / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => mkdirp][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Stats)))[39m [34m[thread => 91, project => frontend, ms => 54026, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(mkdirp/dist/mjs/opts-arg) / TsExport() / Tree() / TsDeclInterface(MkdirpOptionsResolved) / TsMemberFunction(statSync) / TsFunSig() / TsTypeRef()), id => mkdirp][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Stats[39m [34m[thread => 91, project => frontend, ms => 54028, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(mkdirp/dist/mjs/opts-arg) / TsExport() / Tree() / TsDeclInterface(MkdirpOptionsResolved) / TsMemberFunction(statSync) / TsFunSig() / TsTypeRef()), id => mkdirp][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(MakeDirectoryOptions)))[39m [34m[thread => 91, project => frontend, ms => 54032, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(mkdirp/dist/mjs/opts-arg) / TsExport() / Tree() / TsDeclInterface(MkdirpOptionsResolved) / TsMemberFunction(mkdirSync) / TsFunSig() / TsFunParam() / TsTypeIntersect() / TsTypeRef()), id => mkdirp][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify MakeDirectoryOptions[39m [34m[thread => 91, project => frontend, ms => 54034, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(mkdirp/dist/mjs/opts-arg) / TsExport() / Tree() / TsDeclInterface(MkdirpOptionsResolved) / TsMemberFunction(mkdirSync) / TsFunSig() / TsFunParam() / TsTypeIntersect() / TsTypeRef()), id => mkdirp][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(MakeDirectoryOptions)))[39m [34m[thread => 91, project => frontend, ms => 54039, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(mkdirp/dist/mjs/opts-arg) / TsDeclInterface(Options) / TsMemberProperty(mkdirAsync) / TsTypeUnion() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeIntersect() / TsTypeRef()), id => mkdirp][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify MakeDirectoryOptions[39m [34m[thread => 91, project => frontend, ms => 54043, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(mkdirp/dist/mjs/opts-arg) / TsDeclInterface(Options) / TsMemberProperty(mkdirAsync) / TsTypeUnion() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeIntersect() / TsTypeRef()), id => mkdirp][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Stats)))[39m [34m[thread => 91, project => frontend, ms => 54048, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(mkdirp/dist/mjs/opts-arg) / TsDeclInterface(Options) / TsMemberProperty(statAsync) / TsTypeUnion() / TsTypeFunction() / TsFunSig() / TsTypeRef() / TsTypeRef()), id => mkdirp][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Stats[39m [34m[thread => 91, project => frontend, ms => 54053, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(mkdirp/dist/mjs/opts-arg) / TsDeclInterface(Options) / TsMemberProperty(statAsync) / TsTypeUnion() / TsTypeFunction() / TsFunSig() / TsTypeRef() / TsTypeRef()), id => mkdirp][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Buffer)))[39m [34m[thread => 91, project => frontend, ms => 54264, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/header) / TsExport() / Tree() / TsDeclClass(Header) / TsMemberProperty(block) / TsTypeUnion() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Buffer[39m [34m[thread => 91, project => frontend, ms => 54267, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/header) / TsExport() / Tree() / TsDeclClass(Header) / TsMemberProperty(block) / TsTypeUnion() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Buffer)))[39m [34m[thread => 91, project => frontend, ms => 54270, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/header) / TsExport() / Tree() / TsDeclClass(Header) / TsMemberFunction(constructor) / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Buffer[39m [34m[thread => 91, project => frontend, ms => 54272, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/header) / TsExport() / Tree() / TsDeclClass(Header) / TsMemberFunction(constructor) / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Buffer)))[39m [34m[thread => 91, project => frontend, ms => 54279, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/header) / TsExport() / Tree() / TsDeclClass(Header) / TsMemberFunction(decode) / TsFunSig() / TsFunParam() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Buffer[39m [34m[thread => 91, project => frontend, ms => 54282, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/header) / TsExport() / Tree() / TsDeclClass(Header) / TsMemberFunction(decode) / TsFunSig() / TsFunParam() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Buffer)))[39m [34m[thread => 91, project => frontend, ms => 54288, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/header) / TsExport() / Tree() / TsDeclClass(Header) / TsMemberFunction(encode) / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Buffer[39m [34m[thread => 91, project => frontend, ms => 54296, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/header) / TsExport() / Tree() / TsDeclClass(Header) / TsMemberFunction(encode) / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Buffer)))[39m [34m[thread => 91, project => frontend, ms => 54302, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/large-numbers) / TsExport() / Tree() / TsDeclFunction(encode) / TsFunSig() / TsFunParam() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Buffer[39m [34m[thread => 91, project => frontend, ms => 54306, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/large-numbers) / TsExport() / Tree() / TsDeclFunction(encode) / TsFunSig() / TsFunParam() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Buffer)))[39m [34m[thread => 91, project => frontend, ms => 54308, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/large-numbers) / TsExport() / Tree() / TsDeclFunction(encode) / TsFunSig() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Buffer[39m [34m[thread => 91, project => frontend, ms => 54311, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/large-numbers) / TsExport() / Tree() / TsDeclFunction(encode) / TsFunSig() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Buffer)))[39m [34m[thread => 91, project => frontend, ms => 54316, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/large-numbers) / TsExport() / Tree() / TsDeclFunction(parse) / TsFunSig() / TsFunParam() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Buffer[39m [34m[thread => 91, project => frontend, ms => 54318, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/large-numbers) / TsExport() / Tree() / TsDeclFunction(parse) / TsFunSig() / TsFunParam() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(NodeJS), TsIdentSimple(ErrnoException)))[39m [34m[thread => 91, project => frontend, ms => 54355, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/mkdir) / TsExport() / Tree() / TsDeclTypeAlias(MkdirError) / TsTypeUnion() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify NodeJS.ErrnoException[39m [34m[thread => 91, project => frontend, ms => 54360, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/mkdir) / TsExport() / Tree() / TsDeclTypeAlias(MkdirError) / TsTypeUnion() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Stats)))[39m [34m[thread => 91, project => frontend, ms => 54370, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/options) / TsExport() / Tree() / TsDeclInterface(TarOptions) / TsMemberProperty(filter) / TsTypeUnion() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Stats[39m [34m[thread => 91, project => frontend, ms => 54377, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/options) / TsExport() / Tree() / TsDeclInterface(TarOptions) / TsMemberProperty(filter) / TsTypeUnion() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Stats)))[39m [34m[thread => 91, project => frontend, ms => 54387, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/options) / TsExport() / Tree() / TsDeclInterface(TarOptions) / TsMemberProperty(statCache) / TsTypeUnion() / TsTypeRef() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Stats[39m [34m[thread => 91, project => frontend, ms => 54389, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/options) / TsExport() / Tree() / TsDeclInterface(TarOptions) / TsMemberProperty(statCache) / TsTypeUnion() / TsTypeRef() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Stats)))[39m [34m[thread => 91, project => frontend, ms => 54395, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/pack) / TsExport() / Tree() / TsDeclClass(PackJob) / TsMemberProperty(stat) / TsTypeUnion() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Stats[39m [34m[thread => 91, project => frontend, ms => 54397, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/pack) / TsExport() / Tree() / TsDeclClass(PackJob) / TsMemberProperty(stat) / TsTypeUnion() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Buffer)))[39m [34m[thread => 91, project => frontend, ms => 54402, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/pack) / TsExport() / Tree() / TsDeclClass(Pack) / TsTypeRef() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Buffer[39m [34m[thread => 91, project => frontend, ms => 54404, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/pack) / TsExport() / Tree() / TsDeclClass(Pack) / TsTypeRef() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Buffer)))[39m [34m[thread => 91, project => frontend, ms => 54407, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/pack) / TsExport() / Tree() / TsDeclClass(Pack) / TsTypeRef() / TsTypeRef() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Buffer[39m [34m[thread => 91, project => frontend, ms => 54411, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/pack) / TsExport() / Tree() / TsDeclClass(Pack) / TsTypeRef() / TsTypeRef() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Buffer)))[39m [34m[thread => 91, project => frontend, ms => 54417, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/pack) / TsExport() / Tree() / TsDeclClass(Pack) / TsMemberIndex() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Buffer[39m [34m[thread => 91, project => frontend, ms => 54420, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/pack) / TsExport() / Tree() / TsDeclClass(Pack) / TsMemberIndex() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Stats)))[39m [34m[thread => 91, project => frontend, ms => 54425, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/pack) / TsExport() / Tree() / TsDeclClass(Pack) / TsMemberIndex() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Stats[39m [34m[thread => 91, project => frontend, ms => 54428, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/pack) / TsExport() / Tree() / TsDeclClass(Pack) / TsMemberIndex() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(EE)))[39m [34m[thread => 91, project => frontend, ms => 54436, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/parse) / TsExport() / Tree() / TsDeclClass(Parser)), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify EE[39m [34m[thread => 91, project => frontend, ms => 54439, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/parse) / TsExport() / Tree() / TsDeclClass(Parser)), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Buffer)))[39m [34m[thread => 91, project => frontend, ms => 54447, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/parse) / TsExport() / Tree() / TsDeclClass(Parser) / TsMemberIndex() / TsTypeUnion() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Buffer[39m [34m[thread => 91, project => frontend, ms => 54449, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/parse) / TsExport() / Tree() / TsDeclClass(Parser) / TsMemberIndex() / TsTypeUnion() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Buffer)))[39m [34m[thread => 91, project => frontend, ms => 54458, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/parse) / TsExport() / Tree() / TsDeclClass(Parser) / TsMemberIndex() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Buffer[39m [34m[thread => 91, project => frontend, ms => 54461, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/parse) / TsExport() / Tree() / TsDeclClass(Parser) / TsMemberIndex() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Buffer)))[39m [34m[thread => 91, project => frontend, ms => 54470, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/parse) / TsExport() / Tree() / TsDeclClass(Parser) / TsMemberIndex() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Buffer[39m [34m[thread => 91, project => frontend, ms => 54475, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/parse) / TsExport() / Tree() / TsDeclClass(Parser) / TsMemberIndex() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Buffer)))[39m [34m[thread => 91, project => frontend, ms => 54479, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/parse) / TsExport() / Tree() / TsDeclClass(Parser) / TsMemberIndex() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Buffer[39m [34m[thread => 91, project => frontend, ms => 54482, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/parse) / TsExport() / Tree() / TsDeclClass(Parser) / TsMemberIndex() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(BufferEncoding)))[39m [34m[thread => 91, project => frontend, ms => 54489, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/parse) / TsExport() / Tree() / TsDeclClass(Parser) / TsMemberFunction(write) / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify BufferEncoding[39m [34m[thread => 91, project => frontend, ms => 54492, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/parse) / TsExport() / Tree() / TsDeclClass(Parser) / TsMemberFunction(write) / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Buffer)))[39m [34m[thread => 91, project => frontend, ms => 54502, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/parse) / TsExport() / Tree() / TsDeclClass(Parser) / TsMemberIndex() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Buffer[39m [34m[thread => 91, project => frontend, ms => 54504, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/parse) / TsExport() / Tree() / TsDeclClass(Parser) / TsMemberIndex() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Buffer)))[39m [34m[thread => 91, project => frontend, ms => 54516, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/parse) / TsExport() / Tree() / TsDeclClass(Parser) / TsMemberIndex() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Buffer[39m [34m[thread => 91, project => frontend, ms => 54525, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/parse) / TsExport() / Tree() / TsDeclClass(Parser) / TsMemberIndex() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Buffer)))[39m [34m[thread => 91, project => frontend, ms => 54529, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/parse) / TsExport() / Tree() / TsDeclClass(Parser) / TsMemberIndex() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Buffer[39m [34m[thread => 91, project => frontend, ms => 54532, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/parse) / TsExport() / Tree() / TsDeclClass(Parser) / TsMemberIndex() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Buffer)))[39m [34m[thread => 91, project => frontend, ms => 54535, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/parse) / TsExport() / Tree() / TsDeclClass(Parser) / TsMemberFunction(end) / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Buffer[39m [34m[thread => 91, project => frontend, ms => 54537, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/parse) / TsExport() / Tree() / TsDeclClass(Parser) / TsMemberFunction(end) / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(BufferEncoding)))[39m [34m[thread => 91, project => frontend, ms => 54543, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/parse) / TsExport() / Tree() / TsDeclClass(Parser) / TsMemberFunction(end) / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify BufferEncoding[39m [34m[thread => 91, project => frontend, ms => 54547, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/parse) / TsExport() / Tree() / TsDeclClass(Parser) / TsMemberFunction(end) / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Buffer)))[39m [34m[thread => 91, project => frontend, ms => 54553, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/pax) / TsExport() / Tree() / TsDeclClass(Pax) / TsMemberFunction(encode) / TsFunSig() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Buffer[39m [34m[thread => 91, project => frontend, ms => 54557, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/pax) / TsExport() / Tree() / TsDeclClass(Pax) / TsMemberFunction(encode) / TsFunSig() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Buffer)))[39m [34m[thread => 91, project => frontend, ms => 54589, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/read-entry) / TsExport() / Tree() / TsDeclClass(ReadEntry) / TsTypeRef() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Buffer[39m [34m[thread => 91, project => frontend, ms => 54594, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/read-entry) / TsExport() / Tree() / TsDeclClass(ReadEntry) / TsTypeRef() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Buffer)))[39m [34m[thread => 91, project => frontend, ms => 54598, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/read-entry) / TsExport() / Tree() / TsDeclClass(ReadEntry) / TsTypeRef() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Buffer[39m [34m[thread => 91, project => frontend, ms => 54600, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/read-entry) / TsExport() / Tree() / TsDeclClass(ReadEntry) / TsTypeRef() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Buffer)))[39m [34m[thread => 91, project => frontend, ms => 54615, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/read-entry) / TsExport() / Tree() / TsDeclClass(ReadEntry) / TsMemberFunction(write) / TsFunSig() / TsFunParam() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Buffer[39m [34m[thread => 91, project => frontend, ms => 54620, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/read-entry) / TsExport() / Tree() / TsDeclClass(ReadEntry) / TsMemberFunction(write) / TsFunSig() / TsFunParam() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Stats)))[39m [34m[thread => 91, project => frontend, ms => 54632, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/unpack) / TsExport() / Tree() / TsDeclClass(Unpack) / TsMemberIndex() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Stats[39m [34m[thread => 91, project => frontend, ms => 54635, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/unpack) / TsExport() / Tree() / TsDeclClass(Unpack) / TsMemberIndex() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Buffer)))[39m [34m[thread => 91, project => frontend, ms => 54642, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/warn-method) / TsExport() / Tree() / TsDeclTypeAlias(WarnEvent) / TsTypeParam() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Buffer[39m [34m[thread => 91, project => frontend, ms => 54644, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/warn-method) / TsExport() / Tree() / TsDeclTypeAlias(WarnEvent) / TsTypeParam() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Buffer)))[39m [34m[thread => 91, project => frontend, ms => 54657, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/write-entry) / TsExport() / Tree() / TsDeclClass(WriteEntry) / TsTypeRef() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Buffer[39m [34m[thread => 91, project => frontend, ms => 54659, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/write-entry) / TsExport() / Tree() / TsDeclClass(WriteEntry) / TsTypeRef() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Buffer)))[39m [34m[thread => 91, project => frontend, ms => 54674, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/write-entry) / TsExport() / Tree() / TsDeclClass(WriteEntry) / TsMemberProperty(buf) / TsTypeUnion() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Buffer[39m [34m[thread => 91, project => frontend, ms => 54692, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/write-entry) / TsExport() / Tree() / TsDeclClass(WriteEntry) / TsMemberProperty(buf) / TsTypeUnion() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Stats)))[39m [34m[thread => 91, project => frontend, ms => 54702, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/write-entry) / TsExport() / Tree() / TsDeclClass(WriteEntry) / TsMemberProperty(stat) / TsTypeUnion() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Stats[39m [34m[thread => 91, project => frontend, ms => 54705, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/write-entry) / TsExport() / Tree() / TsDeclClass(WriteEntry) / TsMemberProperty(stat) / TsTypeUnion() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Stats)))[39m [34m[thread => 91, project => frontend, ms => 54708, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/write-entry) / TsExport() / Tree() / TsDeclClass(WriteEntry) / TsMemberIndex() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Stats[39m [34m[thread => 91, project => frontend, ms => 54712, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/write-entry) / TsExport() / Tree() / TsDeclClass(WriteEntry) / TsMemberIndex() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(NodeJS), TsIdentSimple(ErrnoException)))[39m [34m[thread => 91, project => frontend, ms => 54720, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/write-entry) / TsExport() / Tree() / TsDeclClass(WriteEntry) / TsMemberIndex() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify NodeJS.ErrnoException[39m [34m[thread => 91, project => frontend, ms => 54725, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/write-entry) / TsExport() / Tree() / TsDeclClass(WriteEntry) / TsMemberIndex() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Buffer)))[39m [34m[thread => 91, project => frontend, ms => 54728, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/write-entry) / TsExport() / Tree() / TsDeclClass(WriteEntry) / TsMemberFunction(write) / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Buffer[39m [34m[thread => 91, project => frontend, ms => 54732, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/write-entry) / TsExport() / Tree() / TsDeclClass(WriteEntry) / TsMemberFunction(write) / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Buffer)))[39m [34m[thread => 91, project => frontend, ms => 54736, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/write-entry) / TsExport() / Tree() / TsDeclClass(WriteEntry) / TsMemberFunction(write) / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Buffer[39m [34m[thread => 91, project => frontend, ms => 54741, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/write-entry) / TsExport() / Tree() / TsDeclClass(WriteEntry) / TsMemberFunction(write) / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(BufferEncoding)))[39m [34m[thread => 91, project => frontend, ms => 54745, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/write-entry) / TsExport() / Tree() / TsDeclClass(WriteEntry) / TsMemberFunction(write) / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify BufferEncoding[39m [34m[thread => 91, project => frontend, ms => 54748, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/write-entry) / TsExport() / Tree() / TsDeclClass(WriteEntry) / TsMemberFunction(write) / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(NodeJS), TsIdentSimple(ErrnoException)))[39m [34m[thread => 91, project => frontend, ms => 54751, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/write-entry) / TsExport() / Tree() / TsDeclClass(WriteEntrySync) / TsMemberIndex() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify NodeJS.ErrnoException[39m [34m[thread => 91, project => frontend, ms => 54755, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/write-entry) / TsExport() / Tree() / TsDeclClass(WriteEntrySync) / TsMemberIndex() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeFunction() / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Buffer)))[39m [34m[thread => 91, project => frontend, ms => 54767, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/write-entry) / TsExport() / Tree() / TsDeclClass(WriteEntryTar) / TsTypeRef() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Buffer[39m [34m[thread => 91, project => frontend, ms => 54771, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/write-entry) / TsExport() / Tree() / TsDeclClass(WriteEntryTar) / TsTypeRef() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Buffer)))[39m [34m[thread => 91, project => frontend, ms => 54776, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/write-entry) / TsExport() / Tree() / TsDeclClass(WriteEntryTar) / TsTypeRef() / TsTypeUnion() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Buffer[39m [34m[thread => 91, project => frontend, ms => 54781, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/write-entry) / TsExport() / Tree() / TsDeclClass(WriteEntryTar) / TsTypeRef() / TsTypeUnion() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Buffer)))[39m [34m[thread => 91, project => frontend, ms => 54792, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/write-entry) / TsExport() / Tree() / TsDeclClass(WriteEntryTar) / TsMemberFunction(write) / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Buffer[39m [34m[thread => 91, project => frontend, ms => 54799, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/write-entry) / TsExport() / Tree() / TsDeclClass(WriteEntryTar) / TsMemberFunction(write) / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Buffer)))[39m [34m[thread => 91, project => frontend, ms => 54802, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/write-entry) / TsExport() / Tree() / TsDeclClass(WriteEntryTar) / TsMemberFunction(write) / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Buffer[39m [34m[thread => 91, project => frontend, ms => 54808, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/write-entry) / TsExport() / Tree() / TsDeclClass(WriteEntryTar) / TsMemberFunction(write) / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(BufferEncoding)))[39m [34m[thread => 91, project => frontend, ms => 54812, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/write-entry) / TsExport() / Tree() / TsDeclClass(WriteEntryTar) / TsMemberFunction(write) / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify BufferEncoding[39m [34m[thread => 91, project => frontend, ms => 54822, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/write-entry) / TsExport() / Tree() / TsDeclClass(WriteEntryTar) / TsMemberFunction(write) / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Buffer)))[39m [34m[thread => 91, project => frontend, ms => 54826, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/write-entry) / TsExport() / Tree() / TsDeclClass(WriteEntryTar) / TsMemberFunction(end) / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Buffer[39m [34m[thread => 91, project => frontend, ms => 54828, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/write-entry) / TsExport() / Tree() / TsDeclClass(WriteEntryTar) / TsMemberFunction(end) / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(Buffer)))[39m [34m[thread => 91, project => frontend, ms => 54831, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/write-entry) / TsExport() / Tree() / TsDeclClass(WriteEntryTar) / TsMemberFunction(end) / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify Buffer[39m [34m[thread => 91, project => frontend, ms => 54834, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/write-entry) / TsExport() / Tree() / TsDeclClass(WriteEntryTar) / TsMemberFunction(end) / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mTsTreeScope.scala:84[39m  [94mCannot resolve TsQIdent(IArray(TsIdentSimple(BufferEncoding)))[39m [34m[thread => 91, project => frontend, ms => 54836, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/write-entry) / TsExport() / Tree() / TsDeclClass(WriteEntryTar) / TsMemberFunction(end) / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mQualifyReferences.scala:72[39m [34mmsg[39m [94mCouldn't qualify BufferEncoding[39m [34m[thread => 91, project => frontend, ms => 54844, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/write-entry) / TsExport() / Tree() / TsDeclClass(WriteEntryTar) / TsMemberFunction(end) / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mResolveTypeQueries.scala:232[39m [34mmsg[39m [94mCouldn't resolve typeof opt[39m [34m[thread => 91, project => frontend, ms => 54865, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/make-command) / TsExport() / Tree() / TsDeclTypeAlias(TarCommand) / TsTypeObject() / TsMemberCall() / TsFunSig() / TsTypeConditional() / TsTypeExtends() / TsTypeQuery()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mResolveTypeQueries.scala:232[39m [34mmsg[39m [94mCouldn't resolve typeof opt[39m [34m[thread => 91, project => frontend, ms => 54884, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/make-command) / TsExport() / Tree() / TsDeclTypeAlias(TarCommand) / TsTypeObject() / TsMemberCall() / TsFunSig() / TsTypeConditional() / TsTypeConditional() / TsTypeExtends() / TsTypeQuery()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mResolveTypeQueries.scala:232[39m [34mmsg[39m [94mCouldn't resolve typeof opt[39m [34m[thread => 91, project => frontend, ms => 54890, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/make-command) / TsExport() / Tree() / TsDeclTypeAlias(TarCommand) / TsTypeObject() / TsMemberCall() / TsFunSig() / TsTypeConditional() / TsTypeExtends() / TsTypeQuery()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mResolveTypeQueries.scala:232[39m [34mmsg[39m [94mCouldn't resolve typeof opt[39m [34m[thread => 91, project => frontend, ms => 54893, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/make-command) / TsExport() / Tree() / TsDeclTypeAlias(TarCommand) / TsTypeObject() / TsMemberCall() / TsFunSig() / TsTypeConditional() / TsTypeConditional() / TsTypeExtends() / TsTypeQuery()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mResolveTypeQueries.scala:232[39m [34mmsg[39m [94mCouldn't resolve typeof opt[39m [34m[thread => 91, project => frontend, ms => 54900, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/make-command) / TsExport() / Tree() / TsDeclTypeAlias(TarCommand) / TsTypeObject() / TsMemberCall() / TsFunSig() / TsTypeConditional() / TsTypeExtends() / TsTypeQuery()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mResolveTypeQueries.scala:232[39m [34mmsg[39m [94mCouldn't resolve typeof opt[39m [34m[thread => 91, project => frontend, ms => 54903, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/make-command) / TsExport() / Tree() / TsDeclTypeAlias(TarCommand) / TsTypeObject() / TsMemberCall() / TsFunSig() / TsTypeConditional() / TsTypeConditional() / TsTypeExtends() / TsTypeQuery()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mResolveTypeQueries.scala:232[39m [34mmsg[39m [94mCouldn't resolve typeof opt[39m [34m[thread => 91, project => frontend, ms => 54908, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/make-command) / TsExport() / Tree() / TsDeclTypeAlias(TarCommand) / TsTypeObject() / TsMemberCall() / TsFunSig() / TsTypeConditional() / TsTypeExtends() / TsTypeQuery()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mResolveTypeQueries.scala:232[39m [34mmsg[39m [94mCouldn't resolve typeof opt[39m [34m[thread => 91, project => frontend, ms => 54910, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/make-command) / TsExport() / Tree() / TsDeclTypeAlias(TarCommand) / TsTypeObject() / TsMemberCall() / TsFunSig() / TsTypeConditional() / TsTypeConditional() / TsTypeExtends() / TsTypeQuery()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mResolveTypeQueries.scala:232[39m [34mmsg[39m [94mCouldn't resolve typeof opt[39m [34m[thread => 91, project => frontend, ms => 54927, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/make-command) / TsExport() / Tree() / TsDeclTypeAlias(TarCommand) / TsTypeObject() / TsMemberCall() / TsFunSig() / TsTypeConditional() / TsTypeExtends() / TsTypeQuery()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mResolveTypeQueries.scala:232[39m [34mmsg[39m [94mCouldn't resolve typeof opt[39m [34m[thread => 91, project => frontend, ms => 54930, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/make-command) / TsExport() / Tree() / TsDeclTypeAlias(TarCommand) / TsTypeObject() / TsMemberCall() / TsFunSig() / TsTypeConditional() / TsTypeConditional() / TsTypeExtends() / TsTypeQuery()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mResolveTypeQueries.scala:232[39m [34mmsg[39m [94mCouldn't resolve typeof opt[39m [34m[thread => 91, project => frontend, ms => 54939, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/make-command) / TsExport() / Tree() / TsDeclTypeAlias(TarCommand) / TsTypeObject() / TsMemberCall() / TsFunSig() / TsTypeConditional() / TsTypeExtends() / TsTypeQuery()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mResolveTypeQueries.scala:232[39m [34mmsg[39m [94mCouldn't resolve typeof opt[39m [34m[thread => 91, project => frontend, ms => 54942, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/make-command) / TsExport() / Tree() / TsDeclTypeAlias(TarCommand) / TsTypeObject() / TsMemberCall() / TsFunSig() / TsTypeConditional() / TsTypeConditional() / TsTypeExtends() / TsTypeQuery()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mResolveTypeQueries.scala:232[39m [34mmsg[39m [94mCouldn't resolve typeof opt[39m [34m[thread => 91, project => frontend, ms => 54948, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/make-command) / TsExport() / Tree() / TsDeclTypeAlias(TarCommand) / TsTypeObject() / TsMemberCall() / TsFunSig() / TsTypeConditional() / TsTypeExtends() / TsTypeQuery()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mResolveTypeQueries.scala:232[39m [34mmsg[39m [94mCouldn't resolve typeof opt[39m [34m[thread => 91, project => frontend, ms => 54951, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/make-command) / TsExport() / Tree() / TsDeclTypeAlias(TarCommand) / TsTypeObject() / TsMemberCall() / TsFunSig() / TsTypeConditional() / TsTypeConditional() / TsTypeExtends() / TsTypeQuery()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mResolveTypeQueries.scala:232[39m [34mmsg[39m [94mCouldn't resolve typeof opt[39m [34m[thread => 91, project => frontend, ms => 54960, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/make-command) / TsExport() / Tree() / TsDeclTypeAlias(TarCommand) / TsTypeObject() / TsMemberCall() / TsFunSig() / TsTypeConditional() / TsTypeExtends() / TsTypeQuery()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mResolveTypeQueries.scala:232[39m [34mmsg[39m [94mCouldn't resolve typeof opt[39m [34m[thread => 91, project => frontend, ms => 54964, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/make-command) / TsExport() / Tree() / TsDeclTypeAlias(TarCommand) / TsTypeObject() / TsMemberCall() / TsFunSig() / TsTypeConditional() / TsTypeConditional() / TsTypeExtends() / TsTypeQuery()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mResolveTypeQueries.scala:232[39m [34mmsg[39m [94mCouldn't resolve typeof opt[39m [34m[thread => 91, project => frontend, ms => 54967, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/make-command) / TsExport() / Tree() / TsDeclTypeAlias(TarCommand) / TsTypeObject() / TsMemberCall() / TsFunSig() / TsTypeConditional() / TsTypeExtends() / TsTypeQuery()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mResolveTypeQueries.scala:232[39m [34mmsg[39m [94mCouldn't resolve typeof opt[39m [34m[thread => 91, project => frontend, ms => 54970, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/make-command) / TsExport() / Tree() / TsDeclTypeAlias(TarCommand) / TsTypeObject() / TsMemberCall() / TsFunSig() / TsTypeConditional() / TsTypeConditional() / TsTypeExtends() / TsTypeQuery()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mResolveTypeQueries.scala:232[39m [34mmsg[39m [94mCouldn't resolve typeof opt[39m [34m[thread => 91, project => frontend, ms => 54974, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/make-command) / TsExport() / Tree() / TsDeclTypeAlias(TarCommand) / TsTypeObject() / TsMemberCall() / TsFunSig() / TsTypeConditional() / TsTypeExtends() / TsTypeQuery()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mResolveTypeQueries.scala:232[39m [34mmsg[39m [94mCouldn't resolve typeof opt[39m [34m[thread => 91, project => frontend, ms => 54979, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/make-command) / TsExport() / Tree() / TsDeclTypeAlias(TarCommand) / TsTypeObject() / TsMemberCall() / TsFunSig() / TsTypeConditional() / TsTypeConditional() / TsTypeExtends() / TsTypeQuery()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mResolveTypeQueries.scala:232[39m [34mmsg[39m [94mCouldn't resolve typeof opt[39m [34m[thread => 91, project => frontend, ms => 54982, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/make-command) / TsExport() / Tree() / TsDeclTypeAlias(TarCommand) / TsTypeObject() / TsMemberCall() / TsFunSig() / TsTypeConditional() / TsTypeConditional() / TsTypeConditional() / TsTypeExtends() / TsTypeQuery()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mResolveTypeQueries.scala:232[39m [34mmsg[39m [94mCouldn't resolve typeof opt[39m [34m[thread => 91, project => frontend, ms => 54985, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/make-command) / TsExport() / Tree() / TsDeclTypeAlias(TarCommand) / TsTypeObject() / TsMemberCall() / TsFunSig() / TsTypeConditional() / TsTypeConditional() / TsTypeExtends() / TsTypeQuery()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mResolveTypeQueries.scala:232[39m [34mmsg[39m [94mCouldn't resolve typeof opt[39m [34m[thread => 91, project => frontend, ms => 54988, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/make-command) / TsExport() / Tree() / TsDeclTypeAlias(TarCommand) / TsTypeObject() / TsMemberCall() / TsFunSig() / TsTypeConditional() / TsTypeConditional() / TsTypeConditional() / TsTypeExtends() / TsTypeQuery()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mResolveTypeQueries.scala:232[39m [34mmsg[39m [94mCouldn't resolve typeof opt[39m [34m[thread => 91, project => frontend, ms => 54996, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/make-command) / TsExport() / Tree() / TsDeclTypeAlias(TarCommand) / TsTypeObject() / TsMemberCall() / TsFunSig() / TsTypeConditional() / TsTypeConditional() / TsTypeConditional() / TsTypeConditional() / TsTypeExtends() / TsTypeQuery()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mResolveTypeQueries.scala:232[39m [34mmsg[39m [94mCouldn't resolve typeof opt[39m [34m[thread => 91, project => frontend, ms => 55000, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/make-command) / TsExport() / Tree() / TsDeclTypeAlias(TarCommand) / TsTypeObject() / TsMemberCall() / TsFunSig() / TsTypeConditional() / TsTypeConditional() / TsTypeConditional() / TsTypeExtends() / TsTypeQuery()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mResolveTypeQueries.scala:232[39m [34mmsg[39m [94mCouldn't resolve typeof opt[39m [34m[thread => 91, project => frontend, ms => 55002, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/make-command) / TsExport() / Tree() / TsDeclTypeAlias(TarCommand) / TsTypeObject() / TsMemberCall() / TsFunSig() / TsTypeConditional() / TsTypeConditional() / TsTypeConditional() / TsTypeConditional() / TsTypeExtends() / TsTypeQuery()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 55058, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(tar/dist/commonjs/parse) / TsDeclClass(Parser) / TsMemberFunction(write) / TsFunSig() / TsFunParam() / TsTypeUnion() / TsTypeRef()), id => tar][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 55909, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(nanoid/async) / TsDeclFunction(random) / TsFunSig() / TsTypeRef() / TsTypeRef()), id => nanoid][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 55916, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(nanoid) / TsDeclFunction(customRandom) / TsFunSig() / TsFunParam() / TsTypeFunction() / TsFunSig() / TsTypeRef()), id => nanoid][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 55922, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(nanoid) / TsDeclFunction(random) / TsFunSig() / TsTypeRef()), id => nanoid][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mReplaceExports.scala:55[39m  [94mDropping unexpected export in namespace TsExport(Comments(1),false,Named,Names(IArray((TsQIdent(IArray(TsIdentSimple(AtRule_))),Some(TsIdentSimple(default)))),None))[39m [34m[thread => 91, project => frontend, ms => 55975, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(postcss/lib/at-rule) / TsDeclNamespace(AtRule)), id => postcss][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mReplaceExports.scala:55[39m  [94mDropping unexpected export in namespace TsExport(Comments(1),false,Named,Names(IArray((TsQIdent(IArray(TsIdentSimple(Container_))),Some(TsIdentSimple(default)))),None))[39m [34m[thread => 91, project => frontend, ms => 55979, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(postcss/lib/container) / TsDeclNamespace(Container)), id => postcss][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mReplaceExports.scala:55[39m  [94mDropping unexpected export in namespace TsExport(Comments(1),false,Named,Names(IArray((TsQIdent(IArray(TsIdentSimple(Container_))),Some(TsIdentSimple(default)))),None))[39m [34m[thread => 91, project => frontend, ms => 55982, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(postcss/lib/container) / TsDeclNamespace(Container)), id => postcss][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mReplaceExports.scala:55[39m  [94mDropping unexpected export in namespace TsExport(Comments(1),false,Named,Names(IArray((TsQIdent(IArray(TsIdentSimple(Comment_))),Some(TsIdentSimple(default)))),None))[39m [34m[thread => 91, project => frontend, ms => 55985, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(postcss/lib/comment) / TsDeclNamespace(Comment)), id => postcss][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mReplaceExports.scala:55[39m  [94mDropping unexpected export in namespace TsExport(NoComments,false,Named,Names(IArray((TsQIdent(IArray(TsIdentSimple(Node))),Some(TsIdentSimple(default)))),None))[39m [34m[thread => 91, project => frontend, ms => 55988, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(postcss/lib/node) / TsDeclNamespace(Node)), id => postcss][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mReplaceExports.scala:55[39m  [94mDropping unexpected export in namespace TsExport(NoComments,false,Named,Names(IArray((TsQIdent(IArray(TsIdentSimple(Node))),Some(TsIdentSimple(default)))),None))[39m [34m[thread => 91, project => frontend, ms => 55993, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(postcss/lib/node) / TsDeclNamespace(Node)), id => postcss][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mReplaceExports.scala:55[39m  [94mDropping unexpected export in namespace TsExport(Comments(1),false,Named,Names(IArray((TsQIdent(IArray(TsIdentSimple(CssSyntaxError_))),Some(TsIdentSimple(default)))),None))[39m [34m[thread => 91, project => frontend, ms => 56003, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(postcss/lib/css-syntax-error) / TsDeclNamespace(CssSyntaxError)), id => postcss][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mReplaceExports.scala:55[39m  [94mDropping unexpected export in namespace TsExport(Comments(1),false,Named,Names(IArray((TsQIdent(IArray(TsIdentSimple(Declaration_))),Some(TsIdentSimple(default)))),None))[39m [34m[thread => 91, project => frontend, ms => 56006, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(postcss/lib/declaration) / TsDeclNamespace(Declaration)), id => postcss][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mReplaceExports.scala:55[39m  [94mDropping unexpected export in namespace TsExport(NoComments,false,Named,Names(IArray((TsQIdent(IArray(TsIdentSimple(AnyNode))),None), (TsQIdent(IArray(TsIdentSimple(AtRule))),None), (TsQIdent(IArray(TsIdentSimple(AtRuleProps))),None), (TsQIdent(IArray(TsIdentSimple(ChildNode))),None), (TsQIdent(IArray(TsIdentSimple(ChildProps))),None), (TsQIdent(IArray(TsIdentSimple(Comment))),None), (TsQIdent(IArray(TsIdentSimple(CommentProps))),None), (TsQIdent(IArray(TsIdentSimple(Container))),None), (TsQIdent(IArray(TsIdentSimple(ContainerProps))),None), (TsQIdent(IArray(TsIdentSimple(CssSyntaxError))),None), (TsQIdent(IArray(TsIdentSimple(Declaration))),None), (TsQIdent(IArray(TsIdentSimple(DeclarationProps))),None), (TsQIdent(IArray(TsIdentSimple(Document))),None), (TsQIdent(IArray(TsIdentSimple(DocumentProps))),None), (TsQIdent(IArray(TsIdentSimple(FilePosition))),None), (TsQIdent(IArray(TsIdentSimple(Input))),None), (TsQIdent(IArray(TsIdentSimple(LazyResult))),None), (TsQIdent(IArray(TsIdentSimple(list))),None), (TsQIdent(IArray(TsIdentSimple(Message))),None), (TsQIdent(IArray(TsIdentSimple(NewChild))),None), (TsQIdent(IArray(TsIdentSimple(Node))),None), (TsQIdent(IArray(TsIdentSimple(NodeErrorOptions))),None), (TsQIdent(IArray(TsIdentSimple(NodeProps))),None), (TsQIdent(IArray(TsIdentSimple(Position))),None), (TsQIdent(IArray(TsIdentSimple(Processor))),None), (TsQIdent(IArray(TsIdentSimple(Result))),None), (TsQIdent(IArray(TsIdentSimple(Root))),None), (TsQIdent(IArray(TsIdentSimple(RootProps))),None), (TsQIdent(IArray(TsIdentSimple(Rule))),None), (TsQIdent(IArray(TsIdentSimple(RuleProps))),None), (TsQIdent(IArray(TsIdentSimple(Source))),None), (TsQIdent(IArray(TsIdentSimple(Warning))),None), (TsQIdent(IArray(TsIdentSimple(WarningOptions))),None)),None))[39m [34m[thread => 91, project => frontend, ms => 56010, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(postcss) / TsDeclNamespace(postcss)), id => postcss][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mReplaceExports.scala:55[39m  [94mDropping unexpected export in namespace TsExport(NoComments,false,Named,Names(IArray((TsQIdent(IArray(TsIdentSimple(postcss))),Some(TsIdentSimple(default)))),None))[39m [34m[thread => 91, project => frontend, ms => 56016, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(postcss) / TsDeclNamespace(postcss)), id => postcss][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mReplaceExports.scala:55[39m  [94mDropping unexpected export in namespace TsExport(NoComments,false,Named,Names(IArray((TsQIdent(IArray(TsIdentSimple(AnyNode))),None), (TsQIdent(IArray(TsIdentSimple(AtRule))),None), (TsQIdent(IArray(TsIdentSimple(AtRuleProps))),None), (TsQIdent(IArray(TsIdentSimple(ChildNode))),None), (TsQIdent(IArray(TsIdentSimple(ChildProps))),None), (TsQIdent(IArray(TsIdentSimple(Comment))),None), (TsQIdent(IArray(TsIdentSimple(CommentProps))),None), (TsQIdent(IArray(TsIdentSimple(Container))),None), (TsQIdent(IArray(TsIdentSimple(ContainerProps))),None), (TsQIdent(IArray(TsIdentSimple(CssSyntaxError))),None), (TsQIdent(IArray(TsIdentSimple(Declaration))),None), (TsQIdent(IArray(TsIdentSimple(DeclarationProps))),None), (TsQIdent(IArray(TsIdentSimple(Document))),None), (TsQIdent(IArray(TsIdentSimple(DocumentProps))),None), (TsQIdent(IArray(TsIdentSimple(FilePosition))),None), (TsQIdent(IArray(TsIdentSimple(Input))),None), (TsQIdent(IArray(TsIdentSimple(LazyResult))),None), (TsQIdent(IArray(TsIdentSimple(list))),None), (TsQIdent(IArray(TsIdentSimple(Message))),None), (TsQIdent(IArray(TsIdentSimple(NewChild))),None), (TsQIdent(IArray(TsIdentSimple(Node))),None), (TsQIdent(IArray(TsIdentSimple(NodeErrorOptions))),None), (TsQIdent(IArray(TsIdentSimple(NodeProps))),None), (TsQIdent(IArray(TsIdentSimple(Position))),None), (TsQIdent(IArray(TsIdentSimple(Processor))),None), (TsQIdent(IArray(TsIdentSimple(Result))),None), (TsQIdent(IArray(TsIdentSimple(Root))),None), (TsQIdent(IArray(TsIdentSimple(RootProps))),None), (TsQIdent(IArray(TsIdentSimple(Rule))),None), (TsQIdent(IArray(TsIdentSimple(RuleProps))),None), (TsQIdent(IArray(TsIdentSimple(Source))),None), (TsQIdent(IArray(TsIdentSimple(Warning))),None), (TsQIdent(IArray(TsIdentSimple(WarningOptions))),None)),None))[39m [34m[thread => 91, project => frontend, ms => 56025, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(postcss) / TsDeclNamespace(postcss)), id => postcss][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mReplaceExports.scala:55[39m  [94mDropping unexpected export in namespace TsExport(NoComments,false,Named,Names(IArray((TsQIdent(IArray(TsIdentSimple(postcss))),Some(TsIdentSimple(default)))),None))[39m [34m[thread => 91, project => frontend, ms => 56031, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(postcss) / TsDeclNamespace(postcss)), id => postcss][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mReplaceExports.scala:55[39m  [94mDropping unexpected export in namespace TsExport(Comments(1),false,Named,Names(IArray((TsQIdent(IArray(TsIdentSimple(LazyResult_))),Some(TsIdentSimple(default)))),None))[39m [34m[thread => 91, project => frontend, ms => 56036, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(postcss/lib/lazy-result) / TsDeclNamespace(LazyResult)), id => postcss][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mReplaceExports.scala:55[39m  [94mDropping unexpected export in namespace TsExport(Comments(1),false,Named,Names(IArray((TsQIdent(IArray(TsIdentSimple(LazyResult_))),Some(TsIdentSimple(default)))),None))[39m [34m[thread => 91, project => frontend, ms => 56043, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(postcss/lib/lazy-result) / TsDeclNamespace(LazyResult)), id => postcss][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mReplaceExports.scala:55[39m  [94mDropping unexpected export in namespace TsExport(Comments(1),false,Named,Names(IArray((TsQIdent(IArray(TsIdentSimple(Root_))),Some(TsIdentSimple(default)))),None))[39m [34m[thread => 91, project => frontend, ms => 56057, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(postcss/lib/root) / TsDeclNamespace(Root)), id => postcss][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mReplaceExports.scala:55[39m  [94mDropping unexpected export in namespace TsExport(Comments(1),false,Named,Names(IArray((TsQIdent(IArray(TsIdentSimple(Rule_))),Some(TsIdentSimple(default)))),None))[39m [34m[thread => 91, project => frontend, ms => 56065, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(postcss/lib/rule) / TsDeclNamespace(Rule)), id => postcss][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mResolveTypeQueries.scala:232[39m [34mmsg[39m [94mCouldn't resolve typeof postcss[39m [34m[thread => 91, project => frontend, ms => 56226, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(postcss) / TsExport() / Tree() / TsDeclTypeAlias(Postcss) / TsTypeQuery()), id => postcss][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mPhase2ToScalaJs.scala:58[39m  [94mProcessing std[39m [34m[thread => 91, project => frontend, ms => 58244, phase => scala.js, id => std][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mPhase2ToScalaJs.scala:58[39m  [94mProcessing source-map-js[39m [34m[thread => 91, project => frontend, ms => 89247, phase => scala.js, id => source-map-js][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mPhase2ToScalaJs.scala:58[39m  [94mProcessing postcss[39m [34m[thread => 91, project => frontend, ms => 89281, phase => scala.js, id => postcss][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mPhase2ToScalaJs.scala:58[39m  [94mProcessing @tailwindcss/postcss[39m [34m[thread => 91, project => frontend, ms => 89602, phase => scala.js, id => @tailwindcss/postcss][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mPhase3Compile.scala:107[39m  [94mUsing cached build C:\Users\<USER>\.ivy2\local\org.scalablytyped\std_sjs1_3\5.8-c2d6aa\jars\std_sjs1_3.jar[39m [34m[thread => 91, project => frontend, ms => 97852, phase => build, id => std, flavour => NormalFlavour][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mPhase3Compile.scala:107[39m  [94mUsing cached build C:\Users\<USER>\.ivy2\local\org.scalablytyped\source-map-js_sjs1_3\1.2.1-26a955\jars\source-map-js_sjs1_3.jar[39m [34m[thread => 91, project => frontend, ms => 97878, phase => build, id => source-map-js, flavour => NormalFlavour][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mPhase3Compile.scala:107[39m  [94mUsing cached build C:\Users\<USER>\.ivy2\local\org.scalablytyped\postcss_sjs1_3\8.5.3-513014\jars\postcss_sjs1_3.jar[39m [34m[thread => 91, project => frontend, ms => 98010, phase => build, id => postcss, flavour => NormalFlavour][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mPhase3Compile.scala:128[39m  [94mBuilding C:\Users\<USER>\.ivy2\local\org.scalablytyped\tailwindcss__postcss_sjs1_3\4.1.7-a8584b\jars\tailwindcss__postcss_sjs1_3.jar...[39m [34m[thread => 91, project => frontend, ms => 98060, phase => build, id => @tailwindcss/postcss, flavour => NormalFlavour][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mPhase3Compile.scala:147[39m  [94mBuilt C:\Users\<USER>\.ivy2\local\org.scalablytyped\tailwindcss__postcss_sjs1_3\4.1.7-a8584b\jars\tailwindcss__postcss_sjs1_3.jar in 15483 ms[39m [34m[thread => 91, project => frontend, ms => 113545, phase => build, id => @tailwindcss/postcss, flavour => NormalFlavour][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mPhase2ToScalaJs.scala:58[39m  [94mProcessing country-emoji[39m [34m[thread => 91, project => frontend, ms => 113633, phase => scala.js, id => country-emoji][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mPhase3Compile.scala:107[39m  [94mUsing cached build C:\Users\<USER>\.ivy2\local\org.scalablytyped\country-emoji_sjs1_3\1.5.6-d49afe\jars\country-emoji_sjs1_3.jar[39m [34m[thread => 91, project => frontend, ms => 113649, phase => build, id => country-emoji, flavour => NormalFlavour][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mPhase2ToScalaJs.scala:58[39m  [94mProcessing country-flag-emoji-polyfill[39m [34m[thread => 91, project => frontend, ms => 113666, phase => scala.js, id => country-flag-emoji-polyfill][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mPhase3Compile.scala:107[39m  [94mUsing cached build C:\Users\<USER>\.ivy2\local\org.scalablytyped\country-flag-emoji-polyfill_sjs1_3\0.1.8-8a7bba\jars\country-flag-emoji-polyfill_sjs1_3.jar[39m [34m[thread => 91, project => frontend, ms => 113679, phase => build, id => country-flag-emoji-polyfill, flavour => NormalFlavour][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 113775, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(d3-array) / TsDeclFunction(cumsum) / TsFunSig() / TsTypeRef()), id => d3-array][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 113778, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(d3-array) / TsDeclFunction(cumsum) / TsFunSig() / TsTypeRef()), id => d3-array][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 113780, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(d3-array) / TsDeclFunction(rank) / TsFunSig() / TsTypeRef()), id => d3-array][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 113781, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(d3-array) / TsDeclFunction(rank) / TsFunSig() / TsTypeRef()), id => d3-array][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 113783, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(d3-array) / TsDeclFunction(fcumsum) / TsFunSig() / TsTypeRef()), id => d3-array][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 113785, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(d3-array) / TsDeclFunction(fcumsum) / TsFunSig() / TsTypeRef()), id => d3-array][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 113789, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(d3-array) / TsDeclFunction(shuffle) / TsFunSig() / TsFunParam() / TsTypeRef()), id => d3-array][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 113791, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(d3-array) / TsDeclFunction(shuffle) / TsFunSig() / TsTypeRef()), id => d3-array][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 113793, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(d3-array) / TsDeclFunction(shuffle) / TsFunSig() / TsFunParam() / TsTypeRef()), id => d3-array][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 113796, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(d3-array) / TsDeclFunction(shuffle) / TsFunSig() / TsTypeRef()), id => d3-array][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 113799, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(d3-array) / TsDeclFunction(shuffle) / TsFunSig() / TsFunParam() / TsTypeRef()), id => d3-array][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 113801, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(d3-array) / TsDeclFunction(shuffle) / TsFunSig() / TsTypeRef()), id => d3-array][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 113802, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(d3-array) / TsDeclFunction(shuffle) / TsFunSig() / TsFunParam() / TsTypeRef()), id => d3-array][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 113804, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(d3-array) / TsDeclFunction(shuffle) / TsFunSig() / TsTypeRef()), id => d3-array][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 113806, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(d3-array) / TsDeclFunction(shuffle) / TsFunSig() / TsFunParam() / TsTypeRef()), id => d3-array][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 113808, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(d3-array) / TsDeclFunction(shuffle) / TsFunSig() / TsTypeRef()), id => d3-array][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 113810, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(d3-array) / TsDeclFunction(shuffle) / TsFunSig() / TsFunParam() / TsTypeRef()), id => d3-array][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 113812, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(d3-array) / TsDeclFunction(shuffle) / TsFunSig() / TsTypeRef()), id => d3-array][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 113814, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(d3-array) / TsDeclFunction(shuffle) / TsFunSig() / TsFunParam() / TsTypeRef()), id => d3-array][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 113817, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(d3-array) / TsDeclFunction(shuffle) / TsFunSig() / TsTypeRef()), id => d3-array][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 113819, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(d3-array) / TsDeclFunction(shuffle) / TsFunSig() / TsFunParam() / TsTypeRef()), id => d3-array][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 113821, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(d3-array) / TsDeclFunction(shuffle) / TsFunSig() / TsTypeRef()), id => d3-array][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 113823, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(d3-array) / TsDeclFunction(shuffle) / TsFunSig() / TsFunParam() / TsTypeRef()), id => d3-array][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 113825, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(d3-array) / TsDeclFunction(shuffle) / TsFunSig() / TsTypeRef()), id => d3-array][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 113828, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(d3-array) / TsDeclFunction(shuffler) / TsFunSig() / TsTypeObject() / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeRef()), id => d3-array][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 113831, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(d3-array) / TsDeclFunction(shuffler) / TsFunSig() / TsTypeObject() / TsMemberCall() / TsFunSig() / TsTypeRef()), id => d3-array][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 113833, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(d3-array) / TsDeclFunction(shuffler) / TsFunSig() / TsTypeObject() / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeRef()), id => d3-array][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 113835, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(d3-array) / TsDeclFunction(shuffler) / TsFunSig() / TsTypeObject() / TsMemberCall() / TsFunSig() / TsTypeRef()), id => d3-array][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 113837, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(d3-array) / TsDeclFunction(shuffler) / TsFunSig() / TsTypeObject() / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeRef()), id => d3-array][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 113838, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(d3-array) / TsDeclFunction(shuffler) / TsFunSig() / TsTypeObject() / TsMemberCall() / TsFunSig() / TsTypeRef()), id => d3-array][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 113840, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(d3-array) / TsDeclFunction(shuffler) / TsFunSig() / TsTypeObject() / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeRef()), id => d3-array][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 113842, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(d3-array) / TsDeclFunction(shuffler) / TsFunSig() / TsTypeObject() / TsMemberCall() / TsFunSig() / TsTypeRef()), id => d3-array][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 113844, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(d3-array) / TsDeclFunction(shuffler) / TsFunSig() / TsTypeObject() / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeRef()), id => d3-array][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 113846, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(d3-array) / TsDeclFunction(shuffler) / TsFunSig() / TsTypeObject() / TsMemberCall() / TsFunSig() / TsTypeRef()), id => d3-array][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 113848, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(d3-array) / TsDeclFunction(shuffler) / TsFunSig() / TsTypeObject() / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeRef()), id => d3-array][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 113851, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(d3-array) / TsDeclFunction(shuffler) / TsFunSig() / TsTypeObject() / TsMemberCall() / TsFunSig() / TsTypeRef()), id => d3-array][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 113854, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(d3-array) / TsDeclFunction(shuffler) / TsFunSig() / TsTypeObject() / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeRef()), id => d3-array][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 113856, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(d3-array) / TsDeclFunction(shuffler) / TsFunSig() / TsTypeObject() / TsMemberCall() / TsFunSig() / TsTypeRef()), id => d3-array][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 113858, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(d3-array) / TsDeclFunction(shuffler) / TsFunSig() / TsTypeObject() / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeRef()), id => d3-array][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 113860, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(d3-array) / TsDeclFunction(shuffler) / TsFunSig() / TsTypeObject() / TsMemberCall() / TsFunSig() / TsTypeRef()), id => d3-array][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 113862, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(d3-array) / TsDeclFunction(shuffler) / TsFunSig() / TsTypeObject() / TsMemberCall() / TsFunSig() / TsFunParam() / TsTypeRef()), id => d3-array][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mDefaultedTypeArguments.scala:40[39m [34mmsg[39m [94mno default parameter for TArrayBuffer extends std.ArrayBufferLike[39m [34m[thread => 91, project => frontend, ms => 113863, phase => typescript, scope => TreeScope(TsParsedFile() / TsDeclModule(d3-array) / TsDeclFunction(shuffler) / TsFunSig() / TsTypeObject() / TsMemberCall() / TsFunSig() / TsTypeRef()), id => d3-array][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mPhase2ToScalaJs.scala:58[39m  [94mProcessing d3-array[39m [34m[thread => 91, project => frontend, ms => 113944, phase => scala.js, id => d3-array][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mPhase3Compile.scala:107[39m  [94mUsing cached build C:\Users\<USER>\.ivy2\local\org.scalablytyped\d3-array_sjs1_3\3.2.1-e294c2\jars\d3-array_sjs1_3.jar[39m [34m[thread => 91, project => frontend, ms => 114122, phase => build, id => d3-array, flavour => NormalFlavour][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mPhase2ToScalaJs.scala:58[39m  [94mProcessing d3-time[39m [34m[thread => 91, project => frontend, ms => 114274, phase => scala.js, id => d3-time][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mPhase2ToScalaJs.scala:58[39m  [94mProcessing d3-scale[39m [34m[thread => 91, project => frontend, ms => 114296, phase => scala.js, id => d3-scale][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mPhase3Compile.scala:107[39m  [94mUsing cached build C:\Users\<USER>\.ivy2\local\org.scalablytyped\d3-time_sjs1_3\3.0.3-ee40fa\jars\d3-time_sjs1_3.jar[39m [34m[thread => 91, project => frontend, ms => 114379, phase => build, id => d3-time, flavour => NormalFlavour][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mPhase3Compile.scala:107[39m  [94mUsing cached build C:\Users\<USER>\.ivy2\local\org.scalablytyped\d3-scale_sjs1_3\4.0.9-b434db\jars\d3-scale_sjs1_3.jar[39m [34m[thread => 91, project => frontend, ms => 114420, phase => build, id => d3-scale, flavour => NormalFlavour][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mPhase2ToScalaJs.scala:58[39m  [94mProcessing d3-scale-chromatic[39m [34m[thread => 91, project => frontend, ms => 114495, phase => scala.js, id => d3-scale-chromatic][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mPhase3Compile.scala:107[39m  [94mUsing cached build C:\Users\<USER>\.ivy2\local\org.scalablytyped\d3-scale-chromatic_sjs1_3\3.1.0-8093c2\jars\d3-scale-chromatic_sjs1_3.jar[39m [34m[thread => 91, project => frontend, ms => 114518, phase => build, id => d3-scale-chromatic, flavour => NormalFlavour][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mPhase2ToScalaJs.scala:58[39m  [94mProcessing d3-selection[39m [34m[thread => 91, project => frontend, ms => 115572, phase => scala.js, id => d3-selection][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mPhase3Compile.scala:107[39m  [94mUsing cached build C:\Users\<USER>\.ivy2\local\org.scalablytyped\d3-selection_sjs1_3\3.0.11-347d3d\jars\d3-selection_sjs1_3.jar[39m [34m[thread => 91, project => frontend, ms => 116239, phase => build, id => d3-selection, flavour => NormalFlavour][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mPhase2ToScalaJs.scala:58[39m  [94mProcessing d3-path[39m [34m[thread => 91, project => frontend, ms => 116463, phase => scala.js, id => d3-path][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mPhase2ToScalaJs.scala:58[39m  [94mProcessing d3-shape[39m [34m[thread => 91, project => frontend, ms => 116469, phase => scala.js, id => d3-shape][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mPhase3Compile.scala:107[39m  [94mUsing cached build C:\Users\<USER>\.ivy2\local\org.scalablytyped\d3-path_sjs1_3\3.0.2-fae252\jars\d3-path_sjs1_3.jar[39m [34m[thread => 91, project => frontend, ms => 116563, phase => build, id => d3-path, flavour => NormalFlavour][39m[0m
[0m[[0m[33mwarn[0m] [0m[0m[34mPhase3Compile.scala:107[39m  [94mUsing cached build C:\Users\<USER>\.ivy2\local\org.scalablytyped\d3-shape_sjs1_3\3.1.7-a1b703\jars\d3-shape_sjs1_3.jar[39m [34m[thread => 91, project => frontend, ms => 116624, phase => build, id => d3-shape, flavour => NormalFlavour][39m[0m
