[{"stageNumber": 1, "stageName": "Viada", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> E. - Žukauskas I.", "realName": "", "group": ["2WD"], "car": "BMW 325 Ti Compact E46", "stageTimeMs": 0, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": false, "comment": []}, {"stageNumber": 1, "stageName": "Viada", "country": "lithuania", "userName": "Apočkinas A. - Šachsuvarova A.", "realName": "", "group": ["2WD", "Junior"], "car": "BMW 320i E36", "stageTimeMs": 554400, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "Viada", "country": "lithuania", "userName": "Sukackas Darius - Vasiliauskas D.", "realName": "", "group": ["2WD", "Junior"], "car": "BMW 328 Ti Compact E46", "stageTimeMs": 571170, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "Viada", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["SG-4"], "car": "Subaru Impreza WRX", "stageTimeMs": 572180, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "Viada", "country": "lithuania", "userName": "Plastininas V. - Šileikis G.", "realName": "", "group": ["Open"], "car": "Audi A1 Rally2-Kit", "stageTimeMs": 581290, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "Viada", "country": "lithuania", "userName": "Patėjūnas M. - Rapalavičiūtė L.", "realName": "", "group": ["2WD", "Junior"], "car": "BMW 325i E36", "stageTimeMs": 582770, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "Viada", "country": "lithuania", "userName": "Ožiūnas Donatas - Ožiūnas V.", "realName": "", "group": ["2WD"], "car": "BMW 330i E46", "stageTimeMs": 584340, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "Viada", "country": "lithuania", "userName": "Černius A. - Ščiglinskienė J.", "realName": "", "group": ["Open"], "car": "Subaru Impreza STi N10", "stageTimeMs": 585920, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 10000, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "Viada", "country": "lithuania", "userName": "Ivanauskas G. - Pauliukonis V.", "realName": "", "group": ["SG-3"], "car": "BMW 328i E46", "stageTimeMs": 587930, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "Viada", "country": "lithuania", "userName": "Čiulada Rokas - <PERSON><PERSON><PERSON>", "realName": "", "group": ["SG-4"], "car": "Subaru Impreza STi N8", "stageTimeMs": 588330, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "Viada", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["2WD"], "car": "BMW 328 Ti Compact E46", "stageTimeMs": 588420, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 10000, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "Viada", "country": "lithuania", "userName": "Vaitašius R. - Ka<PERSON>ckaitė K.", "realName": "", "group": ["SG-3"], "car": "BMW 318 Ti Compact E36", "stageTimeMs": 589550, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "Viada", "country": "lithuania", "userName": "Garbuzas Mantas - But<PERSON>čius P.", "realName": "", "group": ["SG-3"], "car": "BMW 323 Ti Compact E36", "stageTimeMs": 592170, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "Viada", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON>.", "realName": "", "group": ["SG-4"], "car": "Subaru Impreza STi N12", "stageTimeMs": 595600, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "Viada", "country": "lithuania", "userName": "<PERSON><PERSON>š<PERSON> - Lapėnas S.", "realName": "", "group": ["SG-3"], "car": "BMW 325 Ti Compact E36", "stageTimeMs": 613350, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "Viada", "country": "lithuania", "userName": "Dervinskas A. - Sakalauskas D.", "realName": "", "group": ["2WD"], "car": "BMW 325 Ti Compact E36", "stageTimeMs": 627410, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "Viada", "country": "lithuania", "userName": "Žibutis K. - Žibutis D.", "realName": "", "group": ["SG-3"], "car": "BMW 325 Ti Compact E46", "stageTimeMs": 639560, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "Viada", "country": "lithuania", "userName": "Skrebūnas J. - Laurinavičiūtė G.", "realName": "", "group": ["SG-2"], "car": "Honda Civic", "stageTimeMs": 640100, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "Viada", "country": "lithuania", "userName": "Globytė E. - Miod<PERSON>ševskis R.", "realName": "", "group": ["2WD"], "car": "BMW E46", "stageTimeMs": 650630, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "Viada", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON> K.", "realName": "", "group": ["SG-2"], "car": "BMW 318 Ti Compact E36", "stageTimeMs": 661260, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "Viada", "country": "lithuania", "userName": "Grabinskis Justas - Šneideris J.", "realName": "", "group": ["2WD"], "car": "Renault Clio Sport", "stageTimeMs": 666040, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "Viada", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["Open"], "car": "Ford Fiesta MK7", "stageTimeMs": 1390420, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Alburnus 1", "country": "lithuania", "userName": "Ivanauskas G. - Pauliukonis V.", "realName": "", "group": ["SG-3"], "car": "BMW 328i E46", "stageTimeMs": 0, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": false, "comment": []}, {"stageNumber": 2, "stageName": "Alburnus 1", "country": "lithuania", "userName": "Skrebūnas J. - Laurinavičiūtė G.", "realName": "", "group": ["SG-2"], "car": "Honda Civic", "stageTimeMs": 0, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": false, "comment": []}, {"stageNumber": 2, "stageName": "Alburnus 1", "country": "lithuania", "userName": "Apočkinas A. - Šachsuvarova A.", "realName": "", "group": ["2WD", "Junior"], "car": "BMW 320i E36", "stageTimeMs": 467060, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Alburnus 1", "country": "lithuania", "userName": "Sukackas Darius - Vasiliauskas D.", "realName": "", "group": ["2WD", "Junior"], "car": "BMW 328 Ti Compact E46", "stageTimeMs": 471050, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Alburnus 1", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["2WD"], "car": "BMW 328 Ti Compact E46", "stageTimeMs": 471460, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Alburnus 1", "country": "lithuania", "userName": "Černius A. - Ščiglinskienė J.", "realName": "", "group": ["Open"], "car": "Subaru Impreza STi N10", "stageTimeMs": 474840, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Alburnus 1", "country": "lithuania", "userName": "Ožiūnas Donatas - Ožiūnas V.", "realName": "", "group": ["2WD"], "car": "BMW 330i E46", "stageTimeMs": 478360, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Alburnus 1", "country": "lithuania", "userName": "Vaitašius R. - Ka<PERSON>ckaitė K.", "realName": "", "group": ["SG-3"], "car": "BMW 318 Ti Compact E36", "stageTimeMs": 483550, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Alburnus 1", "country": "lithuania", "userName": "Čiulada Rokas - <PERSON><PERSON><PERSON>", "realName": "", "group": ["SG-4"], "car": "Subaru Impreza STi N8", "stageTimeMs": 484760, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Alburnus 1", "country": "lithuania", "userName": "Garbuzas Mantas - But<PERSON>čius P.", "realName": "", "group": ["SG-3"], "car": "BMW 323 Ti Compact E36", "stageTimeMs": 485600, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Alburnus 1", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON>.", "realName": "", "group": ["SG-4"], "car": "Subaru Impreza STi N12", "stageTimeMs": 485670, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Alburnus 1", "country": "lithuania", "userName": "Plastininas V. - Šileikis G.", "realName": "", "group": ["Open"], "car": "Audi A1 Rally2-Kit", "stageTimeMs": 490170, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Alburnus 1", "country": "lithuania", "userName": "<PERSON><PERSON>š<PERSON> - Lapėnas S.", "realName": "", "group": ["SG-3"], "car": "BMW 325 Ti Compact E36", "stageTimeMs": 493590, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Alburnus 1", "country": "lithuania", "userName": "Dervinskas A. - Sakalauskas D.", "realName": "", "group": ["2WD"], "car": "BMW 325 Ti Compact E36", "stageTimeMs": 500470, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Alburnus 1", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON> K.", "realName": "", "group": ["SG-2"], "car": "BMW 318 Ti Compact E36", "stageTimeMs": 508570, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Alburnus 1", "country": "lithuania", "userName": "Globytė E. - Miod<PERSON>ševskis R.", "realName": "", "group": ["2WD"], "car": "BMW E46", "stageTimeMs": 520460, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Alburnus 1", "country": "lithuania", "userName": "Grabinskis Justas - Šneideris J.", "realName": "", "group": ["2WD"], "car": "Renault Clio Sport", "stageTimeMs": 530300, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Alburnus 1", "country": "lithuania", "userName": "Žibutis K. - Žibutis D.", "realName": "", "group": ["SG-3"], "car": "BMW 325 Ti Compact E46", "stageTimeMs": 537390, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Alburnus 1", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["Open"], "car": "Ford Fiesta MK7", "stageTimeMs": 557100, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Alburnus 1", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["SG-4"], "car": "Subaru Impreza WRX", "stageTimeMs": 808350, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Alburnus 1", "country": "lithuania", "userName": "Patėjūnas M. - Rapalavičiūtė L.", "realName": "", "group": ["2WD", "Junior"], "car": "BMW 325i E36", "stageTimeMs": 895630, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 5000, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "TOPsport", "country": "lithuania", "userName": "<PERSON><PERSON>š<PERSON> - Lapėnas S.", "realName": "", "group": ["SG-3"], "car": "BMW 325 Ti Compact E36", "stageTimeMs": 0, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": false, "comment": []}, {"stageNumber": 3, "stageName": "TOPsport", "country": "lithuania", "userName": "Patėjūnas M. - Rapalavičiūtė L.", "realName": "", "group": ["2WD", "Junior"], "car": "BMW 325i E36", "stageTimeMs": 0, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": false, "comment": []}, {"stageNumber": 3, "stageName": "TOPsport", "country": "lithuania", "userName": "Dervinskas A. - Sakalauskas D.", "realName": "", "group": ["2WD"], "car": "BMW 325 Ti Compact E36", "stageTimeMs": 0, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": false, "comment": []}, {"stageNumber": 3, "stageName": "TOPsport", "country": "lithuania", "userName": "Apočkinas A. - Šachsuvarova A.", "realName": "", "group": ["2WD", "Junior"], "car": "BMW 320i E36", "stageTimeMs": 550480, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "TOPsport", "country": "lithuania", "userName": "Sukackas Darius - Vasiliauskas D.", "realName": "", "group": ["2WD", "Junior"], "car": "BMW 328 Ti Compact E46", "stageTimeMs": 567810, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "TOPsport", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["2WD"], "car": "BMW 328 Ti Compact E46", "stageTimeMs": 576390, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "TOPsport", "country": "lithuania", "userName": "Ožiūnas Donatas - Ožiūnas V.", "realName": "", "group": ["2WD"], "car": "BMW 330i E46", "stageTimeMs": 581570, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "TOPsport", "country": "lithuania", "userName": "Černius A. - Ščiglinskienė J.", "realName": "", "group": ["Open"], "car": "Subaru Impreza STi N10", "stageTimeMs": 582750, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "TOPsport", "country": "lithuania", "userName": "Plastininas V. - Šileikis G.", "realName": "", "group": ["Open"], "car": "Audi A1 Rally2-Kit", "stageTimeMs": 587100, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "TOPsport", "country": "lithuania", "userName": "Garbuzas Mantas - But<PERSON>čius P.", "realName": "", "group": ["SG-3"], "car": "BMW 323 Ti Compact E36", "stageTimeMs": 599680, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "TOPsport", "country": "lithuania", "userName": "Vaitašius R. - Ka<PERSON>ckaitė K.", "realName": "", "group": ["SG-3"], "car": "BMW 318 Ti Compact E36", "stageTimeMs": 611590, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "TOPsport", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["SG-4"], "car": "Subaru Impreza WRX", "stageTimeMs": 611600, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "TOPsport", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON>.", "realName": "", "group": ["SG-4"], "car": "Subaru Impreza STi N12", "stageTimeMs": 615950, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "TOPsport", "country": "lithuania", "userName": "Globytė E. - Miod<PERSON>ševskis R.", "realName": "", "group": ["2WD"], "car": "BMW E46", "stageTimeMs": 632050, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "TOPsport", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON> K.", "realName": "", "group": ["SG-2"], "car": "BMW 318 Ti Compact E36", "stageTimeMs": 634670, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "TOPsport", "country": "lithuania", "userName": "Čiulada Rokas - <PERSON><PERSON><PERSON>", "realName": "", "group": ["SG-4"], "car": "Subaru Impreza STi N8", "stageTimeMs": 641930, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "TOPsport", "country": "lithuania", "userName": "Grabinskis Justas - Šneideris J.", "realName": "", "group": ["2WD"], "car": "Renault Clio Sport", "stageTimeMs": 667280, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "TOPsport", "country": "lithuania", "userName": "Žibutis K. - Žibutis D.", "realName": "", "group": ["SG-3"], "car": "BMW 325 Ti Compact E46", "stageTimeMs": 685440, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Alburnus 2", "country": "lithuania", "userName": "Apočkinas A. - Šachsuvarova A.", "realName": "", "group": ["2WD", "Junior"], "car": "BMW 320i E36", "stageTimeMs": 457690, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Alburnus 2", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["SG-4"], "car": "Subaru Impreza WRX", "stageTimeMs": 465070, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Alburnus 2", "country": "lithuania", "userName": "Sukackas Darius - Vasiliauskas D.", "realName": "", "group": ["2WD", "Junior"], "car": "BMW 328 Ti Compact E46", "stageTimeMs": 470720, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Alburnus 2", "country": "lithuania", "userName": "Ožiūnas Donatas - Ožiūnas V.", "realName": "", "group": ["2WD"], "car": "BMW 330i E46", "stageTimeMs": 476020, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Alburnus 2", "country": "lithuania", "userName": "Garbuzas Mantas - But<PERSON>čius P.", "realName": "", "group": ["SG-3"], "car": "BMW 323 Ti Compact E36", "stageTimeMs": 486350, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Alburnus 2", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["2WD"], "car": "BMW 328 Ti Compact E46", "stageTimeMs": 489930, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Alburnus 2", "country": "lithuania", "userName": "Vaitašius R. - Ka<PERSON>ckaitė K.", "realName": "", "group": ["SG-3"], "car": "BMW 318 Ti Compact E36", "stageTimeMs": 491140, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Alburnus 2", "country": "lithuania", "userName": "Černius A. - Ščiglinskienė J.", "realName": "", "group": ["Open"], "car": "Subaru Impreza STi N10", "stageTimeMs": 492430, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Alburnus 2", "country": "lithuania", "userName": "Čiulada Rokas - <PERSON><PERSON><PERSON>", "realName": "", "group": ["SG-4"], "car": "Subaru Impreza STi N8", "stageTimeMs": 493860, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Alburnus 2", "country": "lithuania", "userName": "Plastininas V. - Šileikis G.", "realName": "", "group": ["Open"], "car": "Audi A1 Rally2-Kit", "stageTimeMs": 498040, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Alburnus 2", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON>.", "realName": "", "group": ["SG-4"], "car": "Subaru Impreza STi N12", "stageTimeMs": 504760, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Alburnus 2", "country": "lithuania", "userName": "Globytė E. - Miod<PERSON>ševskis R.", "realName": "", "group": ["2WD"], "car": "BMW E46", "stageTimeMs": 516500, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Alburnus 2", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON> K.", "realName": "", "group": ["SG-2"], "car": "BMW 318 Ti Compact E36", "stageTimeMs": 518520, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Alburnus 2", "country": "lithuania", "userName": "Grabinskis Justas - Šneideris J.", "realName": "", "group": ["2WD"], "car": "Renault Clio Sport", "stageTimeMs": 537710, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Alburnus 2", "country": "lithuania", "userName": "Žibutis K. - Žibutis D.", "realName": "", "group": ["SG-3"], "car": "BMW 325 Ti Compact E46", "stageTimeMs": 635700, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Elektrėnų Mero taurė", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["2WD"], "car": "BMW 328 Ti Compact E46", "stageTimeMs": 0, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": false, "comment": []}, {"stageNumber": 5, "stageName": "Elektrėnų Mero taurė", "country": "lithuania", "userName": "Sukackas Darius - Vasiliauskas D.", "realName": "", "group": ["2WD", "Junior"], "car": "BMW 328 Ti Compact E46", "stageTimeMs": 184430, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Elektrėnų Mero taurė", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["SG-4"], "car": "Subaru Impreza WRX", "stageTimeMs": 191180, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Elektrėnų Mero taurė", "country": "lithuania", "userName": "Ožiūnas Donatas - Ožiūnas V.", "realName": "", "group": ["2WD"], "car": "BMW 330i E46", "stageTimeMs": 192400, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Elektrėnų Mero taurė", "country": "lithuania", "userName": "Garbuzas Mantas - But<PERSON>čius P.", "realName": "", "group": ["SG-3"], "car": "BMW 323 Ti Compact E36", "stageTimeMs": 193060, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Elektrėnų Mero taurė", "country": "lithuania", "userName": "Plastininas V. - Šileikis G.", "realName": "", "group": ["Open"], "car": "Audi A1 Rally2-Kit", "stageTimeMs": 194370, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Elektrėnų Mero taurė", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON>.", "realName": "", "group": ["SG-4"], "car": "Subaru Impreza STi N12", "stageTimeMs": 196450, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Elektrėnų Mero taurė", "country": "lithuania", "userName": "Čiulada Rokas - <PERSON><PERSON><PERSON>", "realName": "", "group": ["SG-4"], "car": "Subaru Impreza STi N8", "stageTimeMs": 198250, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Elektrėnų Mero taurė", "country": "lithuania", "userName": "Vaitašius R. - Ka<PERSON>ckaitė K.", "realName": "", "group": ["SG-3"], "car": "BMW 318 Ti Compact E36", "stageTimeMs": 199670, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Elektrėnų Mero taurė", "country": "lithuania", "userName": "Černius A. - Ščiglinskienė J.", "realName": "", "group": ["Open"], "car": "Subaru Impreza STi N10", "stageTimeMs": 201300, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Elektrėnų Mero taurė", "country": "lithuania", "userName": "Globytė E. - Miod<PERSON>ševskis R.", "realName": "", "group": ["2WD"], "car": "BMW E46", "stageTimeMs": 209670, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Elektrėnų Mero taurė", "country": "lithuania", "userName": "Žibutis K. - Žibutis D.", "realName": "", "group": ["SG-3"], "car": "BMW 325 Ti Compact E46", "stageTimeMs": 210640, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Elektrėnų Mero taurė", "country": "lithuania", "userName": "Grabinskis Justas - Šneideris J.", "realName": "", "group": ["2WD"], "car": "Renault Clio Sport", "stageTimeMs": 212550, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Elektrėnų Mero taurė", "country": "lithuania", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON> K.", "realName": "", "group": ["SG-2"], "car": "BMW 318 Ti Compact E36", "stageTimeMs": 245510, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 20000, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Elektrėnų Mero taurė", "country": "lithuania", "userName": "Apočkinas A. - Šachsuvarova A.", "realName": "", "group": ["2WD", "Junior"], "car": "BMW 320i E36", "stageTimeMs": 355860, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 20000, "superRally": false, "finished": true, "comment": []}]