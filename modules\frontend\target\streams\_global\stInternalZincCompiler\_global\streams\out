[0m[[0m[0mdebug[0m] [0m[0m[32mZincCompiler.scala:236[39m [32mmsg.get()[39m [92mAttempting to fetch org.scala-lang % scala3-compiler_3 % 3.6.4. This operation may fail.[39m [32m[project => frontend, ms => 4][39m[0m
[0m[[0m[0mdebug[0m] [0m[0m[32mZincCompiler.scala:236[39m [32mmsg.get()[39m [92mFiles retrieved for org.scala-lang % scala3-compiler_3 % 3.6.4:[39m [32m[project => frontend, ms => 2346][39m[0m
[0m[[0m[0mdebug[0m] [0m[0m[32mZincCompiler.scala:236[39m [32mmsg.get()[39m [92mC:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-lang\scala3-compiler_3\3.6.4\scala3-compiler_3-3.6.4.jar, C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-lang\scala3-interfaces\3.6.4\scala3-interfaces-3.6.4.jar, C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-lang\scala3-library_3\3.6.4\scala3-library_3-3.6.4.jar, C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-lang\tasty-core_3\3.6.4\tasty-core_3-3.6.4.jar, C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-lang\modules\scala-asm\9.7.1-scala-1\scala-asm-9.7.1-scala-1.jar, C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-sbt\compiler-interface\1.10.4\compiler-interface-1.10.4.jar, C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\jline\jline-reader\3.27.1\jline-reader-3.27.1.jar, C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\jline\jline-terminal\3.27.1\jline-terminal-3.27.1.jar, C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\jline\jline-terminal-jni\3.27.1\jline-terminal-jni-3.27.1.jar, C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-lang\scala-library\2.13.15\scala-library-2.13.15.jar, C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-sbt\util-interface\1.10.4\util-interface-1.10.4.jar, C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\jline\jline-native\3.27.1\jline-native-3.27.1.jar, C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-lang\scala3-compiler_3\3.6.4\scala3-compiler_3-3.6.4.jar, C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-lang\scala3-interfaces\3.6.4\scala3-interfaces-3.6.4.jar, C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-lang\scala3-library_3\3.6.4\scala3-library_3-3.6.4.jar, C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-lang\tasty-core_3\3.6.4\tasty-core_3-3.6.4.jar, C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-lang\modules\scala-asm\9.7.1-scala-1\scala-asm-9.7.1-scala-1.jar, C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-sbt\compiler-interface\1.10.4\compiler-interface-1.10.4.jar, C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\jline\jline-reader\3.27.1\jline-reader-3.27.1.jar, C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\jline\jline-terminal\3.27.1\jline-terminal-3.27.1.jar, C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\jline\jline-terminal-jni\3.27.1\jline-terminal-jni-3.27.1.jar, C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-lang\scala-library\2.13.15\scala-library-2.13.15.jar, C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-sbt\util-interface\1.10.4\util-interface-1.10.4.jar, C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\jline\jline-native\3.27.1\jline-native-3.27.1.jar, C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-lang\scala3-compiler_3\3.6.4\scala3-compiler_3-3.6.4.jar, C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-lang\scala3-interfaces\3.6.4\scala3-interfaces-3.6.4.jar, C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-lang\scala3-library_3\3.6.4\scala3-library_3-3.6.4.jar, C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-lang\tasty-core_3\3.6.4\tasty-core_3-3.6.4.jar, C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-lang\modules\scala-asm\9.7.1-scala-1\scala-asm-9.7.1-scala-1.jar, C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-sbt\compiler-interface\1.10.4\compiler-interface-1.10.4.jar, C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\jline\jline-reader\3.27.1\jline-reader-3.27.1.jar, C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\jline\jline-terminal\3.27.1\jline-terminal-3.27.1.jar, C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\jline\jline-terminal-jni\3.27.1\jline-terminal-jni-3.27.1.jar, C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-lang\scala-library\2.13.15\scala-library-2.13.15.jar, C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-sbt\util-interface\1.10.4\util-interface-1.10.4.jar, C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\jline\jline-native\3.27.1\jline-native-3.27.1.jar[39m [32m[project => frontend, ms => 2350][39m[0m
[0m[[0m[0mdebug[0m] [0m[0m[32mZincCompiler.scala:236[39m [32mmsg.get()[39m [92mAttempting to fetch org.scala-lang % scala-library % 2.13.12. This operation may fail.[39m [32m[project => frontend, ms => 2357][39m[0m
[0m[[0m[0mdebug[0m] [0m[0m[32mZincCompiler.scala:236[39m [32mmsg.get()[39m [92mFiles retrieved for org.scala-lang % scala-library % 2.13.12:[39m [32m[project => frontend, ms => 11827][39m[0m
[0m[[0m[0mdebug[0m] [0m[0m[32mZincCompiler.scala:236[39m [32mmsg.get()[39m [92mC:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-lang\scala-library\2.13.12\scala-library-2.13.12.jar, C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-lang\scala-library\2.13.12\scala-library-2.13.12.jar, C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-lang\scala-library\2.13.12\scala-library-2.13.12.jar[39m [32m[project => frontend, ms => 11827][39m[0m
[0m[[0m[0mdebug[0m] [0m[0m[32mZincCompiler.scala:236[39m [32mmsg.get()[39m [92mAttempting to fetch org.scala-js % scalajs-library_2.13 % 1.19.0. This operation may fail.[39m [32m[project => frontend, ms => 11830][39m[0m
[0m[[0m[0mdebug[0m] [0m[0m[32mZincCompiler.scala:236[39m [32mmsg.get()[39m [92mFiles retrieved for org.scala-js % scalajs-library_2.13 % 1.19.0:[39m [32m[project => frontend, ms => 24244][39m[0m
[0m[[0m[0mdebug[0m] [0m[0m[32mZincCompiler.scala:236[39m [32mmsg.get()[39m [92mC:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-js\scalajs-library_2.13\1.19.0\scalajs-library_2.13-1.19.0.jar, C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-lang\scala-library\2.13.16\scala-library-2.13.16.jar, C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-js\scalajs-javalib\1.19.0\scalajs-javalib-1.19.0.jar, C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-js\scalajs-scalalib_2.13\2.13.16%2B1.19.0\scalajs-scalalib_2.13-2.13.16%2B1.19.0.jar, C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-js\scalajs-library_2.13\1.19.0\scalajs-library_2.13-1.19.0.jar, C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-lang\scala-library\2.13.16\scala-library-2.13.16.jar, C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-js\scalajs-javalib\1.19.0\scalajs-javalib-1.19.0.jar, C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-js\scalajs-scalalib_2.13\2.13.16%2B1.19.0\scalajs-scalalib_2.13-2.13.16%2B1.19.0.jar, C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-js\scalajs-library_2.13\1.19.0\scalajs-library_2.13-1.19.0.jar, C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-lang\scala-library\2.13.16\scala-library-2.13.16.jar, C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-js\scalajs-javalib\1.19.0\scalajs-javalib-1.19.0.jar, C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-js\scalajs-scalalib_2.13\2.13.16%2B1.19.0\scalajs-scalalib_2.13-2.13.16%2B1.19.0.jar[39m [32m[project => frontend, ms => 24245][39m[0m
[0m[[0m[0mdebug[0m] [0m[0m[32mZincCompiler.scala:236[39m [32mmsg.get()[39m [92mAttempting to fetch org.scala-lang % scala3-sbt-bridge % 3.6.4. This operation may fail.[39m [32m[project => frontend, ms => 24262][39m[0m
[0m[[0m[0mdebug[0m] [0m[0m[32mZincCompiler.scala:236[39m [32mmsg.get()[39m [92mFiles retrieved for org.scala-lang % scala3-sbt-bridge % 3.6.4:[39m [32m[project => frontend, ms => 24872][39m[0m
[0m[[0m[0mdebug[0m] [0m[0m[32mZincCompiler.scala:236[39m [32mmsg.get()[39m [92mC:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-lang\scala3-sbt-bridge\3.6.4\scala3-sbt-bridge-3.6.4.jar, C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-lang\scala3-sbt-bridge\3.6.4\scala3-sbt-bridge-3.6.4.jar, C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-lang\scala3-sbt-bridge\3.6.4\scala3-sbt-bridge-3.6.4.jar[39m [32m[project => frontend, ms => 24872][39m[0m
[0m[[0m[0mdebug[0m] [0m[0m[32mZincCompiler.scala:236[39m [32mmsg.get()[39m [92mAttempting to fetch org.scala-js % scalajs-dom_sjs1_3 % 2.3.0. This operation may fail.[39m [32m[project => frontend, ms => 87656][39m[0m
[0m[[0m[0mdebug[0m] [0m[0m[32mZincCompiler.scala:236[39m [32mmsg.get()[39m [92mFiles retrieved for org.scala-js % scalajs-dom_sjs1_3 % 2.3.0:[39m [32m[project => frontend, ms => 87789][39m[0m
[0m[[0m[0mdebug[0m] [0m[0m[32mZincCompiler.scala:236[39m [32mmsg.get()[39m [92mC:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-js\scalajs-dom_sjs1_3\2.3.0\scalajs-dom_sjs1_3-2.3.0.jar, C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-lang\scala3-library_sjs1_3\3.1.3\scala3-library_sjs1_3-3.1.3.jar, C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-js\scalajs-library_2.13\1.7.1\scalajs-library_2.13-1.7.1.jar, C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-lang\scala-library\2.13.8\scala-library-2.13.8.jar, C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-js\scalajs-dom_sjs1_3\2.3.0\scalajs-dom_sjs1_3-2.3.0.jar, C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-lang\scala3-library_sjs1_3\3.1.3\scala3-library_sjs1_3-3.1.3.jar, C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-js\scalajs-library_2.13\1.7.1\scalajs-library_2.13-1.7.1.jar, C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-lang\scala-library\2.13.8\scala-library-2.13.8.jar, C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-js\scalajs-dom_sjs1_3\2.3.0\scalajs-dom_sjs1_3-2.3.0.jar, C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-lang\scala3-library_sjs1_3\3.1.3\scala3-library_sjs1_3-3.1.3.jar, C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-js\scalajs-library_2.13\1.7.1\scalajs-library_2.13-1.7.1.jar, C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-lang\scala-library\2.13.8\scala-library-2.13.8.jar[39m [32m[project => frontend, ms => 87790][39m[0m
[0m[[0m[0mdebug[0m] [0m[0m[32mZincCompiler.scala:236[39m [32mmsg.get()[39m [92mAttempting to fetch com.olvind % scalablytyped-runtime_sjs1_3 % 2.4.2. This operation may fail.[39m [32m[project => frontend, ms => 87790][39m[0m
[0m[[0m[0mdebug[0m] [0m[0m[32mZincCompiler.scala:236[39m [32mmsg.get()[39m [92mFiles retrieved for com.olvind % scalablytyped-runtime_sjs1_3 % 2.4.2:[39m [32m[project => frontend, ms => 87894][39m[0m
[0m[[0m[0mdebug[0m] [0m[0m[32mZincCompiler.scala:236[39m [32mmsg.get()[39m [92mC:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\com\olvind\scalablytyped-runtime_sjs1_3\2.4.2\scalablytyped-runtime_sjs1_3-2.4.2.jar, C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-lang\scala3-library_sjs1_3\3.0.0\scala3-library_sjs1_3-3.0.0.jar, C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-js\scalajs-library_2.13\1.5.1\scalajs-library_2.13-1.5.1.jar, C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-lang\scala-library\2.13.5\scala-library-2.13.5.jar, C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\com\olvind\scalablytyped-runtime_sjs1_3\2.4.2\scalablytyped-runtime_sjs1_3-2.4.2.jar, C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-lang\scala3-library_sjs1_3\3.0.0\scala3-library_sjs1_3-3.0.0.jar, C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-js\scalajs-library_2.13\1.5.1\scalajs-library_2.13-1.5.1.jar, C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-lang\scala-library\2.13.5\scala-library-2.13.5.jar, C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\com\olvind\scalablytyped-runtime_sjs1_3\2.4.2\scalablytyped-runtime_sjs1_3-2.4.2.jar, C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-lang\scala3-library_sjs1_3\3.0.0\scala3-library_sjs1_3-3.0.0.jar, C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-js\scalajs-library_2.13\1.5.1\scalajs-library_2.13-1.5.1.jar, C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-lang\scala-library\2.13.5\scala-library-2.13.5.jar[39m [32m[project => frontend, ms => 87895][39m[0m
[0m[[0m[0mdebug[0m] [0m[0m[32mZincCompiler.scala:236[39m [32mmsg.get()[39m [92m[zinc] IncrementalCompile -----------[39m [32m[project => frontend, ms => 88536][39m[0m
[0m[[0m[0mdebug[0m] [0m[0m[32mZincCompiler.scala:236[39m [32mmsg.get()[39m [92mIncrementalCompile.incrementalCompile[39m [32m[project => frontend, ms => 88537][39m[0m
[0m[[0m[0mdebug[0m] [0m[0m[32mZincCompiler.scala:236[39m [32mmsg.get()[39m [92mprevious = Stamps for: 0 products, 0 sources, 0 libraries[39m [32m[project => frontend, ms => 88537][39m[0m
[0m[[0m[0mdebug[0m] [0m[0m[32mZincCompiler.scala:236[39m [32mmsg.get()[39m [92mcurrent source = Set(C:/Users/<USER>/Documents/Projects/scalaproject/modules/frontend/target/streams/_global/stImport/_global/streams/sources/t/tailwindcss__postcss/src/main/scala/typings/tailwindcssPostcss/anon.scala, C:/Users/<USER>/Documents/Projects/scalaproject/modules/frontend/target/streams/_global/stImport/_global/streams/sources/t/tailwindcss__postcss/src/main/scala/typings/tailwindcssPostcss/distMod.scala, C:/Users/<USER>/Documents/Projects/scalaproject/modules/frontend/target/streams/_global/stImport/_global/streams/sources/t/tailwindcss__postcss/src/main/scala/typings/tailwindcssPostcss/mod.scala, C:/Users/<USER>/Documents/Projects/scalaproject/modules/frontend/target/streams/_global/stImport/_global/streams/sources/t/tailwindcss__postcss/src/main/scala/typings/tailwindcssPostcss/tailwindcssPostcssRequire.scala)[39m [32m[project => frontend, ms => 88537][39m[0m
[0m[[0m[0mdebug[0m] [0m[0m[32mZincCompiler.scala:236[39m [32mmsg.get()[39m [92m> initialChanges = InitialChanges(Changes(added = Set(C:/Users/<USER>/Documents/Projects/scalaproject/modules/frontend/target/streams/_global/stImport/_global/streams/sources/t/tailwindcss__postcss/src/main/scala/typings/tailwindcssPostcss/tailwindcssPostcssRequire.scala, C:/Users/<USER>/Documents/Projects/scalaproject/modules/frontend/target/streams/_global/stImport/_global/streams/sources/t/tailwindcss__postcss/src/main/scala/typings/tailwindcssPostcss/anon.scala, C:/Users/<USER>/Documents/Projects/scalaproject/modules/frontend/target/streams/_global/stImport/_global/streams/sources/t/tailwindcss__postcss/src/main/scala/typings/tailwindcssPostcss/mod.scala, C:/Users/<USER>/Documents/Projects/scalaproject/modules/frontend/target/streams/_global/stImport/_global/streams/sources/t/tailwindcss__postcss/src/main/scala/typings/tailwindcssPostcss/distMod.scala), removed = Set(), changed = Set(), unmodified = ...),Set(),Set(),API Changes: Set())[39m [32m[project => frontend, ms => 88539][39m[0m
[0m[[0m[0mdebug[0m] [0m[0m[32mZincCompiler.scala:236[39m [32mmsg.get()[39m [92mFull compilation, no sources in previous analysis.[39m [32m[project => frontend, ms => 88539][39m[0m
[0m[[0m[0mdebug[0m] [0m[0m[32mZincCompiler.scala:236[39m [32mmsg.get()[39m [92mall 4 sources are invalidated[39m [32m[project => frontend, ms => 88539][39m[0m
[0m[[0m[0mdebug[0m] [0m[0m[32mZincCompiler.scala:236[39m [32mmsg.get()[39m [92mInitial set of included nodes: [39m [32m[project => frontend, ms => 88540][39m[0m
[0m[[0m[0mdebug[0m] [0m[0m[32mZincCompiler.scala:236[39m [32mmsg.get()[39m [92mRecompiling all sources: number of invalidated sources > 50.0 percent of all sources[39m [32m[project => frontend, ms => 88540][39m[0m
[0m[[0m[0mdebug[0m] [0m[0m[32mZincCompiler.scala:236[39m [32mmsg.get()[39m [92mcompilation cycle 1[39m [32m[project => frontend, ms => 88540][39m[0m
[0m[[0m[0minfo[0m] [0m[0m[33mZincCompiler.scala:234[39m [33mmsg.get()[39m [93mcompiling 4 Scala sources to C:\Users\<USER>\Documents\Projects\scalaproject\modules\frontend\target\streams\_global\stImport\_global\streams\sources\t\tailwindcss__postcss\target\scala-3\classes ...[39m [33m[project => frontend, ms => 88541][39m[0m
[0m[[0m[0mdebug[0m] [0m[0m[32mZincCompiler.scala:236[39m [32mmsg.get()[39m [92mReturning already retrieved and compiled bridge: C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-lang\scala3-sbt-bridge\3.6.4\scala3-sbt-bridge-3.6.4.jar.[39m [32m[project => frontend, ms => 88545][39m[0m
[0m[[0m[0mdebug[0m] [0m[0m[32mZincCompiler.scala:236[39m [32mmsg.get()[39m [92m[zinc] Running cached compiler 58a94daa for Scala Compiler version 3.6.4[39m [32m[project => frontend, ms => 89218][39m[0m
[0m[[0m[0mdebug[0m] [0m[0m[32mZincCompiler.scala:236[39m [32mmsg.get()[39m [92m[zinc] The Scala compiler is invoked with:[0m
[0m[[0m[0mdebug[0m] [0m[0m	-scalajs[0m
[0m[[0m[0mdebug[0m] [0m[0m	-encoding[0m
[0m[[0m[0mdebug[0m] [0m[0m	utf-8[0m
[0m[[0m[0mdebug[0m] [0m[0m	-feature[0m
[0m[[0m[0mdebug[0m] [0m[0m	-language:implicitConversions[0m
[0m[[0m[0mdebug[0m] [0m[0m	-language:higherKinds[0m
[0m[[0m[0mdebug[0m] [0m[0m	-language:existentials[0m
[0m[[0m[0mdebug[0m] [0m[0m	-no-indent[0m
[0m[[0m[0mdebug[0m] [0m[0m	-source:future[0m
[0m[[0m[0mdebug[0m] [0m[0m	-classpath[0m
[0m[[0m[0mdebug[0m] [0m[0m	C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-lang\scala3-compiler_3\3.6.4\scala3-compiler_3-3.6.4.jar;C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-lang\scala3-interfaces\3.6.4\scala3-interfaces-3.6.4.jar;C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-lang\scala3-library_3\3.6.4\scala3-library_3-3.6.4.jar;C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-lang\tasty-core_3\3.6.4\tasty-core_3-3.6.4.jar;C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-lang\modules\scala-asm\9.7.1-scala-1\scala-asm-9.7.1-scala-1.jar;C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-sbt\compiler-interface\1.10.4\compiler-interface-1.10.4.jar;C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\jline\jline-reader\3.27.1\jline-reader-3.27.1.jar;C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\jline\jline-terminal\3.27.1\jline-terminal-3.27.1.jar;C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\jline\jline-terminal-jni\3.27.1\jline-terminal-jni-3.27.1.jar;C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-lang\scala-library\2.13.15\scala-library-2.13.15.jar;C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-sbt\util-interface\1.10.4\util-interface-1.10.4.jar;C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\jline\jline-native\3.27.1\jline-native-3.27.1.jar;C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-js\scalajs-library_2.13\1.19.0\scalajs-library_2.13-1.19.0.jar;C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-lang\scala-library\2.13.16\scala-library-2.13.16.jar;C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-js\scalajs-javalib\1.19.0\scalajs-javalib-1.19.0.jar;C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-js\scalajs-scalalib_2.13\2.13.16%2B1.19.0\scalajs-scalalib_2.13-2.13.16%2B1.19.0.jar;C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-lang\scala-library\2.13.12\scala-library-2.13.12.jar;C:\Users\<USER>\.ivy2\local\org.scalablytyped\postcss_sjs1_3\8.5.3-513014\jars\postcss_sjs1_3.jar;C:\Users\<USER>\.ivy2\local\org.scalablytyped\source-map-js_sjs1_3\1.2.1-26a955\jars\source-map-js_sjs1_3.jar;C:\Users\<USER>\.ivy2\local\org.scalablytyped\std_sjs1_3\5.8-c2d6aa\jars\std_sjs1_3.jar;C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-lang\scala3-library_sjs1_3\3.1.3\scala3-library_sjs1_3-3.1.3.jar;C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-lang\scala3-library_sjs1_3\3.0.0\scala3-library_sjs1_3-3.0.0.jar;C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-js\scalajs-dom_sjs1_3\2.3.0\scalajs-dom_sjs1_3-2.3.0.jar;C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-lang\scala-library\2.13.5\scala-library-2.13.5.jar;C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-js\scalajs-library_2.13\1.7.1\scalajs-library_2.13-1.7.1.jar;C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-js\scalajs-library_2.13\1.5.1\scalajs-library_2.13-1.5.1.jar;C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\com\olvind\scalablytyped-runtime_sjs1_3\2.4.2\scalablytyped-runtime_sjs1_3-2.4.2.jar;C:\Users\<USER>\AppData\Local\Coursier\cache\v1\https\repo1.maven.org\maven2\org\scala-lang\scala-library\2.13.8\scala-library-2.13.8.jar[39m [32m[project => frontend, ms => 91186][39m[0m
[0m[[0m[0mdebug[0m] [0m[0m[32mZincCompiler.scala:236[39m [32mmsg.get()[39m [92mScala compilation took 13.6405584 s[39m [32m[project => frontend, ms => 102203][39m[0m
[0m[[0m[0mdebug[0m] [0m[0m[32mZincCompiler.scala:236[39m [32mmsg.get()[39m [92mdone compiling[39m [32m[project => frontend, ms => 102380][39m[0m
