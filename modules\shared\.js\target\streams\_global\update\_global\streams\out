[0m[[0m[0mdebug[0m] [0m[0mnot up to date. inChanged = true, force = false[0m
[0m[[0m[0mdebug[0m] [0m[0mUpdating sharedJS...[0m
[0m[[0m[0minfo[0m] [0m[0mFetching artifacts of shared_sjs1_3[0m
[0m[[0m[0minfo[0m] [0m[0mFetched artifacts of shared_sjs1_3[0m
[0m[[0m[31merror[0m] [0m[0mlmcoursier.internal.shaded.coursier.error.FetchError$DownloadingArtifacts: Error fetching artifacts:[0m
[0m[[0m[31merror[0m] [0m[0mhttps://repo1.maven.org/maven2/io/github/cquiroz/cldr-api_sjs1_3/4.5.0/cldr-api_sjs1_3-4.5.0.jar: download error: Caught java.nio.channels.OverlappingFileLockException while downloading https://repo1.maven.org/maven2/io/github/cquiroz/cldr-api_sjs1_3/4.5.0/cldr-api_sjs1_3-4.5.0.jar[0m
[0m[[0m[31merror[0m] [0m[0m[0m
[0m[[0m[31merror[0m] [0m[0m	at lmcoursier.internal.shaded.coursier.Artifacts$.$anonfun$fetchArtifacts$9(Artifacts.scala:365)[0m
[0m[[0m[31merror[0m] [0m[0m	at lmcoursier.internal.shaded.coursier.util.Task$.$anonfun$flatMap$extension$1(Task.scala:14)[0m
[0m[[0m[31merror[0m] [0m[0m	at lmcoursier.internal.shaded.coursier.util.Task$.$anonfun$flatMap$extension$1$adapted(Task.scala:14)[0m
[0m[[0m[31merror[0m] [0m[0m	at lmcoursier.internal.shaded.coursier.util.Task$.wrap(Task.scala:82)[0m
[0m[[0m[31merror[0m] [0m[0m	at lmcoursier.internal.shaded.coursier.util.Task$.$anonfun$flatMap$2(Task.scala:14)[0m
[0m[[0m[31merror[0m] [0m[0m	at scala.concurrent.Future.$anonfun$flatMap$1(Future.scala:307)[0m
[0m[[0m[31merror[0m] [0m[0m	at scala.concurrent.impl.Promise.$anonfun$transformWith$1(Promise.scala:51)[0m
[0m[[0m[31merror[0m] [0m[0m	at scala.concurrent.impl.CallbackRunnable.run(Promise.scala:74)[0m
[0m[[0m[31merror[0m] [0m[0m	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)[0m
[0m[[0m[31merror[0m] [0m[0m	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)[0m
[0m[[0m[31merror[0m] [0m[0m	at java.base/java.lang.Thread.run(Thread.java:840)[0m
[0m[[0m[31merror[0m] [0m[0mCaused by: lmcoursier.internal.shaded.coursier.cache.ArtifactError$DownloadError: download error: Caught java.nio.channels.OverlappingFileLockException while downloading https://repo1.maven.org/maven2/io/github/cquiroz/cldr-api_sjs1_3/4.5.0/cldr-api_sjs1_3-4.5.0.jar[0m
[0m[[0m[31merror[0m] [0m[0m	at lmcoursier.internal.shaded.coursier.cache.internal.Downloader$.$anonfun$downloading$1(Downloader.scala:808)[0m
[0m[[0m[31merror[0m] [0m[0m	at lmcoursier.internal.shaded.coursier.cache.internal.Retry.loop$1(Retry.scala:23)[0m
[0m[[0m[31merror[0m] [0m[0m	at lmcoursier.internal.shaded.coursier.cache.internal.Retry.retryOpt(Retry.scala:39)[0m
[0m[[0m[31merror[0m] [0m[0m	at lmcoursier.internal.shaded.coursier.cache.internal.Downloader$.coursier$cache$internal$Downloader$$downloading(Downloader.scala:812)[0m
[0m[[0m[31merror[0m] [0m[0m	at lmcoursier.internal.shaded.coursier.cache.internal.Downloader$Blocking$.remote(Downloader.scala:428)[0m
[0m[[0m[31merror[0m] [0m[0m	at lmcoursier.internal.shaded.coursier.cache.internal.Downloader.$anonfun$remote$6(Downloader.scala:546)[0m
[0m[[0m[31merror[0m] [0m[0m	at scala.concurrent.Future$.$anonfun$apply$1(Future.scala:659)[0m
[0m[[0m[31merror[0m] [0m[0m	at scala.util.Success.$anonfun$map$1(Try.scala:255)[0m
[0m[[0m[31merror[0m] [0m[0m	at scala.util.Success.map(Try.scala:213)[0m
[0m[[0m[31merror[0m] [0m[0m	at scala.concurrent.Future.$anonfun$map$1(Future.scala:292)[0m
[0m[[0m[31merror[0m] [0m[0m	at scala.concurrent.impl.Promise.$anonfun$transform$1(Promise.scala:42)[0m
[0m[[0m[31merror[0m] [0m[0m	at scala.concurrent.impl.CallbackRunnable.run(Promise.scala:74)[0m
[0m[[0m[31merror[0m] [0m[0m	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)[0m
[0m[[0m[31merror[0m] [0m[0m	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)[0m
[0m[[0m[31merror[0m] [0m[0m	at java.base/java.lang.Thread.run(Thread.java:840)[0m
[0m[[0m[31merror[0m] [0m[0mCaused by: java.nio.channels.OverlappingFileLockException[0m
[0m[[0m[31merror[0m] [0m[0m	at java.base/sun.nio.ch.FileLockTable.checkList(FileLockTable.java:229)[0m
[0m[[0m[31merror[0m] [0m[0m	at java.base/sun.nio.ch.FileLockTable.add(FileLockTable.java:123)[0m
[0m[[0m[31merror[0m] [0m[0m	at java.base/sun.nio.ch.FileChannelImpl.lock(FileChannelImpl.java:1276)[0m
[0m[[0m[31merror[0m] [0m[0m	at java.base/java.nio.channels.FileChannel.lock(FileChannel.java:1089)[0m
[0m[[0m[31merror[0m] [0m[0m	at lmcoursier.internal.shaded.coursier.paths.CachePath.withStructureLockOnce(CachePath.java:191)[0m
[0m[[0m[31merror[0m] [0m[0m	at lmcoursier.internal.shaded.coursier.paths.CachePath.withStructureLock(CachePath.java:154)[0m
[0m[[0m[31merror[0m] [0m[0m	at lmcoursier.internal.shaded.coursier.cache.CacheLocks$.withStructureLock(CacheLocks.scala:23)[0m
[0m[[0m[31merror[0m] [0m[0m	at lmcoursier.internal.shaded.coursier.cache.internal.Downloader$Blocking$.doDownload(Downloader.scala:304)[0m
[0m[[0m[31merror[0m] [0m[0m	at lmcoursier.internal.shaded.coursier.cache.internal.Downloader$Blocking$.$anonfun$remote$2(Downloader.scala:423)[0m
[0m[[0m[31merror[0m] [0m[0m	at lmcoursier.internal.shaded.coursier.cache.CacheLocks$.loop$1(CacheLocks.scala:94)[0m
[0m[[0m[31merror[0m] [0m[0m	at lmcoursier.internal.shaded.coursier.cache.CacheLocks$.withLockOr(CacheLocks.scala:117)[0m
[0m[[0m[31merror[0m] [0m[0m	at lmcoursier.internal.shaded.coursier.cache.internal.Downloader$Blocking$.$anonfun$remote$1(Downloader.scala:426)[0m
[0m[[0m[31merror[0m] [0m[0m	at lmcoursier.internal.shaded.coursier.cache.internal.Downloader$.$anonfun$downloading$2(Downloader.scala:776)[0m
[0m[[0m[31merror[0m] [0m[0m	at lmcoursier.internal.shaded.coursier.cache.CacheLocks$.withUrlLock(CacheLocks.scala:136)[0m
[0m[[0m[31merror[0m] [0m[0m	at lmcoursier.internal.shaded.coursier.cache.internal.Downloader$.$anonfun$downloading$1(Downloader.scala:776)[0m
[0m[[0m[31merror[0m] [0m[0m	at lmcoursier.internal.shaded.coursier.cache.internal.Retry.loop$1(Retry.scala:23)[0m
[0m[[0m[31merror[0m] [0m[0m	at lmcoursier.internal.shaded.coursier.cache.internal.Retry.retryOpt(Retry.scala:39)[0m
[0m[[0m[31merror[0m] [0m[0m	at lmcoursier.internal.shaded.coursier.cache.internal.Downloader$.coursier$cache$internal$Downloader$$downloading(Downloader.scala:812)[0m
[0m[[0m[31merror[0m] [0m[0m	at lmcoursier.internal.shaded.coursier.cache.internal.Downloader$Blocking$.remote(Downloader.scala:428)[0m
[0m[[0m[31merror[0m] [0m[0m	at lmcoursier.internal.shaded.coursier.cache.internal.Downloader.$anonfun$remote$6(Downloader.scala:546)[0m
[0m[[0m[31merror[0m] [0m[0m	at scala.concurrent.Future$.$anonfun$apply$1(Future.scala:659)[0m
[0m[[0m[31merror[0m] [0m[0m	at scala.util.Success.$anonfun$map$1(Try.scala:255)[0m
[0m[[0m[31merror[0m] [0m[0m	at scala.util.Success.map(Try.scala:213)[0m
[0m[[0m[31merror[0m] [0m[0m	at scala.concurrent.Future.$anonfun$map$1(Future.scala:292)[0m
[0m[[0m[31merror[0m] [0m[0m	at scala.concurrent.impl.Promise.$anonfun$transform$1(Promise.scala:42)[0m
[0m[[0m[31merror[0m] [0m[0m	at scala.concurrent.impl.CallbackRunnable.run(Promise.scala:74)[0m
[0m[[0m[31merror[0m] [0m[0m	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)[0m
[0m[[0m[31merror[0m] [0m[0m	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)[0m
[0m[[0m[31merror[0m] [0m[0m	at java.base/java.lang.Thread.run(Thread.java:840)[0m
[0m[[0m[31merror[0m] [0m[0m(sharedJS / [31mupdate[0m) lmcoursier.internal.shaded.coursier.error.FetchError$DownloadingArtifacts: Error fetching artifacts:[0m
[0m[[0m[31merror[0m] [0m[0mhttps://repo1.maven.org/maven2/io/github/cquiroz/cldr-api_sjs1_3/4.5.0/cldr-api_sjs1_3-4.5.0.jar: download error: Caught java.nio.channels.OverlappingFileLockException while downloading https://repo1.maven.org/maven2/io/github/cquiroz/cldr-api_sjs1_3/4.5.0/cldr-api_sjs1_3-4.5.0.jar[0m
