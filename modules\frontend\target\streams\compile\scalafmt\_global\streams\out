[0m[[0m[0mdebug[0m] [0m[0mscalafmt: Adding repositories [https://repo1.maven.org/maven2/][0m
[0m[[0m[0mdebug[0m] [0m[0mscalafmt: Adding credentials [][0m
[0m[[0m[0mdebug[0m] [0m[0mscalafmt: considering all files (no git)[0m
[0m[[0m[0mdebug[0m] [0m[0mscalafmt: Change report:[0m
[0m[[0m[0mdebug[0m] [0m[0m	Checked: C:\Users\<USER>\Documents\Projects\scalaproject\modules\frontend\src\main\scala\components\ResultFilter.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\frontend\src\main\scala\components\About.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\frontend\src\main\scala\Util.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\frontend\src\main\scala\components\RallyList.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\frontend\src\main\scala\Main.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\frontend\src\main\scala\Router.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\frontend\src\main\scala\Results.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\frontend\src\main\scala\components\Header.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\frontend\src\main\scala\components\ResultLines.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\frontend\src\main\scala\components\RallyResult.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\frontend\src\main\scala\components\Alert.scala[0m
[0m[[0m[0mdebug[0m] [0m[0m	Modified: C:\Users\<USER>\Documents\Projects\scalaproject\modules\frontend\src\main\scala\components\ResultFilter.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\frontend\src\main\scala\components\About.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\frontend\src\main\scala\Util.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\frontend\src\main\scala\components\RallyList.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\frontend\src\main\scala\Main.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\frontend\src\main\scala\Router.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\frontend\src\main\scala\Results.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\frontend\src\main\scala\components\Header.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\frontend\src\main\scala\components\ResultLines.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\frontend\src\main\scala\components\RallyResult.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\frontend\src\main\scala\components\Alert.scala[0m
[0m[[0m[0mdebug[0m] [0m[0m	Unmodified: [0m
[0m[[0m[0mdebug[0m] [0m[0m	Added: C:\Users\<USER>\Documents\Projects\scalaproject\modules\frontend\src\main\scala\components\ResultFilter.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\frontend\src\main\scala\components\About.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\frontend\src\main\scala\Util.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\frontend\src\main\scala\components\RallyList.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\frontend\src\main\scala\Main.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\frontend\src\main\scala\Router.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\frontend\src\main\scala\Results.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\frontend\src\main\scala\components\Header.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\frontend\src\main\scala\components\ResultLines.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\frontend\src\main\scala\components\RallyResult.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\frontend\src\main\scala\components\Alert.scala[0m
[0m[[0m[0mdebug[0m] [0m[0m	Removed: [0m
[0m[[0m[0minfo[0m] [0m[0mscalafmt: Formatting 11 Scala sources (C:\Users\<USER>\Documents\Projects\scalaproject\modules\frontend)...[0m
[0m[[0m[0mdebug[0m] [0m[0mscalafmt: Unchanged 11 Scala sources[0m
