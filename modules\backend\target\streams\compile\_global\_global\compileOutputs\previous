["sbt.Task[scala.collection.Seq[java.nio.file.Path]]", ["C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\Main$.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\Main$HttpServer$.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\Main$HttpServer.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\Main$LoadPressAuto$.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\Main$LoadPressAuto.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\Main$MigrateDb$.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\Main$MigrateDb.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\Main$SmokeRun$.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\Main$SmokeRun.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\Main.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\BuildInfo$.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\BuildInfo.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\Entry$.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\Entry.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\Ewrc$.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\Ewrc$Retired$.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\Ewrc$Retired.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\Ewrc.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\Logic$.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\Logic$Admin$.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\Logic$LogicError.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\Logic$RallyInProgress$.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\Logic$RallyNotStored$.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\Logic$RefreshNotSupported$.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\Logic.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\PositionResult$.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\PositionResult.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\PressAuto$.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\PressAuto.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\RallyEye$package$.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\RallyEye$package.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\RallyInfo$.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\RallyInfo.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\ResultValidator.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\Results$package$.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\Results$package.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\Rsf$$anon$1.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\Rsf$.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\Rsf.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\Shardable.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\Sharded$package$$anon$1.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\Sharded$package$.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\Sharded$package$given_Shardable_RallyKind_String$.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\Sharded$package.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\ShardedEntry$.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\ShardedEntry.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\SmokeRun$package$.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\SmokeRun$package$given_ResultValidator_List$.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\SmokeRun$package$given_ResultValidator_RallyData$.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\SmokeRun$package$given_ResultValidator_Unit$.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\SmokeRun$package$refreshResultValidator$.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\SmokeRun$package.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\Telemetry$.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\Telemetry$package$.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\Telemetry$package.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\Telemetry.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\TimeResult$.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\TimeResult.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\Util$package$.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\Util$package.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\loader\\PressAuto$$anon$1.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\loader\\PressAuto$$anon$10.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\loader\\PressAuto$$anon$11.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\loader\\PressAuto$$anon$12.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\loader\\PressAuto$$anon$13.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\loader\\PressAuto$$anon$14.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\loader\\PressAuto$$anon$15.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\loader\\PressAuto$$anon$16.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\loader\\PressAuto$$anon$17.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\loader\\PressAuto$$anon$18.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\loader\\PressAuto$$anon$19.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\loader\\PressAuto$$anon$2.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\loader\\PressAuto$$anon$20.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\loader\\PressAuto$$anon$21.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\loader\\PressAuto$$anon$22.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\loader\\PressAuto$$anon$3.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\loader\\PressAuto$$anon$4.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\loader\\PressAuto$$anon$5.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\loader\\PressAuto$$anon$6.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\loader\\PressAuto$$anon$7.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\loader\\PressAuto$$anon$8.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\loader\\PressAuto$$anon$9.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\loader\\PressAuto$.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\loader\\PressAuto$RaceAdminEntry$.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\loader\\PressAuto$RaceAdminEntry.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\loader\\PressAuto$RaceAdminResponse$.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\loader\\PressAuto$RaceAdminResponse.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\loader\\PressAuto$Racer$$anon$23.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\loader\\PressAuto$Racer$.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\loader\\PressAuto$Racer.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\loader\\PressAuto$Rally$.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\loader\\PressAuto$Rally.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\loader\\PressAuto$StageEntry$.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\loader\\PressAuto$StageEntry.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\loader\\PressAuto$Time$.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\loader\\PressAuto$Time.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\loader\\PressAuto$package$.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\loader\\PressAuto$package.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\loader\\PressAuto.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\storage\\Db$.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\storage\\Db$Config$.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\storage\\Db$Config.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\storage\\Db$package$.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\storage\\Db$package.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\storage\\Db.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\storage\\GraalVMResourceProvider$.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\storage\\GraalVMResourceProvider.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\storage\\Migrations$package$.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\storage\\Migrations$package.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\storage\\Model$package$.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\storage\\Model$package.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\storage\\Rally$.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\storage\\Rally.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\storage\\Repo$.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\storage\\Repo.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\storage\\Result$.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\classes\\rallyeye\\storage\\Result.class", "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\modules\\backend\\target\\scala-3.6.4\\zinc\\inc_compile_3.zip"]]