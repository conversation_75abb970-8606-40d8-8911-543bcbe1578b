receivers:
  otlp:
    protocols:  # enable OpenTelemetry Protocol receiver, both gRPC and HTTP
      grpc:
      http:

exporters:
  otlp:  # export received traces to Jaeger
    endpoint: jaeger:4317
    tls:
      insecure: true

  prometheus:  # run Prometheus exporter server on port 8889, so Prometheus can scrape the metrics
    endpoint: 0.0.0.0:8889
    send_timestamps: true

processors:
  batch:
    timeout: 10s

service:
  pipelines:
    traces:
      receivers: [otlp]
      processors: [batch]
      exporters: [otlp]
    metrics:
      receivers: [otlp]
      processors: [batch]
      exporters: [prometheus]
