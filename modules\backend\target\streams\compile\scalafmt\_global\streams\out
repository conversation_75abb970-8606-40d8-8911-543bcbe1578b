[0m[[0m[0mdebug[0m] [0m[0mscalafmt: Adding repositories [https://repo1.maven.org/maven2/][0m
[0m[[0m[0mdebug[0m] [0m[0mscalafmt: Adding credentials [][0m
[0m[[0m[0mdebug[0m] [0m[0mscalafmt: considering all files (no git)[0m
[0m[[0m[0mdebug[0m] [0m[0mscalafmt: Change report:[0m
[0m[[0m[0mdebug[0m] [0m[0m	Checked: C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\main\scala\PressAuto.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\main\scala\storage\Repo.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\main\scala\storage\GraalVMResourceProvider.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\main\scala\Rsf.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\main\scala\Telemetry.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\main\scala\Sharded.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\main\scala\Results.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\main\scala\Util.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\main\scala\Main.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\main\scala\storage\Db.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\main\scala\loader\PressAuto.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\main\scala\SmokeRun.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\main\scala\storage\Migrations.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\main\scala\Ewrc.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\main\scala\RallyEye.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\main\scala\storage\Model.scala[0m
[0m[[0m[0mdebug[0m] [0m[0m	Modified: C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\main\scala\PressAuto.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\main\scala\storage\Repo.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\main\scala\storage\GraalVMResourceProvider.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\main\scala\Rsf.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\main\scala\Telemetry.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\main\scala\Sharded.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\main\scala\Results.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\main\scala\Util.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\main\scala\Main.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\main\scala\storage\Db.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\main\scala\loader\PressAuto.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\main\scala\SmokeRun.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\main\scala\storage\Migrations.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\main\scala\Ewrc.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\main\scala\RallyEye.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\main\scala\storage\Model.scala[0m
[0m[[0m[0mdebug[0m] [0m[0m	Unmodified: [0m
[0m[[0m[0mdebug[0m] [0m[0m	Added: C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\main\scala\PressAuto.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\main\scala\storage\Repo.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\main\scala\storage\GraalVMResourceProvider.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\main\scala\Rsf.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\main\scala\Telemetry.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\main\scala\Sharded.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\main\scala\Results.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\main\scala\Util.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\main\scala\Main.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\main\scala\storage\Db.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\main\scala\loader\PressAuto.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\main\scala\SmokeRun.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\main\scala\storage\Migrations.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\main\scala\Ewrc.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\main\scala\RallyEye.scala, C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\src\main\scala\storage\Model.scala[0m
[0m[[0m[0mdebug[0m] [0m[0m	Removed: [0m
[0m[[0m[0minfo[0m] [0m[0mscalafmt: Formatting 16 Scala sources (C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend)...[0m
[0m[[0m[0mdebug[0m] [0m[0mscalafmt: Unchanged 16 Scala sources[0m
