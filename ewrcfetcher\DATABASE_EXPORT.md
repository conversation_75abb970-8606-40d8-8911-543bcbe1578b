# EWRC to StageTime Database Export

Export EWRC JSON results to your StageTime PostgreSQL database format.

## Overview

The `ewrc_to_db_export.py` script converts EWRC JSON data to match your StageTime PostgreSQL database schema. It generates proper SQL with UUIDs, foreign key relationships, and PostgreSQL-specific syntax.

## StageTime Database Schema Mapping

### Rallies Table
| EWRC JSON Field | Database Column | Notes |
|----------------|----------------|-------|
| `rally_info.name` | `name` | Rally name |
| `rally_info.dates` | `start_date`, `end_date` | Parsed date range with timestamps |
| - | `id` | Generated UUID |
| - | `country` | Defaults to 'greece' |
| - | `status` | Set to 'finished' |
| - | `surface` | Set to 'asphalt' |
| - | `views` | Set to 0 |

### Persons Table (Drivers & Codrivers)
| EWRC JSON Field | Database Column | Notes |
|----------------|----------------|-------|
| `driver_codriver` | `first_name`, `last_name` | Parsed from "First Last - First Last" |
| `country` | `nationality` | Driver/codriver nationality |
| - | `id` | Generated UUID |

### Drivers/Codrivers Tables
| EWRC JSON Field | Database Column | Notes |
|----------------|----------------|-------|
| - | `id` | References persons.id |

### Stages Table
| EWRC JSON Field | Database Column | Notes |
|----------------|----------------|-------|
| `stage_number` | `number` | Stage sequence number |
| `stage_name` | `name` | Stage name |
| - | `id` | Generated UUID |
| - | `rally_id` | References rallies.id |
| - | `length` | Default 10.00 km |
| - | `surface` | Set to 'asphalt' |
| - | `status` | Set to 'finished' |
| - | `is_power_stage` | Detected from stage name |
| - | `is_super_special` | Detected from 'SSS' in name |

### Entries Table
| EWRC JSON Field | Database Column | Notes |
|----------------|----------------|-------|
| `entry_number` | `number` | Entry number (without #) |
| `car` | `car` | Car model |
| `group` | `class` | Group/class classification |
| - | `id` | Generated UUID |
| - | `rally_id` | References rallies.id |
| - | `driver_id` | References persons.id |
| - | `codriver_id` | References persons.id |
| - | `status` | Set to 'finished' |

### Results Table
| EWRC JSON Field | Database Column | Notes |
|----------------|----------------|-------|
| `stage_time_ms` | `time` | Converted to seconds (NUMERIC) |
| - | `id` | Generated UUID |
| - | `rally_id` | References rallies.id |
| - | `stage_id` | References stages.id |
| - | `entry_id` | References entries.id |

## Usage

### Generate StageTime SQL File
```bash
python ewrc_to_db_export.py results.json output.sql
```

### Examples
```bash
# Export Rally Kentavros to StageTime SQL
python ewrc_to_db_export.py ewrc_complete_results_94276-rally-kentavros-2025.json kentavros_stagetime.sql

# Export Rally Kritis to StageTime SQL
python ewrc_to_db_export.py ewrc_complete_results_94860-rally-kritis-2025.json kritis_stagetime.sql

# Auto-generate filename
python ewrc_to_db_export.py ewrc_complete_results_94276-rally-kentavros-2025.json
# Creates: ewrc_complete_results_94276-rally-kentavros-2025_stagetime.sql
```

## Features

### ✅ **PostgreSQL Compatibility**
- **UUID Generation**: Proper UUID4 generation for all primary keys
- **Foreign Key Relationships**: Maintains referential integrity
- **PostgreSQL Syntax**: Uses `ON CONFLICT DO NOTHING` and `NOW()` functions
- **ENUM Types**: Compatible with rally_status, stage_status, entry_status enums

### ✅ **Data Transformation**
- **Driver/Codriver Parsing**: Splits "First Last - First Last" into separate persons
- **Date Parsing**: Converts "5.4.2025 - 6.4.2025" to ISO timestamps
- **Time Conversion**: Converts milliseconds to seconds (NUMERIC)
- **Stage Detection**: Auto-detects Power Stages and Super Special Stages
- **Person Deduplication**: Caches persons to avoid duplicates

### ✅ **Relational Structure**
- **Normalized Design**: Follows your StageTime schema exactly
- **Person Management**: Creates persons, drivers, and codrivers properly
- **Entry Management**: Links drivers/codrivers to rally entries
- **Result Linking**: Connects results to stages and entries

### ✅ **Data Quality**
- **Handles Missing Codrivers**: Uses driver as codriver when needed
- **SQL Injection Safe**: Properly escapes single quotes
- **Default Values**: Provides sensible defaults for missing data
- **Error Handling**: Graceful handling of malformed data

## Sample Output

### StageTime SQL File Format
```sql
-- EWRC to StageTime Database Export
-- Generated: 2025-01-27T10:30:00
-- Source: ewrc_complete_results_94276-rally-kentavros-2025.json
-- Rally: Rally Kentavros 2025

BEGIN;

INSERT INTO rallies (
    id, name, country, start_date, end_date, status, surface, views, created_at, updated_at
) VALUES (
    'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
    'Rally Kentavros 2025',
    'greece',
    '2025-04-05T00:00:00Z',
    '2025-04-06T23:59:59Z',
    'finished',
    'asphalt',
    0,
    NOW(),
    NOW()
);

INSERT INTO stages (
    id, rally_id, name, number, length, surface, start_time, status, is_power_stage, is_super_special, created_at, updated_at
) VALUES (
    'b2c3d4e5-f6g7-8901-bcde-f23456789012',
    'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
    'SS1 Alikopetra 1',
    1,
    10.00,
    'asphalt',
    '2025-04-05T08:00:00Z',
    'finished',
    false,
    false,
    NOW(),
    NOW()
);

INSERT INTO persons (
    id, first_name, last_name, nationality, created_at, updated_at
) VALUES (
    'c3d4e5f6-g7h8-9012-cdef-345678901234',
    'Anapoliotakis',
    'G.',
    'greece',
    NOW(),
    NOW()
) ON CONFLICT (id) DO NOTHING;

INSERT INTO drivers (id) VALUES ('c3d4e5f6-g7h8-9012-cdef-345678901234') ON CONFLICT (id) DO NOTHING;

INSERT INTO entries (
    id, rally_id, driver_id, codriver_id, car, number, class, status, created_at, updated_at
) VALUES (
    'd4e5f6g7-h8i9-0123-defg-456789012345',
    'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
    'c3d4e5f6-g7h8-9012-cdef-345678901234',
    'e5f6g7h8-i9j0-1234-efgh-567890123456',
    'Ford Fiesta R5',
    1,
    'C1',
    'finished',
    NOW(),
    NOW()
);

INSERT INTO results (
    id, rally_id, stage_id, entry_id, time, created_at, updated_at
) VALUES (
    'f6g7h8i9-j0k1-2345-fghi-678901234567',
    'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
    'b2c3d4e5-f6g7-8901-bcde-f23456789012',
    'd4e5f6g7-h8i9-0123-defg-456789012345',
    76.430,
    NOW(),
    NOW()
);

COMMIT;
```

## Integration with Your StageTime Workflow

1. **Fetch Rally Data**: Use `ewrc_complete.py` to get JSON results
2. **Export to StageTime SQL**: Use `ewrc_to_db_export.py` to convert to PostgreSQL
3. **Import to Database**: Execute the generated SQL in your PostgreSQL database

```bash
# Complete workflow example
python ewrc_complete.py "94276-rally-kentavros-2025"
python ewrc_to_db_export.py ewrc_complete_results_94276-rally-kentavros-2025.json kentavros_stagetime.sql

# Import to PostgreSQL
psql -d stagetime -f kentavros_stagetime.sql
```

## Database Integration Notes

### ✅ **UUID Management**
- **Consistent UUIDs**: Same person gets same UUID across rallies
- **Foreign Keys**: All relationships properly maintained
- **Conflict Handling**: Uses `ON CONFLICT DO NOTHING` for safety

### ✅ **Data Mapping**
- **Times**: Converted from milliseconds to seconds (NUMERIC)
- **Dates**: ISO format with timezone (TIMESTAMPTZ)
- **Status**: All entities marked as 'finished'
- **Enums**: Compatible with your PostgreSQL ENUM types

### ✅ **Schema Compliance**
- **Normalized Structure**: Follows your StageTime schema exactly
- **Required Fields**: All NOT NULL constraints satisfied
- **Relationships**: Proper foreign key references maintained
- **Indexes**: Compatible with your existing indexes

### 🔧 **Customization Options**
- **Country**: Currently defaults to 'greece' (easily customizable)
- **Surface**: Currently defaults to 'asphalt' (easily customizable)
- **Stage Length**: Currently defaults to 10.00 km (easily customizable)
- **Rally Status**: Currently defaults to 'finished' (easily customizable)
