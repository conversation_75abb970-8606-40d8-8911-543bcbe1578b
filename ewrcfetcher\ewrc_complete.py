#!/usr/bin/env python3
"""
Complete EWRC Fetcher

A comprehensive Python implementation of all EWRC fetching logic from the RallyEye Scala project.
Based on modules/backend/src/main/scala/Ewrc.scala and utility functions.

Features:
- Complete rally information parsing
- Stage-by-stage results fetching
- Retired driver handling
- Time parsing with millisecond precision
- Country extraction from flags
- Group classification parsing
- Robust error handling and date parsing
"""

import requests
import re
import json
import sys

from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict
from bs4 import BeautifulSoup


@dataclass
class RallyInfo:
    name: str
    championship: List[str]
    start_date: str
    end_date: str
    distance_meters: int
    total_entries: int
    finished: int


@dataclass
class Entry:
    stage_number: int
    stage_name: str
    stage_distance_km: Optional[float]
    stage_date: Optional[str]
    stage_start_time: Optional[str]
    country: str
    driver_codriver: str
    entry_number: str
    group: List[str]
    car: str
    stage_time_ms: Optional[int]
    overall_time_ms: Optional[int]
    penalties_ms: int
    super_rally: bool
    active: bool
    nominal_time: bool
    comments: Optional[str] = None


class EWRCCompleteFetcher:
    """Complete EWRC fetcher with all functionality from the Scala implementation."""

    BASE_URL = "https://www.ewrc-results.com"

    def __init__(self, debug=False):
        self.debug = debug
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })

    def fetch_rally_info(self, rally_id: str) -> RallyInfo:
        """Fetch complete rally information from the final results page."""
        url = f"{self.BASE_URL}/final/{rally_id}/"
        response = self.session.get(url)
        response.raise_for_status()
        return self._parse_rally_info(response.text)

    def fetch_rally_results(self, rally_id: str) -> List[Entry]:
        """Fetch complete rally results including stage-by-stage data."""
        # Get final page to extract stage IDs and retired drivers
        final_url = f"{self.BASE_URL}/final/{rally_id}/"
        final_response = self.session.get(final_url)
        final_response.raise_for_status()

        # Parse retired drivers info (maps entry number -> groups)
        retired_drivers = self._parse_retired_number_group(final_response.text)

        # Parse full driver names from entries and final pages (maps entry number -> full name)
        full_driver_names = self._parse_full_driver_names_from_pages(rally_id, final_response.text)

        # Note: Penalties are now parsed per-stage from penalty panels, not from global penalty page

        # First try to get stages from the main results page (more reliable)
        print("Fetching stage list from main results page...")
        results_url = f"{self.BASE_URL}/results/{rally_id}/"
        stage_ids = []

        try:
            results_response = self.session.get(results_url)
            results_response.raise_for_status()
            stage_ids = self._parse_stage_ids_from_results_page(results_response.text, rally_id)
            if self.debug:
                print(f"Found {len(stage_ids)} stages from main results page: {stage_ids}")
        except Exception as e:
            print(f"Could not fetch main results page: {e}")

        # If that fails, try the final page as fallback
        if not stage_ids:
            print("No stages found from main page, trying final page...")
            stage_ids = self._parse_stage_ids(final_response.text, rally_id)

        # Only use stage discovery as last resort and be very conservative
        if not stage_ids:
            print("No stages found from either page, trying stage discovery...")
            # Try a few common stage IDs around a base number
            base_id = 493862  # Known first stage for this rally
            for i in range(6):  # Try up to 6 stages
                test_id = base_id + i
                if self._stage_exists(rally_id, test_id):
                    stage_ids.append(test_id)
                else:
                    break

        # Fetch results for each stage
        all_entries = []
        for stage_id in stage_ids:
            try:
                stage_entries = self._fetch_stage_results(rally_id, stage_id, retired_drivers, full_driver_names)
                all_entries.extend(stage_entries)
            except Exception as e:
                print(f"Warning: Could not fetch stage {stage_id}: {e}")
                continue

        # Penalties are now applied per-stage during stage parsing, not globally
        return all_entries

    def _parse_rally_info(self, html: str) -> RallyInfo:
        """Parse rally information from the final results page (based on Scala parseRallyInfo)."""
        soup = BeautifulSoup(html, 'html.parser')

        # Extract rally name (remove number prefix like "45. Rally Name")
        h3_element = soup.select_one("html body main#main-section h3")
        if not h3_element:
            raise ValueError("Could not find rally name")

        rally_name_text = h3_element.get_text().strip()
        name_match = re.match(r'(\d+)\.\s*(.*)', rally_name_text)
        name = name_match.group(2).strip() if name_match else rally_name_text

        # Extract top info section (dates, distance)
        top_info = soup.select_one("html body main#main-section div.top-info")
        if not top_info:
            raise ValueError("Could not find top info section")

        top_info_text = top_info.get_text()
        top_info_parts = [part.strip() for part in top_info_text.split('•')]

        # Parse dates with multiple strategies
        start_date, end_date = self._parse_dates_from_parts(top_info_parts)

        # Parse distance
        distance_meters = self._parse_distance_from_parts(top_info_parts)

        # Parse championship from top sections
        top_sections = soup.select_one("html body main#main-section div.top-sections")
        championship = []
        if top_sections:
            championship_text = top_sections.get_text()
            championship = [part.split('#')[0].strip() for part in championship_text.split('•')]

        # Parse finished count
        finished_elements = soup.select("html body main#main-section div.text-center.text-primary.font-weight-bold")
        finished = 0
        for element in finished_elements:
            if 'finished' in element.get_text():
                finished_match = re.search(r'finished:\s*(\d+)', element.get_text())
                if finished_match:
                    finished = int(finished_match.group(1))
                break

        # Parse retirements count - look for the retirement section
        retirements = 0

        # Method 1: Look for retirement header and following content
        retirement_headers = soup.select("h4")
        for header in retirement_headers:
            if 'Retirements' in header.get_text():
                # Look for the next element that contains the count
                next_elem = header.find_next_sibling()
                if next_elem:
                    next_text = next_elem.get_text()
                    retirement_match = re.search(r'(\d+)\s*\((\d+)\s*on\s*list\)', next_text)
                    if retirement_match:
                        retirements = int(retirement_match.group(1))
                        break

        # Method 2: If that fails, search the entire page text
        if retirements == 0:
            page_text = soup.get_text()
            retirement_match = re.search(r'Retirements?\s+(\d+)\s*\((\d+)\s*on\s*list\)', page_text, re.IGNORECASE | re.MULTILINE | re.DOTALL)
            if retirement_match:
                retirements = int(retirement_match.group(1))

        # Calculate total entries
        total_entries = finished + retirements

        # Try to get more accurate count from final results table
        final_results_table = soup.select_one("html body main#main-section div.final-results table.results")
        if final_results_table:
            # Count all driver rows (excluding headers)
            driver_rows = final_results_table.select("tbody tr")
            # Filter out header rows and empty rows
            actual_driver_rows = []
            for row in driver_rows:
                cells = row.select("td")
                if len(cells) >= 3:  # Must have at least position, number, driver
                    # Check if it's a driver row (has entry number)
                    number_elem = row.select_one("td span.font-weight-bold.text-primary")
                    if number_elem or any("#" in cell.get_text() for cell in cells[:2]):
                        actual_driver_rows.append(row)

            if len(actual_driver_rows) > total_entries:
                total_entries = len(actual_driver_rows)
                if self.debug:
                    print(f"Updated total entries from final results table: {total_entries}")

        return RallyInfo(
            name=name,
            championship=championship,
            start_date=start_date,
            end_date=end_date,
            distance_meters=distance_meters,
            total_entries=total_entries,
            finished=finished
        )

    def _parse_dates_from_parts(self, top_info_parts: List[str]) -> Tuple[str, str]:
        """Parse dates from top info parts with multiple fallback strategies."""
        date_part = None

        # Strategy 1: European format like "5. 4. – 6. 4. 2025"
        for part in top_info_parts:
            if re.search(r'\d+\.\s*\d+\.\s*–\s*\d+\.\s*\d+\.\s*\d{4}', part):
                date_part = part
                break

        # Strategy 2: Standard format like "15-17 March 2024"
        if not date_part:
            for part in top_info_parts:
                if re.search(r'\d+-\d+\s+\w+\s+\d{4}', part):
                    date_part = part
                    break

        # Strategy 3: Any part with month names
        if not date_part:
            for part in top_info_parts:
                if any(month in part.lower() for month in ['jan', 'feb', 'mar', 'apr', 'may', 'jun', 'jul', 'aug', 'sep', 'oct', 'nov', 'dec']):
                    date_part = part
                    break

        # Strategy 4: Numeric date patterns
        if not date_part:
            for part in top_info_parts:
                if re.search(r'\d{1,2}[-/]\d{1,2}[-/]\d{4}', part):
                    date_part = part
                    break

        # Strategy 5: Any part with a year
        if not date_part:
            for part in top_info_parts:
                if re.search(r'\d{4}', part):
                    date_part = part
                    break

        if not date_part:
            if self.debug:
                print(f"Warning: Could not find date information. Available parts: {top_info_parts}")
            return "Unknown", "Unknown"

        return self._parse_date_string(date_part)

    def _parse_date_string(self, date_str: str) -> Tuple[str, str]:
        """Parse various date string formats (based on Scala date parsing logic)."""
        # European format like "5. 4. – 6. 4. 2025"
        euro_range_match = re.search(r'(\d+)\.\s*(\d+)\.\s*–\s*(\d+)\.\s*(\d+)\.\s*(\d+)', date_str)
        if euro_range_match:
            start_day, start_month, end_day, end_month, year = euro_range_match.groups()
            start_date = f"{start_day}.{start_month}.{year}"
            end_date = f"{end_day}.{end_month}.{year}"
            return start_date, end_date

        # Standard range format like "15-17 March 2024"
        date_match = re.search(r'(\d+)-(\d+)\s+(\w+)\s+(\d+)', date_str)
        if date_match:
            start_day, end_day, month, year = date_match.groups()
            start_date = f"{start_day} {month} {year}"
            end_date = f"{end_day} {month} {year}"
            return start_date, end_date

        # Single date format like "15 March 2024"
        single_date_match = re.search(r'(\d+)\s+(\w+)\s+(\d+)', date_str)
        if single_date_match:
            day, month, year = single_date_match.groups()
            date = f"{day} {month} {year}"
            return date, date

        # Numeric date formats like "15/03/2024" or "15-03-2024"
        numeric_date_match = re.search(r'(\d{1,2})[-/](\d{1,2})[-/](\d{4})', date_str)
        if numeric_date_match:
            day, month, year = numeric_date_match.groups()
            date = f"{day}/{month}/{year}"
            return date, date

        # European single date like "5. 4. 2025"
        euro_single_match = re.search(r'(\d+)\.\s*(\d+)\.\s*(\d+)', date_str)
        if euro_single_match:
            day, month, year = euro_single_match.groups()
            date = f"{day}.{month}.{year}"
            return date, date

        # Year only
        year_match = re.search(r'(\d{4})', date_str)
        if year_match:
            year = year_match.group(1)
            return year, year

        return date_str, date_str

    def _parse_distance_from_parts(self, top_info_parts: List[str]) -> int:
        """Parse distance information from top info parts."""
        distance_part = next((part for part in top_info_parts if 'km' in part), None)
        distance_meters = 0

        if distance_part:
            # Remove "cancelled" text if present
            distance_text = distance_part.split('cancelled')[0]

            # Pattern 1: "123.45 km"
            distance_match = re.search(r'(\d+)\.(\d+)\s*km', distance_text)
            if distance_match:
                distance_meters = int(distance_match.group(1)) * 1000 + int(distance_match.group(2)) * 100
            else:
                # Pattern 2: "123 km"
                distance_match = re.search(r'(\d+)\s*km', distance_text)
                if distance_match:
                    distance_meters = int(distance_match.group(1)) * 1000
                else:
                    if self.debug:
                        print(f"Warning: Could not parse distance from: {distance_part}")
        else:
            if self.debug:
                print(f"Warning: Could not find distance information")

        return distance_meters

    def _parse_retired_number_group(self, html: str) -> Dict[str, List[str]]:
        """Parse retired drivers and their groups (based on Scala parseRetiredNumberGroup)."""
        soup = BeautifulSoup(html, 'html.parser')
        retired_drivers = {}

        retired_elements = soup.select(".final-results-stage")
        for element in retired_elements:
            parent = element.parent
            if parent:
                number_elem = parent.select_one(".final-results-number")
                group_elem = parent.select_one(".final-results-cat")
                special_group_elem = parent.select_one(".startlist-m")

                if number_elem:
                    number = number_elem.get_text().strip()
                    groups = []

                    if group_elem:
                        # Split by <br> tags (like Scala code)
                        group_html = str(group_elem)
                        group_parts = re.split(r'<br\s*/?>', group_html)
                        groups.extend([BeautifulSoup(part, 'html.parser').get_text().strip() for part in group_parts if part.strip()])

                    if special_group_elem:
                        special_text = special_group_elem.get_text().strip()
                        if special_text:
                            groups.append(special_text)

                    retired_drivers[number] = groups

        return retired_drivers

    def _parse_full_driver_names_from_pages(self, rally_id: str, final_html: str) -> Dict[str, str]:
        """Parse full driver names from entries and final pages."""
        full_names = {}

        # First, try to get names from the entries page
        try:
            entries_url = f"{self.BASE_URL}/entries/{rally_id}/"
            if self.debug:
                print(f"Fetching full driver names from entries page: {entries_url}")

            entries_response = self.session.get(entries_url)
            if entries_response.status_code == 200:
                entries_soup = BeautifulSoup(entries_response.text, 'html.parser')

                # Look for entry rows in the entries page - try multiple table selectors
                entry_rows = entries_soup.select("table.results tbody tr")
                if not entry_rows:
                    entry_rows = entries_soup.select("table tbody tr")
                if not entry_rows:
                    entry_rows = entries_soup.select("tbody tr")
                if not entry_rows:
                    # Try even more general selectors
                    entry_rows = entries_soup.select("table tr")
                    # Filter out header rows
                    entry_rows = [row for row in entry_rows if row.select("td")]

                if self.debug:
                    print(f"Found {len(entry_rows)} entry rows in entries page")
                    if entry_rows:
                        # Show structure of first few rows for debugging
                        for i, row in enumerate(entry_rows[:3]):
                            cells = row.select("td")
                            print(f"  Row {i+1}: {len(cells)} cells")
                            for j, cell in enumerate(cells[:5]):  # Show first 5 cells
                                text = cell.get_text().strip()[:50]  # Truncate long text
                                print(f"    Cell {j+1}: '{text}'")
                            print()

                for row in entry_rows:
                    cells = row.select("td")
                    if len(cells) >= 2:
                        # Extract entry number - try multiple selectors
                        entry_number = ""

                        # Try primary selector
                        number_elem = row.select_one("td span.font-weight-bold.text-primary")
                        if number_elem:
                            entry_number = number_elem.get_text().strip().replace('#', '')

                        # Try alternative selectors for entry number
                        if not entry_number:
                            for cell in cells[:3]:  # Check first 3 cells
                                text = cell.get_text().strip()
                                if text.startswith("#"):
                                    entry_number = text.replace('#', '')
                                    break
                                elif text.isdigit() and len(text) <= 3:
                                    entry_number = text
                                    break
                                # Also check for patterns like "1." or "01"
                                elif re.match(r'^\d{1,3}\.?$', text):
                                    entry_number = text.replace('.', '')
                                    break

                        if entry_number:
                            # Extract full driver/codriver names
                            # Look for driver name in various places
                            driver_name = ""
                            codriver_name = ""

                            # Method 1: Look for links (driver names are often in links)
                            name_links = row.select("td a")
                            if self.debug and name_links:
                                print(f"    Found {len(name_links)} name links for #{entry_number}")
                                for i, link in enumerate(name_links):
                                    print(f"      Link {i+1}: '{link.get_text().strip()}'")

                            if len(name_links) >= 2:
                                # Two links usually mean driver and codriver
                                driver_name = name_links[0].get_text().strip()
                                codriver_name = name_links[1].get_text().strip()
                            elif len(name_links) == 1:
                                # One link might contain both names
                                combined_name = name_links[0].get_text().strip()
                                if " - " in combined_name or " / " in combined_name:
                                    # Already combined format
                                    driver_name = combined_name
                                else:
                                    # Single driver name - look for codriver in other cells
                                    driver_name = combined_name

                            # Method 2: Look for names in separate cells or combined in one cell
                            if not driver_name or not codriver_name:
                                if self.debug:
                                    print(f"    Searching cells for names (current: driver='{driver_name}', codriver='{codriver_name}')")

                                for i, cell in enumerate(cells):
                                    cell_text = cell.get_text().strip()

                                    if self.debug and cell_text:
                                        print(f"      Cell {i+1}: '{cell_text[:50]}'")

                                    # Skip entry number and other non-name cells
                                    if (cell_text and
                                        not cell_text.startswith("#") and
                                        not cell_text.replace('.', '').isdigit() and
                                        not self._is_ui_text(cell_text) and
                                        len(cell_text) > 2):

                                        # Check if this cell contains both driver and codriver (common format)
                                        if (" - " in cell_text or " / " in cell_text) and not driver_name:
                                            driver_name = cell_text
                                            if self.debug:
                                                print(f"        Found combined name: '{cell_text}'")
                                            break

                                        # Check if this looks like a single name
                                        elif any(c.isalpha() for c in cell_text) and " " in cell_text:
                                            if not driver_name:
                                                driver_name = cell_text
                                                if self.debug:
                                                    print(f"        Found driver name: '{cell_text}'")
                                            elif not codriver_name and cell_text != driver_name:
                                                codriver_name = cell_text
                                                if self.debug:
                                                    print(f"        Found codriver name: '{cell_text}'")

                                            # If we found both, break
                                            if driver_name and codriver_name:
                                                break

                            # Combine driver and codriver names
                            if driver_name and codriver_name:
                                full_name = f"{driver_name} - {codriver_name}"
                            elif driver_name:
                                full_name = driver_name
                            else:
                                continue

                            # Validate and store the name
                            if full_name and not self._is_ui_text(full_name):
                                full_names[entry_number] = full_name
                                if self.debug:
                                    print(f"Found full name for #{entry_number}: {full_name}")

                if self.debug:
                    print(f"Extracted {len(full_names)} full names from entries page")

        except Exception as e:
            if self.debug:
                print(f"Could not fetch entries page: {e}")

        # Also try to get names from the final page
        try:
            final_soup = BeautifulSoup(final_html, 'html.parser')

            # Look for final results table
            final_table = final_soup.select_one("table.results")
            if final_table:
                final_rows = final_table.select("tbody tr")
                for row in final_rows:
                    cells = row.select("td")
                    if len(cells) >= 3:
                        # Extract entry number
                        number_elem = row.select_one("td span.font-weight-bold.text-primary")
                        if number_elem:
                            entry_number = number_elem.get_text().strip().replace('#', '')

                            # Extract full driver name
                            name_elem = row.select_one("td a")
                            if name_elem:
                                full_name = name_elem.get_text().strip()
                                if full_name and not self._is_ui_text(full_name) and entry_number not in full_names:
                                    full_names[entry_number] = full_name
                                    if self.debug:
                                        print(f"Found full name from final page for #{entry_number}: {full_name}")

        except Exception as e:
            if self.debug:
                print(f"Could not parse final page for names: {e}")

        return full_names

    def _parse_stage_specific_penalties(self, soup: BeautifulSoup) -> Dict[str, int]:
        """Parse stage-specific penalties from penalty panels (based on Scala code)."""
        penalties = {}

        try:
            # Find info panels that contain penalty information
            info_panels = soup.select("main#main-section div.mt-3")

            for panel in info_panels:
                # Look for panels with "Penalty" in the header
                h6_elements = panel.select("h6")
                is_penalty_panel = any("Penalty" in h6.get_text() for h6 in h6_elements)

                if is_penalty_panel:
                    if self.debug:
                        print(f"Found penalty panel")

                    # Parse penalty rows from the table
                    penalty_rows = panel.select("tr")
                    for row in penalty_rows:
                        try:
                            # Extract driver/codriver name from link
                            name_elem = row.select_one("a")
                            if not name_elem:
                                continue

                            driver_codriver_name = name_elem.get_text().strip()

                            # Extract penalty time from red bold cell (like Scala code)
                            penalty_elem = row.select_one("td.text-danger.font-weight-bold")
                            if not penalty_elem:
                                continue

                            penalty_text = penalty_elem.get_text().strip().split()[0]  # Take first part before space
                            penalty_ms = self._get_duration_ms(penalty_text)

                            penalties[driver_codriver_name] = penalty_ms

                            if self.debug:
                                print(f"Found stage penalty: {driver_codriver_name} -> {penalty_text} = {penalty_ms}ms")

                        except Exception as e:
                            if self.debug:
                                print(f"Warning: Could not parse penalty row: {e}")
                            continue

        except Exception as e:
            if self.debug:
                print(f"Warning: Could not parse stage penalties: {e}")

        return penalties

    def _parse_penalties_from_penalty_page(self, rally_id: str) -> Dict[str, int]:
        """Parse penalties from the dedicated penalty page."""
        penalties = {}

        try:
            penalty_url = f"{self.BASE_URL}/penalty/{rally_id}/"
            if self.debug:
                print(f"Fetching penalties from: {penalty_url}")

            response = self.session.get(penalty_url)
            response.raise_for_status()

            soup = BeautifulSoup(response.text, 'html.parser')

            # Parse the entire page text to extract penalties
            page_text = soup.get_text()

            if self.debug:
                print(f"Parsing penalty page text...")

            # Look for penalty patterns in the page text
            # Pattern: #number followed by penalty time
            penalty_pattern = r'#(\d+)[^#]*?(\d{1,2}:\d{2})'
            penalty_matches = re.findall(penalty_pattern, page_text, re.DOTALL)

            if self.debug:
                print(f"Found {len(penalty_matches)} penalty matches")

            for entry_number, penalty_time in penalty_matches:
                try:
                    # Parse penalty time
                    time_parts = penalty_time.split(':')
                    if len(time_parts) == 2:
                        minutes = int(time_parts[0])
                        seconds = int(time_parts[1])
                        penalty_ms = (minutes * 60 + seconds) * 1000

                        # Add to existing penalty if driver already has one (for multiple penalties)
                        if entry_number in penalties:
                            penalties[entry_number] += penalty_ms
                            if self.debug:
                                print(f"Added additional penalty for #{entry_number}: {penalty_time} = {penalty_ms}ms (total: {penalties[entry_number]}ms)")
                        else:
                            penalties[entry_number] = penalty_ms
                            if self.debug:
                                print(f"Found penalty for #{entry_number}: {penalty_time} = {penalty_ms}ms")

                except ValueError:
                    if self.debug:
                        print(f"Warning: Could not parse penalty time '{penalty_time}' for #{entry_number}")
                    continue

        except Exception as e:
            if self.debug:
                print(f"Warning: Could not fetch penalty page: {e}")

        return penalties

    def _parse_stage_ids(self, html: str, rally_id: str) -> List[int]:
        """Extract stage IDs from the final results page (based on Scala parseStageIds)."""
        soup = BeautifulSoup(html, 'html.parser')

        # Use the exact selector from Scala code (with proper CSS syntax)
        selector = f'main#main-section div a.badge[href^="/results/{rally_id}/?s"][title^="SS"]'
        stage_links = soup.select(selector)

        # If that doesn't work, try more general selectors
        if not stage_links:
            stage_links = soup.select(f'a[href*="/results/{rally_id}/?s="]')

        stage_ids = []
        stage_info = {}  # For debugging

        for link in stage_links:
            href = link.get('href', '')
            text = link.get_text().strip()

            # Extract stage ID from URL (like Scala: href.split("=").last.toInt)
            stage_match = re.search(r's=(\d+)', href)
            if stage_match:
                stage_id = int(stage_match.group(1))
                stage_ids.append(stage_id)

                # Extract stage number for debugging
                stage_num_match = re.search(r'SS(\d+)', text)
                if stage_num_match:
                    stage_info[stage_id] = int(stage_num_match.group(1))

        # Remove duplicates while preserving order
        unique_stages = []
        seen = set()
        for stage_id in stage_ids:
            if stage_id not in seen:
                unique_stages.append(stage_id)
                seen.add(stage_id)

        if self.debug:
            print(f"Found {len(unique_stages)} unique stages:")
            for stage_id in unique_stages:
                stage_num = stage_info.get(stage_id, '?')
                print(f"  Stage ID {stage_id} -> SS{stage_num}")
        else:
            print(f"Found {len(unique_stages)} stages: {unique_stages}")

        return unique_stages

    def _parse_stage_ids_from_results_page(self, html: str, rally_id: str) -> List[int]:
        """Parse stage IDs from the main results page (more reliable than final page)."""
        soup = BeautifulSoup(html, 'html.parser')

        # Look for numbered stage links like [1], [2], [3], etc.
        stage_links = soup.select(f'a[href*="/results/{rally_id}/?s="]')

        stage_ids = []
        stage_info = {}

        for link in stage_links:
            href = link.get('href', '')
            text = link.get_text().strip()
            title = link.get('title', '')

            # Extract stage ID from URL
            stage_match = re.search(r's=(\d+)', href)
            if stage_match:
                stage_id = int(stage_match.group(1))

                # Only include if it looks like a stage number (1, 2, 3, etc.) or has SS in title
                if text.isdigit() or 'SS' in title or 'SS' in text:
                    stage_ids.append(stage_id)

                    # Extract stage info for debugging
                    if title:
                        stage_info[stage_id] = title
                    elif text.isdigit():
                        stage_info[stage_id] = f"Stage {text}"

        # Remove duplicates while preserving order
        unique_stages = []
        seen = set()
        for stage_id in stage_ids:
            if stage_id not in seen:
                unique_stages.append(stage_id)
                seen.add(stage_id)

        if self.debug:
            print(f"Found {len(unique_stages)} stages from results page:")
            for stage_id in unique_stages:
                info = stage_info.get(stage_id, 'Unknown')
                print(f"  Stage ID {stage_id}: {info}")

        return unique_stages

    def _discover_additional_stages(self, rally_id: str, known_stage_ids: List[int]) -> List[int]:
        """Discover additional stages by checking sequential IDs around known stages."""
        if not known_stage_ids:
            return []

        additional_stages = []
        min_stage_id = min(known_stage_ids)
        max_stage_id = max(known_stage_ids)

        # Check a few IDs after the maximum known stage
        for stage_id in range(max_stage_id + 1, max_stage_id + 5):
            if self._stage_exists(rally_id, stage_id):
                additional_stages.append(stage_id)
                if self.debug:
                    print(f"Discovered additional stage: {stage_id}")
            else:
                # Stop at first non-existent stage
                break

        # Also check a few IDs before the minimum (in case we missed early stages)
        for stage_id in range(min_stage_id - 1, min_stage_id - 3, -1):
            if stage_id > 0 and self._stage_exists(rally_id, stage_id):
                additional_stages.append(stage_id)
                if self.debug:
                    print(f"Discovered earlier stage: {stage_id}")
            else:
                break

        return additional_stages

    def _stage_exists(self, rally_id: str, stage_id: int) -> bool:
        """Check if a stage exists and belongs to this rally."""
        try:
            url = f"{self.BASE_URL}/results/{rally_id}/?s={stage_id}"
            response = self.session.get(url, timeout=10)

            # Check if we got a valid stage page
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')

                # Look for stage header to confirm it's a valid stage
                h5_element = soup.select_one("main#main-section h5")
                if not h5_element or "SS" not in h5_element.get_text():
                    return False

                # Check if this stage belongs to our rally by verifying the URL structure
                # The stage should redirect to or contain our rally ID in the breadcrumb/navigation

                # Check if the final URL contains our rally ID (after any redirects)
                final_url = response.url
                if rally_id in final_url:
                    return True

                # Check if the page content contains our rally ID in navigation links
                nav_links = soup.select("a[href*='{}']".format(rally_id))
                if nav_links:
                    return True

                # Check for breadcrumb or navigation that contains our rally
                breadcrumb_elements = soup.select("nav a, .breadcrumb a, main a")
                for element in breadcrumb_elements:
                    href = element.get('href', '')
                    if rally_id in href:
                        return True

                # If we can't confirm it belongs to our rally, reject it
                if self.debug:
                    print(f"Stage {stage_id} rejected - no rally ID match in URL or navigation")
                return False

            return False
        except Exception:
            return False

    def _fetch_stage_results(self, rally_id: str, stage_id: int, retired_drivers: Dict[str, List[str]], full_driver_names: Dict[str, str]) -> List[Entry]:
        """Fetch results for a specific stage."""
        url = f"{self.BASE_URL}/results/{rally_id}/?s={stage_id}"
        if self.debug:
            print(f"Fetching stage results from: {url}")
        response = self.session.get(url)
        response.raise_for_status()

        return self._parse_stage_results(response.text, retired_drivers, full_driver_names)

    def _parse_stage_results(self, html: str, retired_drivers: Dict[str, List[str]], full_driver_names: Dict[str, str]) -> List[Entry]:
        """Parse stage results from HTML (completely rewritten for better accuracy)."""
        soup = BeautifulSoup(html, 'html.parser')

        # Extract comprehensive stage information
        stage_number, stage_name, stage_distance_km, stage_date, stage_start_time = self._parse_stage_header_info(soup)

        if self.debug:
            print(f"Stage info - Number: {stage_number}, Name: {stage_name}, Distance: {stage_distance_km}km, Date: {stage_date}, Start: {stage_start_time}")

        # Check if stage was cancelled
        stage_cancelled = bool(soup.select("main#main-section div#stage-results span.badge-danger"))

        # Parse stage-specific penalties from penalty panels (like Scala code)
        stage_penalties = self._parse_stage_specific_penalties(soup)
        if self.debug and stage_penalties:
            print(f"Found {len(stage_penalties)} penalties for stage {stage_number}: {stage_penalties}")

        # Get all results tables - try multiple selectors to catch all tables
        all_tables = soup.select("main#main-section div#stage-results table.results")
        if not all_tables:
            # Fallback: try broader selectors
            all_tables = soup.select("main#main-section table.results")
        if not all_tables:
            # Fallback: try even broader selector
            all_tables = soup.select("table.results")

        if self.debug:
            print(f"Using selector found {len(all_tables)} tables")
            # Also try to find tables without the .results class
            all_tables_broad = soup.select("main#main-section table")
            print(f"Broader selector (all tables) found {len(all_tables_broad)} tables")

            # Let's try using the broader selector to see if we find the missing entries
            if len(all_tables_broad) > len(all_tables):
                print(f"Trying broader selector to find missing entries...")
                all_tables = all_tables_broad

        if not all_tables:
            if self.debug:
                print(f"No results tables found for stage {stage_number}")
            return []

        if self.debug:
            print(f"Found {len(all_tables)} results tables")
            for i, table in enumerate(all_tables):
                rows = table.select("tr")
                print(f"Table {i+1}: {len(rows)} rows")

        # Parse stage results from ALL tables (not just the first one)
        stage_entries = []
        all_found_entries = []

        for table_idx, stage_table in enumerate(all_tables):
            # Try tbody first, then fall back to direct tr selection
            stage_rows = stage_table.select("tbody tr")
            if not stage_rows:
                stage_rows = stage_table.select("tr")
                # Skip header row if it exists
                if stage_rows and not stage_rows[0].select("td"):
                    stage_rows = stage_rows[1:]

            if self.debug:
                print(f"Table {table_idx+1}: Found {len(stage_rows)} rows")
                # Debug: Show all entry numbers found in this table
                found_entries = []
                for row in stage_rows:
                    cells = row.select("td")
                    if len(cells) >= 4:
                        number_elem = row.select_one("td span.font-weight-bold.text-primary")
                        if number_elem:
                            entry_num = number_elem.get_text().strip()
                            found_entries.append(entry_num)
                        else:
                            # Try alternative selectors
                            for cell in cells[:3]:
                                text = cell.get_text().strip()
                                if text.startswith("#") or text.isdigit():
                                    found_entries.append(text)
                                    break
                print(f"Table {table_idx+1} entry numbers: {found_entries}")
                all_found_entries.extend(found_entries)

            # Parse rows from this table
            for i, row in enumerate(stage_rows):
                try:
                    entry = self._parse_stage_row(row, stage_number, stage_name, stage_distance_km, stage_date, stage_start_time, stage_cancelled, full_driver_names)
                    if entry:
                        stage_entries.append(entry)
                    elif self.debug:
                        print(f"Table {table_idx+1} Row {i+1}: Failed to parse entry (returned None)")
                except Exception as e:
                    if self.debug:
                        print(f"Table {table_idx+1} Row {i+1}: Exception parsing stage row: {e}")
                    continue

        if self.debug:
            print(f"Total entries found across all tables: {all_found_entries}")
            print(f"Successfully parsed {len(stage_entries)} entries")

        # Parse overall results from the last table (if exists)
        if len(all_tables) > 1:
            overall_table = all_tables[-1]

            # Try tbody first, then fall back to direct tr selection
            overall_rows = overall_table.select("tbody tr")
            if not overall_rows:
                overall_rows = overall_table.select("tr")
                # Skip header row if it exists
                if overall_rows and not overall_rows[0].select("td"):
                    overall_rows = overall_rows[1:]

            if self.debug:
                print(f"Found {len(overall_rows)} rows in overall table")

            # Create a map of entry number to overall data
            overall_data = {}
            for row in overall_rows:
                try:
                    number_elem = row.select_one("td span.font-weight-bold.text-primary")
                    if number_elem:
                        entry_number = number_elem.get_text().strip()

                        # Extract overall time and penalties
                        time_cells = row.select("td.font-weight-bold.text-right")
                        overall_time_ms = None
                        penalties_ms = 0

                        if time_cells:
                            # Last time cell is usually overall time
                            time_text = time_cells[-1].get_text().strip()
                            if time_text and time_text != "-":
                                overall_time_ms = self._get_duration_ms(time_text)

                        # Look for penalty information in the row
                        # Penalties are often shown as separate time values or in specific cells
                        all_cells = row.select("td")
                        for cell in all_cells:
                            cell_text = cell.get_text().strip()
                            # Look for penalty patterns like "1:00", "0:10", "5:00" (without dots)
                            if re.match(r'^\d{1,2}:\d{2}$', cell_text) and cell_text != "0:00":
                                # This looks like a penalty time (MM:SS format without milliseconds)
                                try:
                                    penalty_parts = cell_text.split(':')
                                    if len(penalty_parts) == 2:
                                        minutes = int(penalty_parts[0])
                                        seconds = int(penalty_parts[1])
                                        penalties_ms = (minutes * 60 + seconds) * 1000
                                        break
                                except ValueError:
                                    continue

                        overall_data[entry_number] = {
                            'overall_time_ms': overall_time_ms,
                            'penalties_ms': penalties_ms
                        }
                except Exception as e:
                    if self.debug:
                        print(f"Warning: Could not parse overall row: {e}")
                    continue

            # Update stage entries with overall data
            for entry in stage_entries:
                if entry.entry_number in overall_data:
                    entry.overall_time_ms = overall_data[entry.entry_number]['overall_time_ms']
                    entry.penalties_ms = overall_data[entry.entry_number]['penalties_ms']

        # Apply stage-specific penalties to entries (like Scala code)
        for entry in stage_entries:
            if entry.driver_codriver in stage_penalties:
                entry.penalties_ms = stage_penalties[entry.driver_codriver]
                if self.debug:
                    print(f"Applied penalty {entry.penalties_ms}ms to {entry.driver_codriver} (#{entry.entry_number})")

        # Parse retired drivers for this stage
        retired_entries = self._parse_retired_entries_for_stage(soup, stage_number, stage_name, retired_drivers, full_driver_names)

        return stage_entries + retired_entries

    def _get_stage_number_and_name(self, stage_text: str) -> Tuple[int, str]:
        """Parse stage number and name from stage header (based on Scala getStageNumberAndName)."""
        # Handle formats like "SS1 Stage Name - 12.34 km" or "SS1 Stage Name"
        stage_match = re.match(r'SS(\d+)\s+([^-]+)(?:\s*-\s*[\d.]+\s*km)?', stage_text.strip())
        if stage_match:
            return int(stage_match.group(1)), stage_match.group(2).strip()
        else:
            raise ValueError(f"Unable to parse stage number and name from [{stage_text}]")

    def _parse_stage_header_info(self, soup: BeautifulSoup) -> Tuple[int, str, Optional[float], Optional[str], Optional[str]]:
        """Parse comprehensive stage information from the stage page header."""
        # Extract stage number and name from h5 element
        h5_element = soup.select_one("main#main-section h5")
        if not h5_element:
            raise ValueError("Could not find stage header")

        stage_text = h5_element.get_text().strip()

        # Parse the complete header format: "SS11 Avgeniki 3 - 8 km - 11. 5. 15:48"
        stage_number = None
        stage_name = None
        stage_distance_km = None
        stage_date = None
        stage_start_time = None

        # Try to parse the full format with date and time
        full_match = re.match(r'SS(\d+)\s+([^-]+?)\s*-\s*(\d+\.?\d*)\s*km\s*-\s*(\d{1,2})\.\s*(\d{1,2})\.\s*(\d{1,2}:\d{2})', stage_text)
        if full_match:
            stage_number = int(full_match.group(1))
            stage_name = full_match.group(2).strip()
            stage_distance_km = float(full_match.group(3))
            day = full_match.group(4)
            month = full_match.group(5)
            stage_start_time = full_match.group(6)
            stage_date = f"{day}.{month}."

            if self.debug:
                print(f"Parsed full header: SS{stage_number} {stage_name} - {stage_distance_km}km - {stage_date} {stage_start_time}")
        else:
            # Fallback to basic parsing
            stage_number, stage_name = self._get_stage_number_and_name(stage_text)

            # Extract stage distance from the header text
            distance_match = re.search(r'(\d+\.?\d*)\s*km', stage_text)
            if distance_match:
                stage_distance_km = float(distance_match.group(1))

            # If basic parsing didn't get date/time, try to find them in the header text
            if not stage_date or not stage_start_time:
                # Look for date pattern like "11. 5." in the header
                date_match = re.search(r'(\d{1,2})\.\s*(\d{1,2})\.\s*(\d{1,2}:\d{2})', stage_text)
                if date_match:
                    day = date_match.group(1)
                    month = date_match.group(2)
                    stage_start_time = date_match.group(3)
                    stage_date = f"{day}.{month}."

                    if self.debug:
                        print(f"Parsed date/time from header: {stage_date} {stage_start_time}")

        return stage_number, stage_name, stage_distance_km, stage_date, stage_start_time

    def _parse_retired_entries_for_stage(self, soup: BeautifulSoup, stage_number: int, stage_name: str, retired_drivers: Dict[str, List[str]], full_driver_names: Dict[str, str]) -> List[Entry]:
        """Parse retired entries for this specific stage (based on Scala retired parsing)."""
        retired_entries = []

        # Find retirement info panels
        info_panels = soup.select("main#main-section div.mt-3")
        for panel in info_panels:
            if 'Retirement' in panel.get_text():
                retired_rows = panel.select("tr")
                for row in retired_rows:
                    try:
                        # Extract country from flag
                        country = self._get_country(row)

                        # Extract entry number first
                        number_elem = row.select_one("td.font-weight-bold.text-primary")
                        entry_number = number_elem.get_text().strip() if number_elem else ""

                        # Extract driver/codriver name - first try full names from entries/final pages
                        driver_codriver = ""
                        entry_number_clean = entry_number.replace('#', '')

                        if entry_number_clean in full_driver_names:
                            driver_codriver = full_driver_names[entry_number_clean]
                            if self.debug:
                                print(f"Using full name for retired #{entry_number_clean}: {driver_codriver}")
                        else:
                            # Fallback to parsing from retirement section
                            name_elem = row.select_one("a")
                            driver_codriver = name_elem.get_text().strip() if name_elem else ""
                            if self.debug:
                                print(f"Using fallback name for retired #{entry_number_clean}: {driver_codriver}")

                        # Extract car
                        car_elem = row.select_one("td.retired-car")
                        car = car_elem.get_text().strip() if car_elem else ""

                        # Get group from retired drivers map
                        group = retired_drivers.get(entry_number, [])

                        retired_entry = Entry(
                            stage_number=stage_number,
                            stage_name=stage_name,
                            stage_distance_km=None,  # Not available for retired entries
                            stage_date=None,  # Not available for retired entries
                            stage_start_time=None,  # Not available for retired entries
                            country=country,
                            driver_codriver=driver_codriver,
                            entry_number=entry_number,
                            group=group,
                            car=car,
                            stage_time_ms=None,
                            overall_time_ms=None,
                            penalties_ms=0,
                            super_rally=False,
                            active=False,
                            nominal_time=False,
                            comments=None
                        )
                        retired_entries.append(retired_entry)
                    except Exception as e:
                        if self.debug:
                            print(f"Warning: Could not parse retired entry: {e}")
                        continue

        return retired_entries

    def _parse_stage_row(self, row, stage_number: int, stage_name: str, stage_distance_km: Optional[float], stage_date: Optional[str], stage_start_time: Optional[str], stage_cancelled: bool, full_driver_names: Dict[str, str]) -> Optional[Entry]:
        """Parse a single stage row from the results table."""
        # Skip rows without table cells
        cells = row.select("td")
        if len(cells) < 4:  # Need at least position, number, driver, time
            if self.debug:
                print(f"Skipping row with {len(cells)} cells (need at least 4)")
            return None

        try:
            # Extract entry number (usually in second or third cell)
            entry_number = ""
            number_elem = row.select_one("td span.font-weight-bold.text-primary")
            if number_elem:
                entry_number = number_elem.get_text().strip()

            if not entry_number:
                # Try alternative selectors
                for cell in cells[:3]:  # Check first 3 cells
                    text = cell.get_text().strip()
                    if text.startswith("#") or text.isdigit():
                        entry_number = text
                        break

            if not entry_number:
                if self.debug:
                    print(f"Could not extract entry number from row with {len(cells)} cells")
                return None



            # Extract country from flag
            country = self._get_country(row)

            # Extract driver/codriver name - first try full names from entries/final pages
            driver_codriver = ""
            entry_number_clean = entry_number.replace('#', '')

            if entry_number_clean in full_driver_names:
                driver_codriver = full_driver_names[entry_number_clean]
                if self.debug:
                    print(f"Using full name for #{entry_number_clean}: {driver_codriver}")
            else:
                # Fallback to parsing from stage page
                name_elem = row.select_one("td a")
                if name_elem:
                    driver_codriver = name_elem.get_text().strip()
                else:
                    # Try to find driver name in cells
                    for cell in cells:
                        text = cell.get_text().strip()
                        if " - " in text and not text.startswith("#"):  # Driver - Codriver format
                            driver_codriver = text
                            break

            # Extract car information (more sophisticated approach)
            car = ""

            # Look for car in the driver cell - often it's after the driver name
            driver_cell = row.select_one("td a")
            if driver_cell:
                parent_cell = driver_cell.parent
                if parent_cell:
                    # Get all text from the cell and try to extract car
                    cell_html = str(parent_cell)
                    # Split by <br> or other separators
                    parts = re.split(r'<br\s*/?>', cell_html)
                    for part in parts:
                        clean_part = BeautifulSoup(part, 'html.parser').get_text().strip()
                        # If it's not the driver name and looks like a car, use it
                        if clean_part and clean_part != driver_codriver and not clean_part.startswith("#"):
                            # Check if it looks like a car name (contains brand names)
                            car_brands = [
    'Toyota', 'Ford', 'Škoda', 'Skoda', 'Renault', 'Mitsubishi', 'Subaru',
    'Peugeot', 'Citroen', 'Citroën', 'Hyundai', 'Volkswagen', 'VW', 'BMW',
    'Audi', 'Honda', 'Nissan', 'Lancia', 'Porsche', 'Fiat', 'Mazda', 'Opel',
    'Chevrolet', 'Suzuki', 'Mini', 'Seat', 'Dacia', 'Kia', 'Volvo', 'Saab',
    'Alfa Romeo', 'Talbot', 'MG', 'Ferrari', 'Lamborghini', 'Bentley',
    'Jaguar', 'Proton', 'Datsun', 'Daewoo', 'Rover', 'Tesla', 'Bugatti',
    'Lotus', 'Abarth', 'Zastava', 'Trabant', 'Moskvitch', 'Lada', 'Polonez',
    'Wartburg', 'Innocenti', 'Simca', 'Isuzu', 'Chrysler', 'Dodge', 'Jeep',
    'Lincoln', 'Cadillac', 'Buick', 'Pontiac', 'Hummer', 'GMC', 'Ram',
    'Holden', 'Perodua', 'Tata', 'Mahindra', 'BYD', 'Geely', 'Chery', 'Great Wall',
    'SsangYong', 'Genesis', 'Lucid', 'Rimac', 'Koenigsegg'
]
                            if any(brand.lower() in clean_part.lower() for brand in car_brands):
                                car = clean_part
                                break

            # If still no car found, look in other cells
            if not car:
                for cell in cells[3:]:  # Skip first few cells (position, number, driver)
                    text = cell.get_text().strip()
                    if text and not self._looks_like_time(text) and not text.startswith("+") and len(text) > 3:
                        car = text
                        break

            # Extract group information
            group = []
            group_elem = row.select_one("td.px-1")
            if group_elem:
                # Handle <br> tags like before
                separator = "---"
                group_elem_copy = BeautifulSoup(str(group_elem), 'html.parser')
                for br in group_elem_copy.select("br"):
                    br.replace_with(f" {separator} ")
                group_text = group_elem_copy.get_text()
                group = [g.strip() for g in group_text.split(separator) if g.strip()]
                group = [g if g else "No group" for g in group]

            # Extract stage time (look for time format in cells)
            stage_time_ms = None
            nominal_time = False

            # Look for time in the rightmost cells
            for cell in reversed(cells):
                cell_text = cell.get_text().strip()
                if "[N]" in cell_text:
                    nominal_time = True

                # Remove markers and try to parse time
                clean_text = cell_text.replace("[N]", "").replace("[SR]", "").strip()
                if self._looks_like_time(clean_text):
                    try:
                        stage_time_ms = self._get_duration_ms(clean_text)
                        break
                    except:
                        continue

            # Check for Super Rally
            super_rally = "[SR]" in row.get_text()

            # Set stage time to 0 if stage was cancelled
            if stage_cancelled and stage_time_ms:
                stage_time_ms = 0
                nominal_time = True



            return Entry(
                stage_number=stage_number,
                stage_name=stage_name,
                stage_distance_km=stage_distance_km,
                stage_date=stage_date,
                stage_start_time=stage_start_time,
                country=country,
                driver_codriver=driver_codriver,
                entry_number=entry_number,
                group=group,
                car=car,
                stage_time_ms=stage_time_ms,
                overall_time_ms=None,  # Will be filled later
                penalties_ms=0,  # Would need additional parsing
                super_rally=super_rally,
                active=True,  # Assume active if in stage results
                nominal_time=nominal_time,
                comments=None
            )

        except Exception as e:
            if self.debug:
                print(f"Error parsing stage row: {e}")
            return None

    def _looks_like_time(self, text: str) -> bool:
        """Check if text looks like a time format."""
        if not text or text == "-":
            return False

        # Check for time patterns like MM:SS.sss or HH:MM:SS.sss
        time_patterns = [
            r'^\d{1,2}:\d{2}\.\d{1,3}$',  # MM:SS.sss
            r'^\d{1,2}:\d{2}:\d{2}\.\d{1,3}$',  # HH:MM:SS.sss
            r'^\d{1,2}:\d{2}$',  # MM:SS
            r'^\d{1,2}:\d{2}:\d{2}$'  # HH:MM:SS
        ]

        for pattern in time_patterns:
            if re.match(pattern, text):
                return True

        return False

    def _parse_single_stage_entry(self, row, stage_number: int, stage_name: str, stage_cancelled: bool) -> Optional[Entry]:
        """Parse a single stage entry from a table row (based on Scala stage entry parsing)."""
        # Extract country from flag
        country = self._get_country(row)

        # Extract driver/codriver name
        name_elem = row.select_one("td.position-relative > a")
        driver_codriver = name_elem.get_text().strip() if name_elem else ""

        # Extract car
        car_elem = row.select_one("td.position-relative > span")
        car = car_elem.get_text().strip() if car_elem else ""

        # Extract entry number
        number_elem = row.select_one("td.text-left span.font-weight-bold.text-primary")
        entry_number = number_elem.get_text().strip() if number_elem else ""

        # Extract group information (like Scala code with separator handling)
        group_elem = row.select_one("td.px-1")
        group = []
        if group_elem:
            # Replace <br> tags with separators for parsing (exactly like Scala code)
            separator = "---"
            group_elem_copy = BeautifulSoup(str(group_elem), 'html.parser')
            for br in group_elem_copy.select("br"):
                br.replace_with(f" {separator} ")
            group_text = group_elem_copy.get_text()
            group = [g.strip() for g in group_text.split(separator) if g.strip()]
            group = [g if g else "No group" for g in group]

        # Extract stage time (like Scala code)
        time_elem = row.select_one("td.font-weight-bold.text-right")
        stage_time_ms = None
        nominal_time = False

        if time_elem:
            nominal_time = "[N]" in time_elem.get_text()
            # Remove span elements (like [N] markers) - exactly like Scala code
            time_elem_copy = BeautifulSoup(str(time_elem), 'html.parser')
            for span in time_elem_copy.select("span"):
                span.decompose()
            time_text = time_elem_copy.get_text().strip()
            if time_text and time_text != "-":
                stage_time_ms = self._get_duration_ms(time_text)

        # Check for Super Rally
        super_rally = "[SR]" in car

        # Set stage time to 0 if stage was cancelled (like Scala code)
        if stage_cancelled and stage_time_ms:
            stage_time_ms = 0

        return Entry(
            stage_number=stage_number,
            stage_name=stage_name,
            stage_distance_km=None,  # Not available in this parsing method
            stage_date=None,  # Not available in this parsing method
            stage_start_time=None,  # Not available in this parsing method
            country=country,
            driver_codriver=driver_codriver,
            entry_number=entry_number,
            group=group,
            car=car,
            stage_time_ms=stage_time_ms,
            overall_time_ms=None,
            penalties_ms=0,  # Would need additional parsing for penalties
            super_rally=super_rally,
            active=True,
            nominal_time=nominal_time or stage_cancelled,
            comments=None  # Would need additional parsing for comments
        )

    # ========== Utility Functions (from Scala Util.scala and Ewrc.scala) ==========

    def _is_ui_text(self, text: str) -> bool:
        """Check if text looks like UI elements rather than driver names."""
        if not text:
            return True

        # Common UI text patterns to filter out
        ui_patterns = [
            'show entry info and stats',
            'show info',
            'entry info',
            'stats',
            'view',
            'details',
            'more info',
            'click here',
            'expand',
            'collapse',
            'toggle',
            'menu',
            'navigation',
            'home',
            'back',
            'next',
            'previous',
            'search',
            'filter',
            'sort',
            'results',
            'stage',
            'final',
            'penalty',
            'retirement'
        ]

        text_lower = text.lower().strip()

        # Check for exact matches or if UI text is contained
        for pattern in ui_patterns:
            if pattern in text_lower:
                return True

        # Check for very short text (likely not names)
        if len(text.strip()) < 3:
            return True

        # Check for text that's all uppercase (often UI labels)
        if text.isupper() and len(text) > 10:
            return True

        # Check for text with no letters (numbers only, symbols)
        if not any(c.isalpha() for c in text):
            return True

        return False

    def _get_country(self, row) -> str:
        """Extract country from flag image (based on Scala getCountry function)."""
        flag_img = row.select_one("td img.flag-s")
        if not flag_img:
            return "unknown"

        src = flag_img.get('src', '')
        if not src:
            return "unknown"

        # Extract country code from flag image path
        country_code = src.split('/')[-1].split('.')[0]

        # Map special cases (exactly from Scala code)
        country_mapping = {
            'uk': 'united kingdom',
            'saudi_arabia': 'saudi arabia',
            'costa_rica': 'costa rica',
            'nederland': 'netherlands',
            'jar': 'south africa',
            'newzealand': 'new zealand'
        }

        return country_mapping.get(country_code, country_code)

    def _get_duration_ms(self, time_str: str) -> int:
        """Parse time string to milliseconds (based on Scala getDurationMs function)."""
        time_str = time_str.strip()
        if not time_str or time_str == "-":
            return 0

        parts = time_str.split(':')

        if len(parts) == 3:  # hours:minutes:seconds.tenths
            hours, minutes, seconds_and_tenths = parts
            total_seconds = int(hours) * 3600 + int(minutes) * 60
            ms = self._to_ms(seconds_and_tenths)
            return total_seconds * 1000 + ms
        elif len(parts) == 2:  # minutes:seconds.tenths
            minutes, seconds_and_tenths = parts
            total_seconds = int(minutes) * 60
            ms = self._to_ms(seconds_and_tenths)
            return total_seconds * 1000 + ms
        elif len(parts) == 1:  # seconds.tenths
            return self._to_ms(parts[0])
        else:
            raise ValueError(f"Unable to parse stage time from {time_str}")

    def _to_ms(self, seconds_str: str) -> int:
        """Convert seconds.tenths to milliseconds (based on Scala toMs extension)."""
        if not seconds_str:
            return 0

        # Clean the string - remove any extra characters
        clean_str = seconds_str.strip()

        if '.' in clean_str:
            parts = clean_str.split('.')
            if len(parts) == 2:
                seconds, tenths = parts
                try:
                    # Pad tenths to 3 digits and take first 3 (like Scala: padTo(3, '0').take(3))
                    tenths_padded = tenths.ljust(3, '0')[:3]
                    return int(seconds) * 1000 + int(tenths_padded)
                except ValueError:
                    # If conversion fails, try to extract just the numeric part
                    seconds_clean = re.sub(r'[^\d]', '', seconds)
                    tenths_clean = re.sub(r'[^\d]', '', tenths)[:3].ljust(3, '0')
                    if seconds_clean and tenths_clean:
                        return int(seconds_clean) * 1000 + int(tenths_clean)
        else:
            try:
                return int(clean_str) * 1000
            except ValueError:
                # Extract just the numeric part
                numeric_part = re.sub(r'[^\d]', '', clean_str)
                if numeric_part:
                    return int(numeric_part) * 1000

        return 0


def main():
    """Main function to demonstrate usage."""
    import argparse

    parser = argparse.ArgumentParser(description='Fetch EWRC rally results')
    parser.add_argument('rally_id', help='Rally ID (e.g., "93042-olympiako-rally-2025")')
    parser.add_argument('--debug', action='store_true', help='Enable debug output')

    args = parser.parse_args()

    rally_id = args.rally_id
    fetcher = EWRCCompleteFetcher(debug=args.debug)

    try:
        print(f"Fetching EWRC rally info for: {rally_id}")
        rally_info = fetcher.fetch_rally_info(rally_id)

        print("\n=== RALLY INFO ===")
        print(f"Name: {rally_info.name}")
        print(f"Championship: {', '.join(rally_info.championship)}")
        print(f"Dates: {rally_info.start_date} - {rally_info.end_date}")
        print(f"Distance: {rally_info.distance_meters / 1000:.2f} km")
        print(f"Entries: {rally_info.total_entries} (Finished: {rally_info.finished})")

        print(f"\nFetching detailed results...")
        results = fetcher.fetch_rally_results(rally_id)

        print(f"\n=== RESULTS SUMMARY ===")
        print(f"Total entries: {len(results)}")

        # Group by stage
        stages = {}
        for entry in results:
            stage_key = f"SS{entry.stage_number} {entry.stage_name}"
            if stage_key not in stages:
                stages[stage_key] = []
            stages[stage_key].append(entry)

        print(f"Stages: {len(stages)}")
        for stage_name, entries in stages.items():
            active_count = sum(1 for e in entries if e.active)
            retired_count = len(entries) - active_count
            print(f"  {stage_name}: {active_count} active, {retired_count} retired")

        # Show some sample data
        if results:
            print(f"\n=== SAMPLE ENTRIES ===")
            for i, entry in enumerate(results[:3]):
                print(f"Entry {i+1}:")
                print(f"  Stage: SS{entry.stage_number} {entry.stage_name}")
                if entry.stage_distance_km:
                    print(f"  Distance: {entry.stage_distance_km} km")
                if entry.stage_date:
                    print(f"  Date: {entry.stage_date}")
                if entry.stage_start_time:
                    print(f"  Start Time: {entry.stage_start_time}")
                print(f"  Driver: {entry.driver_codriver}")
                print(f"  Number: {entry.entry_number}")
                print(f"  Country: {entry.country}")
                print(f"  Group: {', '.join(entry.group)}")
                print(f"  Car: {entry.car}")
                if entry.stage_time_ms:
                    time_seconds = entry.stage_time_ms / 1000
                    minutes = int(time_seconds // 60)
                    seconds = time_seconds % 60
                    print(f"  Time: {minutes}:{seconds:06.3f}")
                print(f"  Active: {entry.active}")
                if entry.super_rally:
                    print(f"  Super Rally: Yes")
                if entry.nominal_time:
                    print(f"  Nominal Time: Yes")
                print()

        # Save to JSON file
        output_file = f"ewrc_complete_results_{rally_id.replace('/', '_')}.json"
        output_data = {
            'rally_info': asdict(rally_info),
            'results': [asdict(entry) for entry in results]
        }

        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, indent=2, ensure_ascii=False)

        print(f"Complete results saved to: {output_file}")

    except Exception as e:
        print(f"Error: {e}")
        if fetcher.debug:
            import traceback
            traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()