[{"stageNumber": 1, "stageName": "Super Special Ka<PERSON>ani", "country": "estonia", "userName": "Tänak Ott - Järveoja Martin", "realName": "", "group": ["RC1", "M"], "car": "Ford Puma Rally1 Hybrid", "stageTimeMs": 194300, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["Safari Rally Kenya is a demanding one. Let's see after Saturday. I would say this day will tell us a lot."]}, {"stageNumber": 1, "stageName": "Super Special Ka<PERSON>ani", "country": "france", "userName": "Ogier Sébastien - Landais V.", "realName": "", "group": ["RC1", "M"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 194400, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["Similar stages to the previous two years, but different conditions. A bit more rough from what we have some seen in recce. I need some Kenyan gods this weekend to help me. Luck will be needed!"]}, {"stageNumber": 1, "stageName": "Super Special Ka<PERSON>ani", "country": "finland", "userName": "Rovanperä Kalle - Halttunen J.", "realName": "", "group": ["RC1", "M"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 196700, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["It's gonna be a new challenge this year. Different from last year. Definitely more rough. Let's try to do the same as last year. We just need to be steady and keep a good pace. At least the start was better."]}, {"stageNumber": 1, "stageName": "Super Special Ka<PERSON>ani", "country": "belgium", "userName": "Neuville Thierry - Wydaeghe M.", "realName": "", "group": ["RC1", "M"], "car": "Hyundai i20 N Rally1 Hybrid", "stageTimeMs": 197000, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["It's a tough event. Nobody knows what is going to happen. It will be a big surprise. There is going to be ups and downs for all of us during the weekend but hopefully more ups than downs."]}, {"stageNumber": 1, "stageName": "Super Special Ka<PERSON>ani", "country": "united kingdom", "userName": "<PERSON> - <PERSON>", "realName": "", "group": ["RC1", "M"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 197600, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "Super Special Ka<PERSON>ani", "country": "finland", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC1", "M"], "car": "Hyundai i20 N Rally1 Hybrid", "stageTimeMs": 198300, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "Super Special Ka<PERSON>ani", "country": "japan", "userName": "<PERSON><PERSON><PERSON> - <PERSON>", "realName": "", "group": ["RC1"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 199400, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["Thanks to the team - they fixed the car perfectly and I had a very good feeling. This stage is one that you can really enjoy and it was very slippy. I will try to enjoy tomorrow."]}, {"stageNumber": 1, "stageName": "Super Special Ka<PERSON>ani", "country": "spain", "userName": "<PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC1", "M"], "car": "Hyundai i20 N Rally1 Hybrid", "stageTimeMs": 200200, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 10000, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "Super Special Ka<PERSON>ani", "country": "france", "userName": "<PERSON><PERSON> - Gilsoul N.", "realName": "", "group": ["RC1", "M"], "car": "Ford Puma Rally1 Hybrid", "stageTimeMs": 202300, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["It will be a big, big challenge. Hopefully we can do a good rally and nothing breaks with the car and the same with us."]}, {"stageNumber": 1, "stageName": "Super Special Ka<PERSON>ani", "country": "sweden", "userName": "Solberg Oliver - <PERSON><PERSON> E.", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 204000, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["I think it will be a lot different. It's great to start here with all the fans and the people. The car looks like a proper Safari car but it's going to be a tough, long week."]}, {"stageNumber": 1, "stageName": "Super Special Ka<PERSON>ani", "country": "greece", "userName": "<PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["RC1"], "car": "Ford Puma Rally1 Hybrid", "stageTimeMs": 205600, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "Super Special Ka<PERSON>ani", "country": "poland", "userName": "Ka<PERSON>anowicz K. - Szczepaniak M.", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 207200, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "Super Special Ka<PERSON>ani", "country": "luxembourg", "userName": "Munster Grégoire - <PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Ford Fiesta Rally2", "stageTimeMs": 208000, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["We are really looking forward to it. This one is all about the show and having fun, but there was more grip than I expected."]}, {"stageNumber": 1, "stageName": "Super Special Ka<PERSON>ani", "country": "czech", "userName": "Prokop Martin - Jůrka <PERSON>ě<PERSON>", "realName": "", "group": ["RC2"], "car": "Ford Fiesta Rally2", "stageTimeMs": 209400, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "Super Special Ka<PERSON>ani", "country": "germany", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 209700, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "Super Special Ka<PERSON>ani", "country": "poland", "userName": "Chwist <PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia RS Rally2", "stageTimeMs": 209700, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "Super Special Ka<PERSON>ani", "country": "kenya", "userName": "<PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia R5", "stageTimeMs": 215900, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["It's been a long road getting here. I've just switched over to rally mode, and this is a lot easier!"]}, {"stageNumber": 1, "stageName": "Super Special Ka<PERSON>ani", "country": "spain", "userName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Sanjuan R.", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 217400, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "Super Special Ka<PERSON>ani", "country": "paraguay", "userName": "Domínguez Diego Jr. - Peñate R.", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 220600, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["I think this year is going to be tougher than last year so we are just going to try and keep it steady, and try to finish this time."]}, {"stageNumber": 1, "stageName": "Super Special Ka<PERSON>ani", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia R5", "stageTimeMs": 223800, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "Super Special Ka<PERSON>ani", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 227400, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "Super Special Ka<PERSON>ani", "country": "canada", "userName": "<PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 228900, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "Super Special Ka<PERSON>ani", "country": "greece", "userName": "<PERSON><PERSON><PERSON><PERSON> - Krawszik Tom", "realName": "", "group": ["RC2"], "car": "Ford Fiesta Rally2", "stageTimeMs": 229300, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "Super Special Ka<PERSON>ani", "country": "kenya", "userName": "<PERSON> - <PERSON>", "realName": "", "group": ["RC2"], "car": "Ford Fiesta R5", "stageTimeMs": 229500, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "Super Special Ka<PERSON>ani", "country": "kenya", "userName": "<PERSON> - <PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 229600, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "Super Special Ka<PERSON>ani", "country": "kenya", "userName": "<PERSON><PERSON>", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 230500, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["We are really looking forward to it. So far, not the best start, but it's alright. It's early days yet."]}, {"stageNumber": 1, "stageName": "Super Special Ka<PERSON>ani", "country": "united kingdom", "userName": "<PERSON><PERSON><PERSON>", "realName": "", "group": ["NAT", "NR4"], "car": "Mitsubishi Lancer Evo X", "stageTimeMs": 230800, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "Super Special Ka<PERSON>ani", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["NAT", "NR4"], "car": "Mitsubishi Lancer Evo X", "stageTimeMs": 231000, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "Super Special Ka<PERSON>ani", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> Jeremiah - <PERSON><PERSON><PERSON>", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 233900, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "Super Special Ka<PERSON>ani", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON>", "realName": "", "group": ["NAT", "NR4"], "car": "Mitsubishi Lancer Evo X R4", "stageTimeMs": 240300, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 1, "stageName": "Super Special Ka<PERSON>ani", "country": "italy", "userName": "Davi<PERSON> - Vindevogel S.", "realName": "", "group": ["RC2"], "car": "Ford Fiesta R5", "stageTimeMs": 266800, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Loldia 1", "country": "france", "userName": "Ogier Sébastien - Landais V.", "realName": "", "group": ["RC1", "M"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 830300, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["I tried to have a decent speed but nothing crazy because there's a long weekend ahead."]}, {"stageNumber": 2, "stageName": "Loldia 1", "country": "finland", "userName": "Rovanperä Kalle - Halttunen J.", "realName": "", "group": ["RC1", "M"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 837800, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["This one downhill section which is really narrow with lots of loose stuff was tricky and I'm sure it will clean. I was even struggling to make the hairpins there, but otherwise safe and clean run through!"]}, {"stageNumber": 2, "stageName": "Loldia 1", "country": "united kingdom", "userName": "<PERSON> - <PERSON>", "realName": "", "group": ["RC1", "M"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 838700, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["It's not so rough in terms of loose stones but there's a lot of bad bumps. It's tricky to judge the pace."]}, {"stageNumber": 2, "stageName": "Loldia 1", "country": "belgium", "userName": "Neuville Thierry - Wydaeghe M.", "realName": "", "group": ["RC1", "M"], "car": "Hyundai i20 N Rally1 Hybrid", "stageTimeMs": 839000, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["It's very shaky in there and we got kicked off the line and we had a big bee in the car"]}, {"stageNumber": 2, "stageName": "Loldia 1", "country": "japan", "userName": "<PERSON><PERSON><PERSON> - <PERSON>", "realName": "", "group": ["RC1"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 839900, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["I don't know if it's cleaning or not but I was quite careful. We will see - step by step moving forward and we will see how it's going."]}, {"stageNumber": 2, "stageName": "Loldia 1", "country": "spain", "userName": "<PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC1", "M"], "car": "Hyundai i20 N Rally1 Hybrid", "stageTimeMs": 842000, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["We saw some zebras in the middle of the stage and we were stuck behind them."]}, {"stageNumber": 2, "stageName": "Loldia 1", "country": "estonia", "userName": "Tänak Ott - Järveoja Martin", "realName": "", "group": ["RC1", "M"], "car": "Ford Puma Rally1 Hybrid", "stageTimeMs": 845500, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["We had a group of zebras so we had to stop for them a few times."]}, {"stageNumber": 2, "stageName": "Loldia 1", "country": "finland", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC1", "M"], "car": "Hyundai i20 N Rally1 Hybrid", "stageTimeMs": 847800, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["We stayed on the road the whole time, so this is a good start! I didn't use all the open space where I saw some lines and I used the wrong side of the trees sometimes."]}, {"stageNumber": 2, "stageName": "Loldia 1", "country": "france", "userName": "<PERSON><PERSON> - Gilsoul N.", "realName": "", "group": ["RC1", "M"], "car": "Ford Puma Rally1 Hybrid", "stageTimeMs": 864500, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["We have no power."]}, {"stageNumber": 2, "stageName": "Loldia 1", "country": "sweden", "userName": "Solberg Oliver - <PERSON><PERSON> E.", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 874400, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["I was pushing at the start, but this is my first time here in a Rally2 car so quickly realised I had to take it calmer than in a WRC car, good stage though!"]}, {"stageNumber": 2, "stageName": "Loldia 1", "country": "poland", "userName": "Ka<PERSON>anowicz K. - Szczepaniak M.", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 908300, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["At the beginning of the rally, my mum was calling to me. She said: 'Let <PERSON><PERSON> go,' and I did it!"]}, {"stageNumber": 2, "stageName": "Loldia 1", "country": "luxembourg", "userName": "Munster Grégoire - <PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Ford Fiesta Rally2", "stageTimeMs": 917600, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["I tried to push a bit. I thought I had a front-left puncture but I didn't."]}, {"stageNumber": 2, "stageName": "Loldia 1", "country": "greece", "userName": "<PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["RC1"], "car": "Ford Puma Rally1 Hybrid", "stageTimeMs": 917900, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["Of course, we like the difficulty here - we like the challenge! We lost a little bit of the brakes in the downhill, but the rest was not so bad. Let's see."]}, {"stageNumber": 2, "stageName": "Loldia 1", "country": "czech", "userName": "Prokop Martin - Jůrka <PERSON>ě<PERSON>", "realName": "", "group": ["RC2"], "car": "Ford Fiesta Rally2", "stageTimeMs": 928200, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Loldia 1", "country": "germany", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 930900, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Loldia 1", "country": "kenya", "userName": "<PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia R5", "stageTimeMs": 963000, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Loldia 1", "country": "poland", "userName": "Chwist <PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia RS Rally2", "stageTimeMs": 966300, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Loldia 1", "country": "kenya", "userName": "<PERSON><PERSON>", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 1000300, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Loldia 1", "country": "paraguay", "userName": "Domínguez Diego Jr. - Peñate R.", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 1007500, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Loldia 1", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia R5", "stageTimeMs": 1012000, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Loldia 1", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["NAT", "NR4"], "car": "Mitsubishi Lancer Evo X", "stageTimeMs": 1033300, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Loldia 1", "country": "canada", "userName": "<PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 1042100, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Loldia 1", "country": "spain", "userName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Sanjuan R.", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 1062200, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Loldia 1", "country": "kenya", "userName": "<PERSON> - <PERSON>", "realName": "", "group": ["RC2"], "car": "Ford Fiesta R5", "stageTimeMs": 1075700, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Loldia 1", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 1077300, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Loldia 1", "country": "italy", "userName": "Davi<PERSON> - Vindevogel S.", "realName": "", "group": ["RC2"], "car": "Ford Fiesta R5", "stageTimeMs": 1089500, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 10000, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Loldia 1", "country": "greece", "userName": "<PERSON><PERSON><PERSON><PERSON> - Krawszik Tom", "realName": "", "group": ["RC2"], "car": "Ford Fiesta Rally2", "stageTimeMs": 1110200, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Loldia 1", "country": "united kingdom", "userName": "<PERSON><PERSON><PERSON>", "realName": "", "group": ["NAT", "NR4"], "car": "Mitsubishi Lancer Evo X", "stageTimeMs": 1129000, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Loldia 1", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON>", "realName": "", "group": ["NAT", "NR4"], "car": "Mitsubishi Lancer Evo X R4", "stageTimeMs": 1153600, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Loldia 1", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> Jeremiah - <PERSON><PERSON><PERSON>", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 1184100, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 2, "stageName": "Loldia 1", "country": "kenya", "userName": "<PERSON> - <PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 1193700, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Geothermal 1", "country": "finland", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC1", "M"], "car": "Hyundai i20 N Rally1 Hybrid", "stageTimeMs": 409700, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["Okay. For sure over here it's clear, it's more Finland style with the cuts. In the last stage I didn't take cuts others did because of the penalty last year. Next stages are not so simple."]}, {"stageNumber": 3, "stageName": "Geothermal 1", "country": "france", "userName": "Ogier Sébastien - Landais V.", "realName": "", "group": ["RC1", "M"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 410900, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["I think in this one there was some cleaning, but already last year <PERSON><PERSON> did a good job there first on the road, good time from me. These stages were not the roughest, hardest part of the loop coming now."]}, {"stageNumber": 3, "stageName": "Geothermal 1", "country": "japan", "userName": "<PERSON><PERSON><PERSON> - <PERSON>", "realName": "", "group": ["RC1"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 412000, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["Step by step I need to get back the feeling, the car is working good. This rally is not flat out everywhere."]}, {"stageNumber": 3, "stageName": "Geothermal 1", "country": "finland", "userName": "Rovanperä Kalle - Halttunen J.", "realName": "", "group": ["RC1", "M"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 413000, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["It's a totally different stage profile, faster and wider. There is more cleaning on this one, but it's so fast it doesn't matter."]}, {"stageNumber": 3, "stageName": "Geothermal 1", "country": "united kingdom", "userName": "<PERSON> - <PERSON>", "realName": "", "group": ["RC1", "M"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 414100, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["It was a clean run through the stage. Should have been quicker compared to <PERSON><PERSON>. A clean and tidy run."]}, {"stageNumber": 3, "stageName": "Geothermal 1", "country": "spain", "userName": "<PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC1", "M"], "car": "Hyundai i20 N Rally1 Hybrid", "stageTimeMs": 416700, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["I was driving a bit too careful in some places, the pacenotes were nice but I changed some corners with videos. I'm sad because I was enjoying the stage."]}, {"stageNumber": 3, "stageName": "Geothermal 1", "country": "france", "userName": "<PERSON><PERSON> - Gilsoul N.", "realName": "", "group": ["RC1", "M"], "car": "Ford Puma Rally1 Hybrid", "stageTimeMs": 417500, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["Nothing big, just setting issue. I was a bit cautious in some places, very high speed."]}, {"stageNumber": 3, "stageName": "Geothermal 1", "country": "estonia", "userName": "Tänak Ott - Järveoja Martin", "realName": "", "group": ["RC1", "M"], "car": "Ford Puma Rally1 Hybrid", "stageTimeMs": 419000, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["It's the same as previous rally, at speed I struggle a lot. Trying to get it through."]}, {"stageNumber": 3, "stageName": "Geothermal 1", "country": "belgium", "userName": "Neuville Thierry - Wydaeghe M.", "realName": "", "group": ["RC1", "M"], "car": "Hyundai i20 N Rally1 Hybrid", "stageTimeMs": 422200, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Geothermal 1", "country": "sweden", "userName": "Solberg Oliver - <PERSON><PERSON> E.", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 443500, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Geothermal 1", "country": "poland", "userName": "Ka<PERSON>anowicz K. - Szczepaniak M.", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 455000, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Geothermal 1", "country": "luxembourg", "userName": "Munster Grégoire - <PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Ford Fiesta Rally2", "stageTimeMs": 461200, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Geothermal 1", "country": "greece", "userName": "<PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["RC1"], "car": "Ford Puma Rally1 Hybrid", "stageTimeMs": 468000, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["This stage was much better and I enjoyed. We don't have the hybrid anymore, so we need to sort this issue."]}, {"stageNumber": 3, "stageName": "Geothermal 1", "country": "czech", "userName": "Prokop Martin - Jůrka <PERSON>ě<PERSON>", "realName": "", "group": ["RC2"], "car": "Ford Fiesta Rally2", "stageTimeMs": 472700, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Geothermal 1", "country": "germany", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 484700, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Geothermal 1", "country": "kenya", "userName": "<PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia R5", "stageTimeMs": 498500, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Geothermal 1", "country": "paraguay", "userName": "Domínguez Diego Jr. - Peñate R.", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 499600, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Geothermal 1", "country": "poland", "userName": "Chwist <PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia RS Rally2", "stageTimeMs": 502400, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Geothermal 1", "country": "kenya", "userName": "<PERSON><PERSON>", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 514900, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 10000, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Geothermal 1", "country": "canada", "userName": "<PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 521900, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Geothermal 1", "country": "spain", "userName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Sanjuan R.", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 526700, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Geothermal 1", "country": "italy", "userName": "Davi<PERSON> - Vindevogel S.", "realName": "", "group": ["RC2"], "car": "Ford Fiesta R5", "stageTimeMs": 537800, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Geothermal 1", "country": "kenya", "userName": "<PERSON> - <PERSON>", "realName": "", "group": ["RC2"], "car": "Ford Fiesta R5", "stageTimeMs": 542200, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 10000, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Geothermal 1", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 543100, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Geothermal 1", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["NAT", "NR4"], "car": "Mitsubishi Lancer Evo X", "stageTimeMs": 544200, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Geothermal 1", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia R5", "stageTimeMs": 552400, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Geothermal 1", "country": "kenya", "userName": "<PERSON> - <PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 557800, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Geothermal 1", "country": "greece", "userName": "<PERSON><PERSON><PERSON><PERSON> - Krawszik Tom", "realName": "", "group": ["RC2"], "car": "Ford Fiesta Rally2", "stageTimeMs": 562000, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Geothermal 1", "country": "united kingdom", "userName": "<PERSON><PERSON><PERSON>", "realName": "", "group": ["NAT", "NR4"], "car": "Mitsubishi Lancer Evo X", "stageTimeMs": 573000, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Geothermal 1", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> Jeremiah - <PERSON><PERSON><PERSON>", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 574400, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 3, "stageName": "Geothermal 1", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON>", "realName": "", "group": ["NAT", "NR4"], "car": "Mitsubishi Lancer Evo X R4", "stageTimeMs": 624800, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Kedong 1", "country": "finland", "userName": "Rovanperä Kalle - Halttunen J.", "realName": "", "group": ["RC1", "M"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 892400, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["It was a proper cleaning morning, but we did a good job, no mistakes. I could be quite happy."]}, {"stageNumber": 4, "stageName": "Kedong 1", "country": "belgium", "userName": "Neuville Thierry - Wydaeghe M.", "realName": "", "group": ["RC1", "M"], "car": "Hyundai i20 N Rally1 Hybrid", "stageTimeMs": 900400, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["(Time loss) It's a lot."]}, {"stageNumber": 4, "stageName": "Kedong 1", "country": "france", "userName": "Ogier Sébastien - Landais V.", "realName": "", "group": ["RC1", "M"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 901800, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["I had no hybrid all stage so it didn't help."]}, {"stageNumber": 4, "stageName": "Kedong 1", "country": "united kingdom", "userName": "<PERSON> - <PERSON>", "realName": "", "group": ["RC1", "M"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 906500, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["A bit of bad dust at the end there, but that wasn't the timeloss. Had to avoid big stones being pulled out, felt so soft at places. I wasn't taking enough risks."]}, {"stageNumber": 4, "stageName": "Kedong 1", "country": "spain", "userName": "<PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC1", "M"], "car": "Hyundai i20 N Rally1 Hybrid", "stageTimeMs": 914400, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["I tried to do my best with no mistakes, a lot of ruts. In the fast places the car moves quite a lot, many stones in the sides of the road. Not easy, nice stage but difficiult to feel the speed."]}, {"stageNumber": 4, "stageName": "Kedong 1", "country": "japan", "userName": "<PERSON><PERSON><PERSON> - <PERSON>", "realName": "", "group": ["RC1"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 915200, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["I'm not happy, I had big damage at the front and had to repair before the stage. But i'm here."]}, {"stageNumber": 4, "stageName": "Kedong 1", "country": "finland", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC1", "M"], "car": "Hyundai i20 N Rally1 Hybrid", "stageTimeMs": 922700, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["I don't know. Suddenly it went empty and that's it, like 13km before the finish."]}, {"stageNumber": 4, "stageName": "Kedong 1", "country": "france", "userName": "<PERSON><PERSON> - Gilsoul N.", "realName": "", "group": ["RC1", "M"], "car": "Ford Puma Rally1 Hybrid", "stageTimeMs": 930400, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Kedong 1", "country": "sweden", "userName": "Solberg Oliver - <PERSON><PERSON> E.", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 985100, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Kedong 1", "country": "estonia", "userName": "Tänak Ott - Järveoja Martin", "realName": "", "group": ["RC1", "M"], "car": "Ford Puma Rally1 Hybrid", "stageTimeMs": 1025000, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["It just came off the rim quite early in the ruts. Simple. We had some engine troubles and we didn't get power any more."]}, {"stageNumber": 4, "stageName": "Kedong 1", "country": "luxembourg", "userName": "Munster Grégoire - <PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Ford Fiesta Rally2", "stageTimeMs": 1036300, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Kedong 1", "country": "greece", "userName": "<PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["RC1"], "car": "Ford Puma Rally1 Hybrid", "stageTimeMs": 1039900, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Kedong 1", "country": "poland", "userName": "Ka<PERSON>anowicz K. - Szczepaniak M.", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 1073800, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Kedong 1", "country": "germany", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 1080200, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Kedong 1", "country": "czech", "userName": "Prokop Martin - Jůrka <PERSON>ě<PERSON>", "realName": "", "group": ["RC2"], "car": "Ford Fiesta Rally2", "stageTimeMs": 1093100, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Kedong 1", "country": "kenya", "userName": "<PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia R5", "stageTimeMs": 1111400, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Kedong 1", "country": "poland", "userName": "Chwist <PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia RS Rally2", "stageTimeMs": 1111900, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Kedong 1", "country": "kenya", "userName": "<PERSON><PERSON>", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 1123700, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Kedong 1", "country": "paraguay", "userName": "Domínguez Diego Jr. - Peñate R.", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 1166600, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Kedong 1", "country": "kenya", "userName": "<PERSON> - <PERSON>", "realName": "", "group": ["RC2"], "car": "Ford Fiesta R5", "stageTimeMs": 1200800, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Kedong 1", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia R5", "stageTimeMs": 1217200, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Kedong 1", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["NAT", "NR4"], "car": "Mitsubishi Lancer Evo X", "stageTimeMs": 1220700, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Kedong 1", "country": "italy", "userName": "Davi<PERSON> - Vindevogel S.", "realName": "", "group": ["RC2"], "car": "Ford Fiesta R5", "stageTimeMs": 1244800, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Kedong 1", "country": "canada", "userName": "<PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 1250700, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Kedong 1", "country": "spain", "userName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Sanjuan R.", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 1251100, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Kedong 1", "country": "kenya", "userName": "<PERSON> - <PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 1289400, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Kedong 1", "country": "united kingdom", "userName": "<PERSON><PERSON><PERSON>", "realName": "", "group": ["NAT", "NR4"], "car": "Mitsubishi Lancer Evo X", "stageTimeMs": 1299300, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Kedong 1", "country": "greece", "userName": "<PERSON><PERSON><PERSON><PERSON> - Krawszik Tom", "realName": "", "group": ["RC2"], "car": "Ford Fiesta Rally2", "stageTimeMs": 1354100, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Kedong 1", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> Jeremiah - <PERSON><PERSON><PERSON>", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 1359900, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Kedong 1", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 1382200, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 60000, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 4, "stageName": "Kedong 1", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON>", "realName": "", "group": ["NAT", "NR4"], "car": "Mitsubishi Lancer Evo X R4", "stageTimeMs": 1820700, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": true, "finished": true, "comment": [], "nominal": true}, {"stageNumber": 5, "stageName": "Loldia 2", "country": "france", "userName": "Ogier Sébastien - Landais V.", "realName": "", "group": ["RC1", "M"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 830200, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["My strategy is a bit different. I tried to drive more clean and gain the performance from the tyres. It's only in the last one that the risk is very high."]}, {"stageNumber": 5, "stageName": "Loldia 2", "country": "finland", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC1", "M"], "car": "Hyundai i20 N Rally1 Hybrid", "stageTimeMs": 838600, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["It more slippy than in the morning which I was not expecting. In some corners at the beginning you have good grip then it becomes really bad."]}, {"stageNumber": 5, "stageName": "Loldia 2", "country": "estonia", "userName": "Tänak Ott - Järveoja Martin", "realName": "", "group": ["RC1", "M"], "car": "Ford Puma Rally1 Hybrid", "stageTimeMs": 840400, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["We managed to improve the suspension a bit."]}, {"stageNumber": 5, "stageName": "Loldia 2", "country": "united kingdom", "userName": "<PERSON> - <PERSON>", "realName": "", "group": ["RC1", "M"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 840700, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Loldia 2", "country": "spain", "userName": "<PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC1", "M"], "car": "Hyundai i20 N Rally1 Hybrid", "stageTimeMs": 841600, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["It was good. Much more slippery than before because it's getting dry. In the first corner we went wide, but anyway, I am quite happy."]}, {"stageNumber": 5, "stageName": "Loldia 2", "country": "belgium", "userName": "Neuville Thierry - Wydaeghe M.", "realName": "", "group": ["RC1", "M"], "car": "Hyundai i20 N Rally1 Hybrid", "stageTimeMs": 844300, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["I think we are even slower than this morning, but generally the feeling was good."]}, {"stageNumber": 5, "stageName": "Loldia 2", "country": "finland", "userName": "Rovanperä Kalle - Halttunen J.", "realName": "", "group": ["RC1", "M"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 845500, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["I think we made a small mistake on the set-up because we expected to have more grip in the afternoon."]}, {"stageNumber": 5, "stageName": "Loldia 2", "country": "japan", "userName": "<PERSON><PERSON><PERSON> - <PERSON>", "realName": "", "group": ["RC1"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 849000, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Loldia 2", "country": "sweden", "userName": "Solberg Oliver - <PERSON><PERSON> E.", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 883300, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Loldia 2", "country": "luxembourg", "userName": "Munster Grégoire - <PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Ford Fiesta Rally2", "stageTimeMs": 904400, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Loldia 2", "country": "poland", "userName": "Ka<PERSON>anowicz K. - Szczepaniak M.", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 906400, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Loldia 2", "country": "greece", "userName": "<PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["RC1"], "car": "Ford Puma Rally1 Hybrid", "stageTimeMs": 908500, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["I think the grip is better but it's loose in some parts. More or less, it's not so bad."]}, {"stageNumber": 5, "stageName": "Loldia 2", "country": "czech", "userName": "Prokop Martin - Jůrka <PERSON>ě<PERSON>", "realName": "", "group": ["RC2"], "car": "Ford Fiesta Rally2", "stageTimeMs": 924900, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Loldia 2", "country": "germany", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 944300, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Loldia 2", "country": "kenya", "userName": "<PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia R5", "stageTimeMs": 953900, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Loldia 2", "country": "poland", "userName": "Chwist <PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia RS Rally2", "stageTimeMs": 956800, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Loldia 2", "country": "kenya", "userName": "<PERSON> - <PERSON>", "realName": "", "group": ["RC2"], "car": "Ford Fiesta R5", "stageTimeMs": 957300, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Loldia 2", "country": "kenya", "userName": "<PERSON><PERSON>", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 991400, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Loldia 2", "country": "france", "userName": "<PERSON><PERSON> - Gilsoul N.", "realName": "", "group": ["RC1", "M"], "car": "Ford Puma Rally1 Hybrid", "stageTimeMs": 996000, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["Under braking, in the middle there was a big step and we broke the wheel there."]}, {"stageNumber": 5, "stageName": "Loldia 2", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia R5", "stageTimeMs": 1003400, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Loldia 2", "country": "paraguay", "userName": "Domínguez Diego Jr. - Peñate R.", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 1014700, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Loldia 2", "country": "italy", "userName": "Davi<PERSON> - Vindevogel S.", "realName": "", "group": ["RC2"], "car": "Ford Fiesta R5", "stageTimeMs": 1017800, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 10000, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Loldia 2", "country": "canada", "userName": "<PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 1042400, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Loldia 2", "country": "spain", "userName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Sanjuan R.", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 1047800, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Loldia 2", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["NAT", "NR4"], "car": "Mitsubishi Lancer Evo X", "stageTimeMs": 1050400, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 480000, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Loldia 2", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 1050600, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Loldia 2", "country": "greece", "userName": "<PERSON><PERSON><PERSON><PERSON> - Krawszik Tom", "realName": "", "group": ["RC2"], "car": "Ford Fiesta Rally2", "stageTimeMs": 1069000, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Loldia 2", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> Jeremiah - <PERSON><PERSON><PERSON>", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 1069900, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 120000, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Loldia 2", "country": "kenya", "userName": "<PERSON> - <PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 1097700, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Loldia 2", "country": "united kingdom", "userName": "<PERSON><PERSON><PERSON>", "realName": "", "group": ["NAT", "NR4"], "car": "Mitsubishi Lancer Evo X", "stageTimeMs": 1127900, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 5, "stageName": "Loldia 2", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON>", "realName": "", "group": ["NAT", "NR4"], "car": "Mitsubishi Lancer Evo X R4", "stageTimeMs": 1650400, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": true, "finished": true, "comment": [], "nominal": true}, {"stageNumber": 6, "stageName": "Geothermal 2", "country": "france", "userName": "Ogier Sébastien - Landais V.", "realName": "", "group": ["RC1", "M"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 406500, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["I tried to drive very clean and now I can say it was the right choice."]}, {"stageNumber": 6, "stageName": "Geothermal 2", "country": "finland", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC1", "M"], "car": "Hyundai i20 N Rally1 Hybrid", "stageTimeMs": 407700, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["It was okay. All good. It's a different kind of challenge but I like it."]}, {"stageNumber": 6, "stageName": "Geothermal 2", "country": "finland", "userName": "Rovanperä Kalle - Halttunen J.", "realName": "", "group": ["RC1", "M"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 409100, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["Quite a different style of stage, so for sure the understeer was not so big."]}, {"stageNumber": 6, "stageName": "Geothermal 2", "country": "united kingdom", "userName": "<PERSON> - <PERSON>", "realName": "", "group": ["RC1", "M"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 409600, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["I'm just trying to keep it clean and tidy but it's still difficult to know the right balance to push."]}, {"stageNumber": 6, "stageName": "Geothermal 2", "country": "japan", "userName": "<PERSON><PERSON><PERSON> - <PERSON>", "realName": "", "group": ["RC1"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 410200, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["We are just focusing on our driving and luckily we have no water splashes today! There's a long way to go."]}, {"stageNumber": 6, "stageName": "Geothermal 2", "country": "estonia", "userName": "Tänak Ott - Järveoja Martin", "realName": "", "group": ["RC1", "M"], "car": "Ford Puma Rally1 Hybrid", "stageTimeMs": 411900, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["It's a bit hard for the suspension, but still quite smooth anyway."]}, {"stageNumber": 6, "stageName": "Geothermal 2", "country": "spain", "userName": "<PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC1", "M"], "car": "Hyundai i20 N Rally1 Hybrid", "stageTimeMs": 412800, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["It's a really nice stage, really nice to drive."]}, {"stageNumber": 6, "stageName": "Geothermal 2", "country": "france", "userName": "<PERSON><PERSON> - Gilsoul N.", "realName": "", "group": ["RC1", "M"], "car": "Ford Puma Rally1 Hybrid", "stageTimeMs": 416100, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["It's quite demanding, I would say. I tried to be careful because we had a puncture in the previous one and it's very hard on the car, you need to be careful."]}, {"stageNumber": 6, "stageName": "Geothermal 2", "country": "sweden", "userName": "Solberg Oliver - <PERSON><PERSON> E.", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 442100, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 6, "stageName": "Geothermal 2", "country": "poland", "userName": "Ka<PERSON>anowicz K. - Szczepaniak M.", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 449000, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 6, "stageName": "Geothermal 2", "country": "luxembourg", "userName": "Munster Grégoire - <PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Ford Fiesta Rally2", "stageTimeMs": 449700, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 6, "stageName": "Geothermal 2", "country": "greece", "userName": "<PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["RC1"], "car": "Ford Puma Rally1 Hybrid", "stageTimeMs": 456100, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["I think I improved quite well, it's about one second per kilometre so I am very happy."]}, {"stageNumber": 6, "stageName": "Geothermal 2", "country": "czech", "userName": "Prokop Martin - Jůrka <PERSON>ě<PERSON>", "realName": "", "group": ["RC2"], "car": "Ford Fiesta Rally2", "stageTimeMs": 467000, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 6, "stageName": "Geothermal 2", "country": "germany", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 472100, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 6, "stageName": "Geothermal 2", "country": "paraguay", "userName": "Domínguez Diego Jr. - Peñate R.", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 490000, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 6, "stageName": "Geothermal 2", "country": "kenya", "userName": "<PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia R5", "stageTimeMs": 492600, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 6, "stageName": "Geothermal 2", "country": "kenya", "userName": "<PERSON> - <PERSON>", "realName": "", "group": ["RC2"], "car": "Ford Fiesta R5", "stageTimeMs": 494100, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 6, "stageName": "Geothermal 2", "country": "poland", "userName": "Chwist <PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia RS Rally2", "stageTimeMs": 497200, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 6, "stageName": "Geothermal 2", "country": "kenya", "userName": "<PERSON><PERSON>", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 517800, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 6, "stageName": "Geothermal 2", "country": "spain", "userName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Sanjuan R.", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 523200, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 6, "stageName": "Geothermal 2", "country": "canada", "userName": "<PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 527100, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 6, "stageName": "Geothermal 2", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> Jeremiah - <PERSON><PERSON><PERSON>", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 538200, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 6, "stageName": "Geothermal 2", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 542400, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 6, "stageName": "Geothermal 2", "country": "kenya", "userName": "<PERSON> - <PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 542500, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 6, "stageName": "Geothermal 2", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia R5", "stageTimeMs": 543900, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 6, "stageName": "Geothermal 2", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["NAT", "NR4"], "car": "Mitsubishi Lancer Evo X", "stageTimeMs": 568100, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 6, "stageName": "Geothermal 2", "country": "united kingdom", "userName": "<PERSON><PERSON><PERSON>", "realName": "", "group": ["NAT", "NR4"], "car": "Mitsubishi Lancer Evo X", "stageTimeMs": 580000, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 6, "stageName": "Geothermal 2", "country": "italy", "userName": "Davi<PERSON> - Vindevogel S.", "realName": "", "group": ["RC2"], "car": "Ford Fiesta R5", "stageTimeMs": 590800, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 6, "stageName": "Geothermal 2", "country": "belgium", "userName": "Neuville Thierry - Wydaeghe M.", "realName": "", "group": ["RC1", "M"], "car": "Hyundai i20 N Rally1 Hybrid", "stageTimeMs": 1006500, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": true, "finished": true, "comment": [], "nominal": true}, {"stageNumber": 6, "stageName": "Geothermal 2", "country": "greece", "userName": "<PERSON><PERSON><PERSON><PERSON> - Krawszik Tom", "realName": "", "group": ["RC2"], "car": "Ford Fiesta Rally2", "stageTimeMs": 1113200, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 6, "stageName": "Geothermal 2", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON>", "realName": "", "group": ["NAT", "NR4"], "car": "Mitsubishi Lancer Evo X R4", "stageTimeMs": 1168100, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": true, "finished": true, "comment": [], "nominal": true}, {"stageNumber": 7, "stageName": "Kedong 2", "country": "france", "userName": "Ogier Sébastien - Landais V.", "realName": "", "group": ["RC1", "M"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 904600, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["Except for the hybrid issue this morning we had a perfect day, so we can be happy."]}, {"stageNumber": 7, "stageName": "Kedong 2", "country": "finland", "userName": "Rovanperä Kalle - Halttunen J.", "realName": "", "group": ["RC1", "M"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 907000, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["A lot of surprises, and it's a high-speed stage so not easy."]}, {"stageNumber": 7, "stageName": "Kedong 2", "country": "finland", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC1", "M"], "car": "Hyundai i20 N Rally1 Hybrid", "stageTimeMs": 907900, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["I was a bit careful but I think you should be in these conditions."]}, {"stageNumber": 7, "stageName": "Kedong 2", "country": "united kingdom", "userName": "<PERSON> - <PERSON>", "realName": "", "group": ["RC1", "M"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 915000, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["I think it was more of a bush than a tree, which we can probably be happy about! I got crossed up in these tricky ruts and didn't manage to catch it in time."]}, {"stageNumber": 7, "stageName": "Kedong 2", "country": "estonia", "userName": "Tänak Ott - Järveoja Martin", "realName": "", "group": ["RC1", "M"], "car": "Ford Puma Rally1 Hybrid", "stageTimeMs": 925900, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["I don't feel really comfortable to go any faster."]}, {"stageNumber": 7, "stageName": "Kedong 2", "country": "spain", "userName": "<PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC1", "M"], "car": "Hyundai i20 N Rally1 Hybrid", "stageTimeMs": 929400, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["We took a hairpin and we had all the dust in front of us. We were stopped for 10sec to clarify all the dust."]}, {"stageNumber": 7, "stageName": "Kedong 2", "country": "japan", "userName": "<PERSON><PERSON><PERSON> - <PERSON>", "realName": "", "group": ["RC1"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 932400, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["The puncture was maybe in the last 10km."]}, {"stageNumber": 7, "stageName": "Kedong 2", "country": "luxembourg", "userName": "Munster Grégoire - <PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Ford Fiesta Rally2", "stageTimeMs": 1032200, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 7, "stageName": "Kedong 2", "country": "poland", "userName": "Ka<PERSON>anowicz K. - Szczepaniak M.", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 1036100, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 7, "stageName": "Kedong 2", "country": "greece", "userName": "<PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["RC1"], "car": "Ford Puma Rally1 Hybrid", "stageTimeMs": 1058100, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 7, "stageName": "Kedong 2", "country": "france", "userName": "<PERSON><PERSON> - Gilsoul N.", "realName": "", "group": ["RC1", "M"], "car": "Ford Puma Rally1 Hybrid", "stageTimeMs": 1076800, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 7, "stageName": "Kedong 2", "country": "czech", "userName": "Prokop Martin - Jůrka <PERSON>ě<PERSON>", "realName": "", "group": ["RC2"], "car": "Ford Fiesta Rally2", "stageTimeMs": 1115900, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 7, "stageName": "Kedong 2", "country": "kenya", "userName": "<PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia R5", "stageTimeMs": 1129900, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 7, "stageName": "Kedong 2", "country": "paraguay", "userName": "Domínguez Diego Jr. - Peñate R.", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 1143600, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 7, "stageName": "Kedong 2", "country": "canada", "userName": "<PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 1210200, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 7, "stageName": "Kedong 2", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia R5", "stageTimeMs": 1253300, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 7, "stageName": "Kedong 2", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 1273100, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 7, "stageName": "Kedong 2", "country": "spain", "userName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Sanjuan R.", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 1287000, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 7, "stageName": "Kedong 2", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["NAT", "NR4"], "car": "Mitsubishi Lancer Evo X", "stageTimeMs": 1345800, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 7, "stageName": "Kedong 2", "country": "kenya", "userName": "<PERSON> - <PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 1373400, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 7, "stageName": "Kedong 2", "country": "germany", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 1392500, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 7, "stageName": "Kedong 2", "country": "united kingdom", "userName": "<PERSON><PERSON><PERSON>", "realName": "", "group": ["NAT", "NR4"], "car": "Mitsubishi Lancer Evo X", "stageTimeMs": 1394400, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 7, "stageName": "Kedong 2", "country": "poland", "userName": "Chwist <PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia RS Rally2", "stageTimeMs": 1410000, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 7, "stageName": "Kedong 2", "country": "kenya", "userName": "<PERSON> - <PERSON>", "realName": "", "group": ["RC2"], "car": "Ford Fiesta R5", "stageTimeMs": 1452000, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 7, "stageName": "Kedong 2", "country": "belgium", "userName": "Neuville Thierry - Wydaeghe M.", "realName": "", "group": ["RC1", "M"], "car": "Hyundai i20 N Rally1 Hybrid", "stageTimeMs": 1504600, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": true, "finished": true, "comment": [], "nominal": true}, {"stageNumber": 7, "stageName": "Kedong 2", "country": "sweden", "userName": "Solberg Oliver - <PERSON><PERSON> E.", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 1632200, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": true, "finished": true, "comment": [], "nominal": true}, {"stageNumber": 7, "stageName": "Kedong 2", "country": "greece", "userName": "<PERSON><PERSON><PERSON><PERSON> - Krawszik Tom", "realName": "", "group": ["RC2"], "car": "Ford Fiesta Rally2", "stageTimeMs": 1632200, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 250000, "superRally": true, "finished": true, "comment": [], "nominal": true}, {"stageNumber": 7, "stageName": "Kedong 2", "country": "italy", "userName": "Davi<PERSON> - Vindevogel S.", "realName": "", "group": ["RC2"], "car": "Ford Fiesta R5", "stageTimeMs": 1632200, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": true, "finished": true, "comment": [], "nominal": true}, {"stageNumber": 7, "stageName": "Kedong 2", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> Jeremiah - <PERSON><PERSON><PERSON>", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 1650700, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 7, "stageName": "Kedong 2", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON>", "realName": "", "group": ["NAT", "NR4"], "car": "Mitsubishi Lancer Evo X R4", "stageTimeMs": 1945800, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": true, "finished": true, "comment": [], "nominal": true}, {"stageNumber": 7, "stageName": "Kedong 2", "country": "kenya", "userName": "<PERSON><PERSON>", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 2049300, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 8, "stageName": "Soysambu 1", "country": "kenya", "userName": "<PERSON><PERSON>", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 0, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": false, "comment": []}, {"stageNumber": 8, "stageName": "Soysambu 1", "country": "france", "userName": "Ogier Sébastien - Landais V.", "realName": "", "group": ["RC1", "M"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 1050000, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 8, "stageName": "Soysambu 1", "country": "finland", "userName": "Rovanperä Kalle - Halttunen J.", "realName": "", "group": ["RC1", "M"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 1057600, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["You can always go quicker in this rally, it just depends how much you want to push on the rough places. I would say quite good stage. I lost some time on some easy places where I tried to smooth, but it didn't work."]}, {"stageNumber": 8, "stageName": "Soysambu 1", "country": "finland", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC1", "M"], "car": "Hyundai i20 N Rally1 Hybrid", "stageTimeMs": 1076800, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["All Ok, no drama, bit tricky in the first 2km with puddles and mud so quite a few surprises. On the fast section over there, I was too slow."]}, {"stageNumber": 8, "stageName": "Soysambu 1", "country": "japan", "userName": "<PERSON><PERSON><PERSON> - <PERSON>", "realName": "", "group": ["RC1"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 1084600, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["We stalled the engine at the start."]}, {"stageNumber": 8, "stageName": "Soysambu 1", "country": "estonia", "userName": "Tänak Ott - Järveoja Martin", "realName": "", "group": ["RC1", "M"], "car": "Ford Puma Rally1 Hybrid", "stageTimeMs": 1084900, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["For sure some moments, some muddy places, you don't see them coming. It is just an adventure, it is a proper zoo!"]}, {"stageNumber": 8, "stageName": "Soysambu 1", "country": "spain", "userName": "<PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC1", "M"], "car": "Hyundai i20 N Rally1 Hybrid", "stageTimeMs": 1092700, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["The beginning was very slippery, especially the first corners at the beginning, then really bumpy. The car is not working well in these places..."]}, {"stageNumber": 8, "stageName": "Soysambu 1", "country": "belgium", "userName": "Neuville Thierry - Wydaeghe M.", "realName": "", "group": ["RC1", "M"], "car": "Hyundai i20 N Rally1 Hybrid", "stageTimeMs": 1117100, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["I don't know if it is the car or the road, but something is wrong. It is very hard to see the road, I struggled alot."]}, {"stageNumber": 8, "stageName": "Soysambu 1", "country": "united kingdom", "userName": "<PERSON> - <PERSON>", "realName": "", "group": ["RC1", "M"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 1118500, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["I was very slow and tried to lift the front..I don't know."]}, {"stageNumber": 8, "stageName": "Soysambu 1", "country": "sweden", "userName": "Solberg Oliver - <PERSON><PERSON> E.", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 1118700, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["Proper Safari conditions on the first stage this morning - mud, dust, watersplashes, puddles and more! Good run though, winning the stage by more than a minute."]}, {"stageNumber": 8, "stageName": "Soysambu 1", "country": "france", "userName": "<PERSON><PERSON> - Gilsoul N.", "realName": "", "group": ["RC1", "M"], "car": "Ford Puma Rally1 Hybrid", "stageTimeMs": 1163300, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["I was not seeing the road so it was very strange, very difficult."]}, {"stageNumber": 8, "stageName": "Soysambu 1", "country": "poland", "userName": "Ka<PERSON>anowicz K. - Szczepaniak M.", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 1188500, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 8, "stageName": "Soysambu 1", "country": "luxembourg", "userName": "Munster Grégoire - <PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Ford Fiesta Rally2", "stageTimeMs": 1192400, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 8, "stageName": "Soysambu 1", "country": "greece", "userName": "<PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["RC1"], "car": "Ford Puma Rally1 Hybrid", "stageTimeMs": 1231400, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["This one is an early wake up. Very slippery at the bottom, then some bumps...we took it a little bit carefully."]}, {"stageNumber": 8, "stageName": "Soysambu 1", "country": "kenya", "userName": "<PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia R5", "stageTimeMs": 1240700, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 8, "stageName": "Soysambu 1", "country": "czech", "userName": "Prokop Martin - Jůrka <PERSON>ě<PERSON>", "realName": "", "group": ["RC2"], "car": "Ford Fiesta Rally2", "stageTimeMs": 1265300, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 8, "stageName": "Soysambu 1", "country": "germany", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 1273200, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 8, "stageName": "Soysambu 1", "country": "poland", "userName": "Chwist <PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia RS Rally2", "stageTimeMs": 1281500, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 8, "stageName": "Soysambu 1", "country": "italy", "userName": "Davi<PERSON> - Vindevogel S.", "realName": "", "group": ["RC2"], "car": "Ford Fiesta R5", "stageTimeMs": 1342300, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 8, "stageName": "Soysambu 1", "country": "canada", "userName": "<PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 1367800, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 8, "stageName": "Soysambu 1", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia R5", "stageTimeMs": 1383800, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 8, "stageName": "Soysambu 1", "country": "paraguay", "userName": "Domínguez Diego Jr. - Peñate R.", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 1393800, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 8, "stageName": "Soysambu 1", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> Jeremiah - <PERSON><PERSON><PERSON>", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 1395100, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 8, "stageName": "Soysambu 1", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON>", "realName": "", "group": ["NAT", "NR4"], "car": "Mitsubishi Lancer Evo X R4", "stageTimeMs": 1410700, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 8, "stageName": "Soysambu 1", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 1429500, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 8, "stageName": "Soysambu 1", "country": "spain", "userName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Sanjuan R.", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 1477700, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 8, "stageName": "Soysambu 1", "country": "greece", "userName": "<PERSON><PERSON><PERSON><PERSON> - Krawszik Tom", "realName": "", "group": ["RC2"], "car": "Ford Fiesta Rally2", "stageTimeMs": 1490800, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": [], "nominal": true}, {"stageNumber": 8, "stageName": "Soysambu 1", "country": "united kingdom", "userName": "<PERSON><PERSON><PERSON>", "realName": "", "group": ["NAT", "NR4"], "car": "Mitsubishi Lancer Evo X", "stageTimeMs": 1498800, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 8, "stageName": "Soysambu 1", "country": "kenya", "userName": "<PERSON> - <PERSON>", "realName": "", "group": ["RC2"], "car": "Ford Fiesta R5", "stageTimeMs": 1553500, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 8, "stageName": "Soysambu 1", "country": "kenya", "userName": "<PERSON> - <PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 1718700, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": true, "finished": true, "comment": [], "nominal": true}, {"stageNumber": 8, "stageName": "Soysambu 1", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["NAT", "NR4"], "car": "Mitsubishi Lancer Evo X", "stageTimeMs": 2323400, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 9, "stageName": "Elmenteita 1", "country": "finland", "userName": "Rovanperä Kalle - Halttunen J.", "realName": "", "group": ["RC1", "M"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 505200, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["Quite rough in some places and I made one mistake as well."]}, {"stageNumber": 9, "stageName": "Elmenteita 1", "country": "france", "userName": "Ogier Sébastien - Landais V.", "realName": "", "group": ["RC1", "M"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 505800, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["All OK in this one, tried be careful..a very long one to go after this."]}, {"stageNumber": 9, "stageName": "Elmenteita 1", "country": "united kingdom", "userName": "<PERSON> - <PERSON>", "realName": "", "group": ["RC1", "M"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 507400, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["We have to keep doing what we came here to do but obviously there is no magic solution to bring that time back all in one stage."]}, {"stageNumber": 9, "stageName": "Elmenteita 1", "country": "finland", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC1", "M"], "car": "Hyundai i20 N Rally1 Hybrid", "stageTimeMs": 507600, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["It's a bloody long way to go still! Every stage is very tricky, anything can happen in every stage."]}, {"stageNumber": 9, "stageName": "Elmenteita 1", "country": "japan", "userName": "<PERSON><PERSON><PERSON> - <PERSON>", "realName": "", "group": ["RC1"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 510200, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["I need to take it easy a bit, but everything is fine. The next one if going to be very difficult and challenging."]}, {"stageNumber": 9, "stageName": "Elmenteita 1", "country": "belgium", "userName": "Neuville Thierry - Wydaeghe M.", "realName": "", "group": ["RC1", "M"], "car": "Hyundai i20 N Rally1 Hybrid", "stageTimeMs": 512000, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["I would say it was fun, I enjoyed this one. On the first one the conditions were so bad for us. We lost 3sec per km. We can just try to catch some points from the rally."]}, {"stageNumber": 9, "stageName": "Elmenteita 1", "country": "spain", "userName": "<PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC1", "M"], "car": "Hyundai i20 N Rally1 Hybrid", "stageTimeMs": 512500, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["It is really difficult to read the road."]}, {"stageNumber": 9, "stageName": "Elmenteita 1", "country": "estonia", "userName": "Tänak Ott - Järveoja Martin", "realName": "", "group": ["RC1", "M"], "car": "Ford Puma Rally1 Hybrid", "stageTimeMs": 512700, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["It's a challenge today - Safari challenge!"]}, {"stageNumber": 9, "stageName": "Elmenteita 1", "country": "france", "userName": "<PERSON><PERSON> - Gilsoul N.", "realName": "", "group": ["RC1", "M"], "car": "Ford Puma Rally1 Hybrid", "stageTimeMs": 533100, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["I am struggling to see the road."]}, {"stageNumber": 9, "stageName": "Elmenteita 1", "country": "sweden", "userName": "Solberg Oliver - <PERSON><PERSON> E.", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 536800, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["When you have a puncture [yesterday] you should change it. Obviously a very stupid mistake."]}, {"stageNumber": 9, "stageName": "Elmenteita 1", "country": "greece", "userName": "<PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["RC1"], "car": "Ford Puma Rally1 Hybrid", "stageTimeMs": 554600, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["This one was top, I enjoyed it very much."]}, {"stageNumber": 9, "stageName": "Elmenteita 1", "country": "poland", "userName": "Ka<PERSON>anowicz K. - Szczepaniak M.", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 561100, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["I tried but at the same time I try to not take a big risk."]}, {"stageNumber": 9, "stageName": "Elmenteita 1", "country": "luxembourg", "userName": "Munster Grégoire - <PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Ford Fiesta Rally2", "stageTimeMs": 568700, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["I think we had a slow puncture front right because we had an impact."]}, {"stageNumber": 9, "stageName": "Elmenteita 1", "country": "kenya", "userName": "<PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia R5", "stageTimeMs": 591300, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 9, "stageName": "Elmenteita 1", "country": "czech", "userName": "Prokop Martin - Jůrka <PERSON>ě<PERSON>", "realName": "", "group": ["RC2"], "car": "Ford Fiesta Rally2", "stageTimeMs": 605400, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 9, "stageName": "Elmenteita 1", "country": "germany", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 613300, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 9, "stageName": "Elmenteita 1", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia R5", "stageTimeMs": 642200, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 9, "stageName": "Elmenteita 1", "country": "italy", "userName": "Davi<PERSON> - Vindevogel S.", "realName": "", "group": ["RC2"], "car": "Ford Fiesta R5", "stageTimeMs": 657800, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 9, "stageName": "Elmenteita 1", "country": "paraguay", "userName": "Domínguez Diego Jr. - Peñate R.", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 674700, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 9, "stageName": "Elmenteita 1", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> Jeremiah - <PERSON><PERSON><PERSON>", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 685600, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 9, "stageName": "Elmenteita 1", "country": "spain", "userName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Sanjuan R.", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 695100, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 9, "stageName": "Elmenteita 1", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON>", "realName": "", "group": ["NAT", "NR4"], "car": "Mitsubishi Lancer Evo X R4", "stageTimeMs": 707200, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 9, "stageName": "Elmenteita 1", "country": "united kingdom", "userName": "<PERSON><PERSON><PERSON>", "realName": "", "group": ["NAT", "NR4"], "car": "Mitsubishi Lancer Evo X", "stageTimeMs": 711200, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 9, "stageName": "Elmenteita 1", "country": "greece", "userName": "<PERSON><PERSON><PERSON><PERSON> - Krawszik Tom", "realName": "", "group": ["RC2"], "car": "Ford Fiesta Rally2", "stageTimeMs": 726900, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 9, "stageName": "Elmenteita 1", "country": "kenya", "userName": "<PERSON> - <PERSON>", "realName": "", "group": ["RC2"], "car": "Ford Fiesta R5", "stageTimeMs": 791600, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 9, "stageName": "Elmenteita 1", "country": "poland", "userName": "Chwist <PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia RS Rally2", "stageTimeMs": 963600, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 9, "stageName": "Elmenteita 1", "country": "kenya", "userName": "<PERSON> - <PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 1136800, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": true, "finished": true, "comment": [], "nominal": true}, {"stageNumber": 9, "stageName": "Elmenteita 1", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 1274700, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": true, "finished": true, "comment": [], "nominal": true}, {"stageNumber": 9, "stageName": "Elmenteita 1", "country": "canada", "userName": "<PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 1274700, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": true, "finished": true, "comment": [], "nominal": true}, {"stageNumber": 9, "stageName": "Elmenteita 1", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["NAT", "NR4"], "car": "Mitsubishi Lancer Evo X", "stageTimeMs": 1307200, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": true, "finished": true, "comment": [], "nominal": true}, {"stageNumber": 10, "stageName": "Sleeping Warrior 1", "country": "spain", "userName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Sanjuan R.", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 0, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": false, "comment": []}, {"stageNumber": 10, "stageName": "Sleeping Warrior 1", "country": "finland", "userName": "Rovanperä Kalle - Halttunen J.", "realName": "", "group": ["RC1", "M"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 1077700, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["I think we could push a bit more but for sure <PERSON><PERSON> is pushing a lot for this win."]}, {"stageNumber": 10, "stageName": "Sleeping Warrior 1", "country": "finland", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC1", "M"], "car": "Hyundai i20 N Rally1 Hybrid", "stageTimeMs": 1081200, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["I have never experienced anything like this before. When you see the puddles and mud, you brake like absolutely everything and then you see if you survive."]}, {"stageNumber": 10, "stageName": "Sleeping Warrior 1", "country": "france", "userName": "Ogier Sébastien - Landais V.", "realName": "", "group": ["RC1", "M"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 1085400, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["Some sections were very slippy and I was very cautious to be honest. On the last section with the stones it's so easy to damage anything anywhere"]}, {"stageNumber": 10, "stageName": "Sleeping Warrior 1", "country": "united kingdom", "userName": "<PERSON> - <PERSON>", "realName": "", "group": ["RC1", "M"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 1099900, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["I don't think we were having a particularly good stage before that either..."]}, {"stageNumber": 10, "stageName": "Sleeping Warrior 1", "country": "japan", "userName": "<PERSON><PERSON><PERSON> - <PERSON>", "realName": "", "group": ["RC1"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 1100600, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["Sometimes you are not in control, the car was just going everywhere because of the mud and the water."]}, {"stageNumber": 10, "stageName": "Sleeping Warrior 1", "country": "spain", "userName": "<PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC1", "M"], "car": "Hyundai i20 N Rally1 Hybrid", "stageTimeMs": 1107200, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["It was really slippery in some places with a lot of muddy places. In the really fast corners the car was completely sideways with no control, but this I enjoyed a bit. The last part was really rough but it's okay."]}, {"stageNumber": 10, "stageName": "Sleeping Warrior 1", "country": "estonia", "userName": "Tänak Ott - Järveoja Martin", "realName": "", "group": ["RC1", "M"], "car": "Ford Puma Rally1 Hybrid", "stageTimeMs": 1121400, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["It was so muddy that I ran out of washer fluid so I had no visibility for the last 20 km."]}, {"stageNumber": 10, "stageName": "Sleeping Warrior 1", "country": "belgium", "userName": "Neuville Thierry - Wydaeghe M.", "realName": "", "group": ["RC1", "M"], "car": "Hyundai i20 N Rally1 Hybrid", "stageTimeMs": 1139900, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["I was enjoying, but I have no pressure and I don't need to push."]}, {"stageNumber": 10, "stageName": "Sleeping Warrior 1", "country": "poland", "userName": "Ka<PERSON>anowicz K. - Szczepaniak M.", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 1180700, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 10, "stageName": "Sleeping Warrior 1", "country": "france", "userName": "<PERSON><PERSON> - Gilsoul N.", "realName": "", "group": ["RC1", "M"], "car": "Ford Puma Rally1 Hybrid", "stageTimeMs": 1181300, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["On a junction, I go straight."]}, {"stageNumber": 10, "stageName": "Sleeping Warrior 1", "country": "luxembourg", "userName": "Munster Grégoire - <PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Ford Fiesta Rally2", "stageTimeMs": 1202800, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 10, "stageName": "Sleeping Warrior 1", "country": "greece", "userName": "<PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["RC1"], "car": "Ford Puma Rally1 Hybrid", "stageTimeMs": 1254400, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["It's not Sleeping Warrior, it's Sleeping Hell! Some parts are extremely slippery and we hit a tree."]}, {"stageNumber": 10, "stageName": "Sleeping Warrior 1", "country": "kenya", "userName": "<PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia R5", "stageTimeMs": 1291700, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 10, "stageName": "Sleeping Warrior 1", "country": "germany", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 1295300, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 10, "stageName": "Sleeping Warrior 1", "country": "sweden", "userName": "Solberg Oliver - <PERSON><PERSON> E.", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 1307600, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 10, "stageName": "Sleeping Warrior 1", "country": "czech", "userName": "Prokop Martin - Jůrka <PERSON>ě<PERSON>", "realName": "", "group": ["RC2"], "car": "Ford Fiesta Rally2", "stageTimeMs": 1311700, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 10, "stageName": "Sleeping Warrior 1", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia R5", "stageTimeMs": 1430500, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 80000, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 10, "stageName": "Sleeping Warrior 1", "country": "paraguay", "userName": "Domínguez Diego Jr. - Peñate R.", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 1452500, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 10, "stageName": "Sleeping Warrior 1", "country": "italy", "userName": "Davi<PERSON> - Vindevogel S.", "realName": "", "group": ["RC2"], "car": "Ford Fiesta R5", "stageTimeMs": 1456400, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 10, "stageName": "Sleeping Warrior 1", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON>", "realName": "", "group": ["NAT", "NR4"], "car": "Mitsubishi Lancer Evo X R4", "stageTimeMs": 1489900, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 10, "stageName": "Sleeping Warrior 1", "country": "greece", "userName": "<PERSON><PERSON><PERSON><PERSON> - Krawszik Tom", "realName": "", "group": ["RC2"], "car": "Ford Fiesta Rally2", "stageTimeMs": 1520200, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 10000, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 10, "stageName": "Sleeping Warrior 1", "country": "united kingdom", "userName": "<PERSON><PERSON><PERSON>", "realName": "", "group": ["NAT", "NR4"], "car": "Mitsubishi Lancer Evo X", "stageTimeMs": 1549100, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 10, "stageName": "Sleeping Warrior 1", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> Jeremiah - <PERSON><PERSON><PERSON>", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 1593000, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 10, "stageName": "Sleeping Warrior 1", "country": "kenya", "userName": "<PERSON> - <PERSON>", "realName": "", "group": ["RC2"], "car": "Ford Fiesta R5", "stageTimeMs": 1605600, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 10, "stageName": "Sleeping Warrior 1", "country": "poland", "userName": "Chwist <PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia RS Rally2", "stageTimeMs": 1780700, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": true, "finished": true, "comment": [], "nominal": true}, {"stageNumber": 10, "stageName": "Sleeping Warrior 1", "country": "kenya", "userName": "<PERSON> - <PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 1780700, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": true, "finished": true, "comment": [], "nominal": true}, {"stageNumber": 10, "stageName": "Sleeping Warrior 1", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 2052500, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": true, "finished": true, "comment": [], "nominal": true}, {"stageNumber": 10, "stageName": "Sleeping Warrior 1", "country": "canada", "userName": "<PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 2052500, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": true, "finished": true, "comment": [], "nominal": true}, {"stageNumber": 10, "stageName": "Sleeping Warrior 1", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["NAT", "NR4"], "car": "Mitsubishi Lancer Evo X", "stageTimeMs": 2089900, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": true, "finished": true, "comment": [], "nominal": true}, {"stageNumber": 11, "stageName": "Soysambu 2", "country": "france", "userName": "Ogier Sébastien - Landais V.", "realName": "", "group": ["RC1", "M"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 1043400, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["There is a lot of branches in there and this one got stuck in there, no big drama."]}, {"stageNumber": 11, "stageName": "Soysambu 2", "country": "finland", "userName": "Rovanperä Kalle - Halttunen J.", "realName": "", "group": ["RC1", "M"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 1049800, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["I am driving my own pace with quite a level speed in my opinion."]}, {"stageNumber": 11, "stageName": "Soysambu 2", "country": "japan", "userName": "<PERSON><PERSON><PERSON> - <PERSON>", "realName": "", "group": ["RC1"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 1055500, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["We have two rough stages to go so I don't need to be excited too much, just calm down and do my job."]}, {"stageNumber": 11, "stageName": "Soysambu 2", "country": "united kingdom", "userName": "<PERSON> - <PERSON>", "realName": "", "group": ["RC1", "M"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 1058200, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["Okay, it looks like a big fight. Let's see. It's Safari at the end of the day."]}, {"stageNumber": 11, "stageName": "Soysambu 2", "country": "spain", "userName": "<PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC1", "M"], "car": "Hyundai i20 N Rally1 Hybrid", "stageTimeMs": 1066000, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["I didn't take many risks but I enjoyed the drive at least."]}, {"stageNumber": 11, "stageName": "Soysambu 2", "country": "belgium", "userName": "Neuville Thierry - Wydaeghe M.", "realName": "", "group": ["RC1", "M"], "car": "Hyundai i20 N Rally1 Hybrid", "stageTimeMs": 1076700, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["The conditions have changed a lot. For sure it's better but with the car in general there's no real change. We can feel that we are missing traction in the bumpy sections. It's quite hard and we are getting kicked around quite a lot."]}, {"stageNumber": 11, "stageName": "Soysambu 2", "country": "france", "userName": "<PERSON><PERSON> - Gilsoul N.", "realName": "", "group": ["RC1", "M"], "car": "Ford Puma Rally1 Hybrid", "stageTimeMs": 1097500, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["This one was okay. Quite rough in some places but I prefer that to the morning with the wet."]}, {"stageNumber": 11, "stageName": "Soysambu 2", "country": "sweden", "userName": "Solberg Oliver - <PERSON><PERSON> E.", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 1109300, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 11, "stageName": "Soysambu 2", "country": "luxembourg", "userName": "Munster Grégoire - <PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Ford Fiesta Rally2", "stageTimeMs": 1136000, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 11, "stageName": "Soysambu 2", "country": "poland", "userName": "Ka<PERSON>anowicz K. - Szczepaniak M.", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 1146300, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 11, "stageName": "Soysambu 2", "country": "estonia", "userName": "Tänak Ott - Järveoja Martin", "realName": "", "group": ["RC1", "M"], "car": "Ford Puma Rally1 Hybrid", "stageTimeMs": 1188700, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["Okay, I didn't investigate too much on the stage but it looks like another tyre off somewhere. Quite a struggle."]}, {"stageNumber": 11, "stageName": "Soysambu 2", "country": "kenya", "userName": "<PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia R5", "stageTimeMs": 1215900, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 11, "stageName": "Soysambu 2", "country": "czech", "userName": "Prokop Martin - Jůrka <PERSON>ě<PERSON>", "realName": "", "group": ["RC2"], "car": "Ford Fiesta Rally2", "stageTimeMs": 1238800, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 11, "stageName": "Soysambu 2", "country": "kenya", "userName": "<PERSON> - <PERSON>", "realName": "", "group": ["RC2"], "car": "Ford Fiesta R5", "stageTimeMs": 1251700, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 11, "stageName": "Soysambu 2", "country": "germany", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 1252800, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 11, "stageName": "Soysambu 2", "country": "greece", "userName": "<PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["RC1"], "car": "Ford Puma Rally1 Hybrid", "stageTimeMs": 1300700, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["After the splash, no engine anymore. We stalled and then we got stuck but the engine has an issue."]}, {"stageNumber": 11, "stageName": "Soysambu 2", "country": "italy", "userName": "Davi<PERSON> - Vindevogel S.", "realName": "", "group": ["RC2"], "car": "Ford Fiesta R5", "stageTimeMs": 1329400, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 11, "stageName": "Soysambu 2", "country": "greece", "userName": "<PERSON><PERSON><PERSON><PERSON> - Krawszik Tom", "realName": "", "group": ["RC2"], "car": "Ford Fiesta Rally2", "stageTimeMs": 1390400, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 11, "stageName": "Soysambu 2", "country": "paraguay", "userName": "Domínguez Diego Jr. - Peñate R.", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 1432800, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 11, "stageName": "Soysambu 2", "country": "united kingdom", "userName": "<PERSON><PERSON><PERSON>", "realName": "", "group": ["NAT", "NR4"], "car": "Mitsubishi Lancer Evo X", "stageTimeMs": 1446200, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 11, "stageName": "Soysambu 2", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON>", "realName": "", "group": ["NAT", "NR4"], "car": "Mitsubishi Lancer Evo X R4", "stageTimeMs": 1511400, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 11, "stageName": "Soysambu 2", "country": "finland", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC1", "M"], "car": "Hyundai i20 N Rally1 Hybrid", "stageTimeMs": 1643400, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": true, "finished": true, "comment": [], "nominal": true}, {"stageNumber": 11, "stageName": "Soysambu 2", "country": "poland", "userName": "Chwist <PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia RS Rally2", "stageTimeMs": 1709300, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": true, "finished": true, "comment": [], "nominal": true}, {"stageNumber": 11, "stageName": "Soysambu 2", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia R5", "stageTimeMs": 1709300, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": [], "nominal": true}, {"stageNumber": 11, "stageName": "Soysambu 2", "country": "kenya", "userName": "<PERSON> - <PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 1709300, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": true, "finished": true, "comment": [], "nominal": true}, {"stageNumber": 11, "stageName": "Soysambu 2", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> Jeremiah - <PERSON><PERSON><PERSON>", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 2032800, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": true, "finished": true, "comment": [], "nominal": true}, {"stageNumber": 11, "stageName": "Soysambu 2", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 2032800, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": true, "finished": true, "comment": [], "nominal": true}, {"stageNumber": 11, "stageName": "Soysambu 2", "country": "canada", "userName": "<PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 2032800, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": true, "finished": true, "comment": [], "nominal": true}, {"stageNumber": 11, "stageName": "Soysambu 2", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["NAT", "NR4"], "car": "Mitsubishi Lancer Evo X", "stageTimeMs": 2046200, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": true, "finished": true, "comment": [], "nominal": true}, {"stageNumber": 12, "stageName": "Elmenteita 2", "country": "greece", "userName": "<PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["RC1"], "car": "Ford Puma Rally1 Hybrid", "stageTimeMs": 0, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": false, "comment": []}, {"stageNumber": 12, "stageName": "Elmenteita 2", "country": "italy", "userName": "Davi<PERSON> - Vindevogel S.", "realName": "", "group": ["RC2"], "car": "Ford Fiesta R5", "stageTimeMs": 0, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": false, "comment": []}, {"stageNumber": 12, "stageName": "Elmenteita 2", "country": "japan", "userName": "<PERSON><PERSON><PERSON> - <PERSON>", "realName": "", "group": ["RC1"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 500600, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 12, "stageName": "Elmenteita 2", "country": "france", "userName": "Ogier Sébastien - Landais V.", "realName": "", "group": ["RC1", "M"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 502900, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["I just try to do my job and not do any mistakes. Hopefully we stay out of trouble up until the end of the day now."]}, {"stageNumber": 12, "stageName": "Elmenteita 2", "country": "belgium", "userName": "Neuville Thierry - Wydaeghe M.", "realName": "", "group": ["RC1", "M"], "car": "Hyundai i20 N Rally1 Hybrid", "stageTimeMs": 503900, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["I don't know if it was a push but there was not hole left that I didn't go through! It was a bit shaky and I had to push back my helmet a few times, but it was fun."]}, {"stageNumber": 12, "stageName": "Elmenteita 2", "country": "united kingdom", "userName": "<PERSON> - <PERSON>", "realName": "", "group": ["RC1", "M"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 505500, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["It looks like <PERSON><PERSON> is doing a better job at the moment"]}, {"stageNumber": 12, "stageName": "Elmenteita 2", "country": "finland", "userName": "Rovanperä Kalle - Halttunen J.", "realName": "", "group": ["RC1", "M"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 506400, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["Definitely now since <PERSON> also dropped from the battle I don't want to take crazy risks anymore and catching <PERSON><PERSON> purely by driving would be pretty much impossible anyways."]}, {"stageNumber": 12, "stageName": "Elmenteita 2", "country": "spain", "userName": "<PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC1", "M"], "car": "Hyundai i20 N Rally1 Hybrid", "stageTimeMs": 510000, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["The only thing we can do is try to stay here at the end and make some points."]}, {"stageNumber": 12, "stageName": "Elmenteita 2", "country": "estonia", "userName": "Tänak Ott - Järveoja Martin", "realName": "", "group": ["RC1", "M"], "car": "Ford Puma Rally1 Hybrid", "stageTimeMs": 522400, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["One rear damper has leaked the oil out so we are just trying to get it through now."]}, {"stageNumber": 12, "stageName": "Elmenteita 2", "country": "france", "userName": "<PERSON><PERSON> - Gilsoul N.", "realName": "", "group": ["RC1", "M"], "car": "Ford Puma Rally1 Hybrid", "stageTimeMs": 522900, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["For sure it's important to finish but I am just trying to enjoy."]}, {"stageNumber": 12, "stageName": "Elmenteita 2", "country": "sweden", "userName": "Solberg Oliver - <PERSON><PERSON> E.", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 537900, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 12, "stageName": "Elmenteita 2", "country": "poland", "userName": "Ka<PERSON>anowicz K. - Szczepaniak M.", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 547100, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 12, "stageName": "Elmenteita 2", "country": "czech", "userName": "Prokop Martin - Jůrka <PERSON>ě<PERSON>", "realName": "", "group": ["RC2"], "car": "Ford Fiesta Rally2", "stageTimeMs": 593800, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 12, "stageName": "Elmenteita 2", "country": "kenya", "userName": "<PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia R5", "stageTimeMs": 601900, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 12, "stageName": "Elmenteita 2", "country": "germany", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 603100, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 12, "stageName": "Elmenteita 2", "country": "kenya", "userName": "<PERSON> - <PERSON>", "realName": "", "group": ["RC2"], "car": "Ford Fiesta R5", "stageTimeMs": 618600, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 12, "stageName": "Elmenteita 2", "country": "greece", "userName": "<PERSON><PERSON><PERSON><PERSON> - Krawszik Tom", "realName": "", "group": ["RC2"], "car": "Ford Fiesta Rally2", "stageTimeMs": 669500, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 12, "stageName": "Elmenteita 2", "country": "paraguay", "userName": "Domínguez Diego Jr. - Peñate R.", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 701500, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 12, "stageName": "Elmenteita 2", "country": "united kingdom", "userName": "<PERSON><PERSON><PERSON>", "realName": "", "group": ["NAT", "NR4"], "car": "Mitsubishi Lancer Evo X", "stageTimeMs": 704900, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 12, "stageName": "Elmenteita 2", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON>", "realName": "", "group": ["NAT", "NR4"], "car": "Mitsubishi Lancer Evo X R4", "stageTimeMs": 718500, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 12, "stageName": "Elmenteita 2", "country": "finland", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC1", "M"], "car": "Hyundai i20 N Rally1 Hybrid", "stageTimeMs": 1100600, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": true, "finished": true, "comment": [], "nominal": true}, {"stageNumber": 12, "stageName": "Elmenteita 2", "country": "luxembourg", "userName": "Munster Grégoire - <PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Ford Fiesta Rally2", "stageTimeMs": 1137900, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": true, "finished": true, "comment": [], "nominal": true}, {"stageNumber": 12, "stageName": "Elmenteita 2", "country": "poland", "userName": "Chwist <PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia RS Rally2", "stageTimeMs": 1137900, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": true, "finished": true, "comment": [], "nominal": true}, {"stageNumber": 12, "stageName": "Elmenteita 2", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia R5", "stageTimeMs": 1137900, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": true, "finished": true, "comment": [], "nominal": true}, {"stageNumber": 12, "stageName": "Elmenteita 2", "country": "kenya", "userName": "<PERSON> - <PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 1137900, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": true, "finished": true, "comment": [], "nominal": true}, {"stageNumber": 12, "stageName": "Elmenteita 2", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> Jeremiah - <PERSON><PERSON><PERSON>", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 1301500, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": true, "finished": true, "comment": [], "nominal": true}, {"stageNumber": 12, "stageName": "Elmenteita 2", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 1301500, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": true, "finished": true, "comment": [], "nominal": true}, {"stageNumber": 12, "stageName": "Elmenteita 2", "country": "canada", "userName": "<PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 1301500, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": true, "finished": true, "comment": [], "nominal": true}, {"stageNumber": 12, "stageName": "Elmenteita 2", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["NAT", "NR4"], "car": "Mitsubishi Lancer Evo X", "stageTimeMs": 1304900, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": true, "finished": true, "comment": [], "nominal": true}, {"stageNumber": 13, "stageName": "Sleeping Warrior 2", "country": "greece", "userName": "<PERSON><PERSON><PERSON><PERSON> - Krawszik Tom", "realName": "", "group": ["RC2"], "car": "Ford Fiesta Rally2", "stageTimeMs": 0, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": false, "comment": []}, {"stageNumber": 13, "stageName": "Sleeping Warrior 2", "country": "finland", "userName": "Rovanperä Kalle - Halttunen J.", "realName": "", "group": ["RC1", "M"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 1147700, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["Even on the straights, second gear was quite high-speed. We are here in one piece and that was the only goal for today."]}, {"stageNumber": 13, "stageName": "Sleeping Warrior 2", "country": "united kingdom", "userName": "<PERSON> - <PERSON>", "realName": "", "group": ["RC1", "M"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 1160800, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["Super slippy in there and of course when you start coming to the rainy parts it's patchy with the grip. You can only have a few moments before it's time to calm it down and bring it through."]}, {"stageNumber": 13, "stageName": "Sleeping Warrior 2", "country": "france", "userName": "Ogier Sébastien - Landais V.", "realName": "", "group": ["RC1", "M"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 1163000, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["I cannot say that I have so much luck with these tyres but that's the way it is."]}, {"stageNumber": 13, "stageName": "Sleeping Warrior 2", "country": "japan", "userName": "<PERSON><PERSON><PERSON> - <PERSON>", "realName": "", "group": ["RC1"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 1179600, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["Too many moments! Even with no rain I spun and had to reverse."]}, {"stageNumber": 13, "stageName": "Sleeping Warrior 2", "country": "sweden", "userName": "Solberg Oliver - <PERSON><PERSON> E.", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 1200900, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 13, "stageName": "Sleeping Warrior 2", "country": "spain", "userName": "<PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC1", "M"], "car": "Hyundai i20 N Rally1 Hybrid", "stageTimeMs": 1206000, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["Really difficult conditions, it's like ice - amazing. This is another level."]}, {"stageNumber": 13, "stageName": "Sleeping Warrior 2", "country": "belgium", "userName": "Neuville Thierry - Wydaeghe M.", "realName": "", "group": ["RC1", "M"], "car": "Hyundai i20 N Rally1 Hybrid", "stageTimeMs": 1211800, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["A hell of a stage - if you are not in the car you cannot describe the conditions."]}, {"stageNumber": 13, "stageName": "Sleeping Warrior 2", "country": "estonia", "userName": "Tänak Ott - Järveoja Martin", "realName": "", "group": ["RC1", "M"], "car": "Ford Puma Rally1 Hybrid", "stageTimeMs": 1256000, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["It's worse than a slick one ice!"]}, {"stageNumber": 13, "stageName": "Sleeping Warrior 2", "country": "france", "userName": "<PERSON><PERSON> - Gilsoul N.", "realName": "", "group": ["RC1", "M"], "car": "Ford Puma Rally1 Hybrid", "stageTimeMs": 1264100, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["I went slowly but even then I did a spin on the last corner!"]}, {"stageNumber": 13, "stageName": "Sleeping Warrior 2", "country": "poland", "userName": "Ka<PERSON>anowicz K. - Szczepaniak M.", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 1362800, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 13, "stageName": "Sleeping Warrior 2", "country": "germany", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 1365200, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 13, "stageName": "Sleeping Warrior 2", "country": "czech", "userName": "Prokop Martin - Jůrka <PERSON>ě<PERSON>", "realName": "", "group": ["RC2"], "car": "Ford Fiesta Rally2", "stageTimeMs": 1397400, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 13, "stageName": "Sleeping Warrior 2", "country": "kenya", "userName": "<PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia R5", "stageTimeMs": 1457200, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 13, "stageName": "Sleeping Warrior 2", "country": "kenya", "userName": "<PERSON> - <PERSON>", "realName": "", "group": ["RC2"], "car": "Ford Fiesta R5", "stageTimeMs": 1581800, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 13, "stageName": "Sleeping Warrior 2", "country": "paraguay", "userName": "Domínguez Diego Jr. - Peñate R.", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 1616800, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 13, "stageName": "Sleeping Warrior 2", "country": "united kingdom", "userName": "<PERSON><PERSON><PERSON>", "realName": "", "group": ["NAT", "NR4"], "car": "Mitsubishi Lancer Evo X", "stageTimeMs": 1649100, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 13, "stageName": "Sleeping Warrior 2", "country": "finland", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC1", "M"], "car": "Hyundai i20 N Rally1 Hybrid", "stageTimeMs": 1747700, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": true, "finished": true, "comment": [], "nominal": true}, {"stageNumber": 13, "stageName": "Sleeping Warrior 2", "country": "luxembourg", "userName": "Munster Grégoire - <PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Ford Fiesta Rally2", "stageTimeMs": 1800900, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": true, "finished": true, "comment": [], "nominal": true}, {"stageNumber": 13, "stageName": "Sleeping Warrior 2", "country": "poland", "userName": "Chwist <PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia RS Rally2", "stageTimeMs": 1800900, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": true, "finished": true, "comment": [], "nominal": true}, {"stageNumber": 13, "stageName": "Sleeping Warrior 2", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia R5", "stageTimeMs": 1800900, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": true, "finished": true, "comment": [], "nominal": true}, {"stageNumber": 13, "stageName": "Sleeping Warrior 2", "country": "kenya", "userName": "<PERSON> - <PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 1800900, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": true, "finished": true, "comment": [], "nominal": true}, {"stageNumber": 13, "stageName": "Sleeping Warrior 2", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> Jeremiah - <PERSON><PERSON><PERSON>", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 2216800, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": true, "finished": true, "comment": [], "nominal": true}, {"stageNumber": 13, "stageName": "Sleeping Warrior 2", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 2216800, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": true, "finished": true, "comment": [], "nominal": true}, {"stageNumber": 13, "stageName": "Sleeping Warrior 2", "country": "canada", "userName": "<PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 2216800, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": true, "finished": true, "comment": [], "nominal": true}, {"stageNumber": 13, "stageName": "Sleeping Warrior 2", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["NAT", "NR4"], "car": "Mitsubishi Lancer Evo X", "stageTimeMs": 2249100, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": true, "finished": true, "comment": [], "nominal": true}, {"stageNumber": 13, "stageName": "Sleeping Warrior 2", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON>", "realName": "", "group": ["NAT", "NR4"], "car": "Mitsubishi Lancer Evo X R4", "stageTimeMs": 2249100, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": [], "nominal": true}, {"stageNumber": 14, "stageName": "Malewa 1", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> Jeremiah - <PERSON><PERSON><PERSON>", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 0, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": false, "comment": []}, {"stageNumber": 14, "stageName": "Malewa 1", "country": "finland", "userName": "Rovanperä Kalle - Halttunen J.", "realName": "", "group": ["RC1", "M"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 365100, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["I don't think the others are really trying much. Let's see."]}, {"stageNumber": 14, "stageName": "Malewa 1", "country": "france", "userName": "Ogier Sébastien - Landais V.", "realName": "", "group": ["RC1", "M"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 373200, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["There are huge rocks everywhere on the road and I had to nearly stop because there was a massiver rock in the line."]}, {"stageNumber": 14, "stageName": "Malewa 1", "country": "japan", "userName": "<PERSON><PERSON><PERSON> - <PERSON>", "realName": "", "group": ["RC1"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 376500, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["It's very tricky, even I was quite careful. There is a lot of rocks everywhere. I was careful but it still felt on the limit, I don't know why. So tricky."]}, {"stageNumber": 14, "stageName": "Malewa 1", "country": "estonia", "userName": "Tänak Ott - Järveoja Martin", "realName": "", "group": ["RC1", "M"], "car": "Ford Puma Rally1 Hybrid", "stageTimeMs": 379100, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["It's quite a tricky one, it's really painful. A proper headache after this one, yeah."]}, {"stageNumber": 14, "stageName": "Malewa 1", "country": "united kingdom", "userName": "<PERSON> - <PERSON>", "realName": "", "group": ["RC1", "M"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 381800, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["We got caught in a dried rut and it completely spun us out of the blue."]}, {"stageNumber": 14, "stageName": "Malewa 1", "country": "belgium", "userName": "Neuville Thierry - Wydaeghe M.", "realName": "", "group": ["RC1", "M"], "car": "Hyundai i20 N Rally1 Hybrid", "stageTimeMs": 382800, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["<PERSON><PERSON><PERSON>'s propshaft is broken already?! I took it very slowly... We can't catch <PERSON><PERSON> so I just drove through. This is the roughest stage of the rally so we had to be careful in there."]}, {"stageNumber": 14, "stageName": "Malewa 1", "country": "spain", "userName": "<PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC1", "M"], "car": "Hyundai i20 N Rally1 Hybrid", "stageTimeMs": 384500, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["A lot of rocks and you need to just pass through the stage."]}, {"stageNumber": 14, "stageName": "Malewa 1", "country": "france", "userName": "<PERSON><PERSON> - Gilsoul N.", "realName": "", "group": ["RC1", "M"], "car": "Ford Puma Rally1 Hybrid", "stageTimeMs": 393500, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["We just took it easy, it's already really destroyed. We had some issue and it was very hard, so I was careful with the car."]}, {"stageNumber": 14, "stageName": "Malewa 1", "country": "sweden", "userName": "Solberg Oliver - <PERSON><PERSON> E.", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 400800, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 14, "stageName": "Malewa 1", "country": "luxembourg", "userName": "Munster Grégoire - <PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Ford Fiesta Rally2", "stageTimeMs": 406400, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 14, "stageName": "Malewa 1", "country": "poland", "userName": "Chwist <PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia RS Rally2", "stageTimeMs": 425000, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 14, "stageName": "Malewa 1", "country": "kenya", "userName": "<PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia R5", "stageTimeMs": 433300, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 14, "stageName": "Malewa 1", "country": "finland", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC1", "M"], "car": "Hyundai i20 N Rally1 Hybrid", "stageTimeMs": 435200, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["The propshaft is broken already so I don't really know if there is any sense. It happened three kilometres before the finish..."]}, {"stageNumber": 14, "stageName": "Malewa 1", "country": "czech", "userName": "Prokop Martin - Jůrka <PERSON>ě<PERSON>", "realName": "", "group": ["RC2"], "car": "Ford Fiesta Rally2", "stageTimeMs": 436300, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 14, "stageName": "Malewa 1", "country": "germany", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 442500, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 14, "stageName": "Malewa 1", "country": "poland", "userName": "Ka<PERSON>anowicz K. - Szczepaniak M.", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 451100, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 14, "stageName": "Malewa 1", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia R5", "stageTimeMs": 468100, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 14, "stageName": "Malewa 1", "country": "canada", "userName": "<PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 480800, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 14, "stageName": "Malewa 1", "country": "kenya", "userName": "<PERSON> - <PERSON>", "realName": "", "group": ["RC2"], "car": "Ford Fiesta R5", "stageTimeMs": 498800, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 14, "stageName": "Malewa 1", "country": "kenya", "userName": "<PERSON> - <PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 505600, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 14, "stageName": "Malewa 1", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["NAT", "NR4"], "car": "Mitsubishi Lancer Evo X", "stageTimeMs": 506400, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 14, "stageName": "Malewa 1", "country": "paraguay", "userName": "Domínguez Diego Jr. - Peñate R.", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 525800, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 14, "stageName": "Malewa 1", "country": "united kingdom", "userName": "<PERSON><PERSON><PERSON>", "realName": "", "group": ["NAT", "NR4"], "car": "Mitsubishi Lancer Evo X", "stageTimeMs": 548600, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 14, "stageName": "Malewa 1", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 562000, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 14, "stageName": "Malewa 1", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON>", "realName": "", "group": ["NAT", "NR4"], "car": "Mitsubishi Lancer Evo X R4", "stageTimeMs": 602500, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 10000, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 15, "stageName": "Oserian 1", "country": "france", "userName": "Ogier Sébastien - Landais V.", "realName": "", "group": ["RC1", "M"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 675900, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["I just clipped a little bit a tree and I am very surprised to be honest."]}, {"stageNumber": 15, "stageName": "Oserian 1", "country": "finland", "userName": "Rovanperä Kalle - Halttunen J.", "realName": "", "group": ["RC1", "M"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 684500, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 15, "stageName": "Oserian 1", "country": "estonia", "userName": "Tänak Ott - Järveoja Martin", "realName": "", "group": ["RC1", "M"], "car": "Ford Puma Rally1 Hybrid", "stageTimeMs": 692400, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 15, "stageName": "Oserian 1", "country": "united kingdom", "userName": "<PERSON> - <PERSON>", "realName": "", "group": ["RC1", "M"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 693800, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 15, "stageName": "Oserian 1", "country": "belgium", "userName": "Neuville Thierry - Wydaeghe M.", "realName": "", "group": ["RC1", "M"], "car": "Hyundai i20 N Rally1 Hybrid", "stageTimeMs": 699400, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 15, "stageName": "Oserian 1", "country": "japan", "userName": "<PERSON><PERSON><PERSON> - <PERSON>", "realName": "", "group": ["RC1"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 703600, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 15, "stageName": "Oserian 1", "country": "spain", "userName": "<PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC1", "M"], "car": "Hyundai i20 N Rally1 Hybrid", "stageTimeMs": 712000, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 15, "stageName": "Oserian 1", "country": "france", "userName": "<PERSON><PERSON> - Gilsoul N.", "realName": "", "group": ["RC1", "M"], "car": "Ford Puma Rally1 Hybrid", "stageTimeMs": 723200, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 15, "stageName": "Oserian 1", "country": "sweden", "userName": "Solberg Oliver - <PERSON><PERSON> E.", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 735800, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 15, "stageName": "Oserian 1", "country": "luxembourg", "userName": "Munster Grégoire - <PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Ford Fiesta Rally2", "stageTimeMs": 765600, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 15, "stageName": "Oserian 1", "country": "germany", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 802500, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 15, "stageName": "Oserian 1", "country": "kenya", "userName": "<PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia R5", "stageTimeMs": 804900, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 15, "stageName": "Oserian 1", "country": "poland", "userName": "Ka<PERSON>anowicz K. - Szczepaniak M.", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 805200, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 15, "stageName": "Oserian 1", "country": "poland", "userName": "Chwist <PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia RS Rally2", "stageTimeMs": 807600, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 15, "stageName": "Oserian 1", "country": "czech", "userName": "Prokop Martin - Jůrka <PERSON>ě<PERSON>", "realName": "", "group": ["RC2"], "car": "Ford Fiesta Rally2", "stageTimeMs": 817000, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 15, "stageName": "Oserian 1", "country": "kenya", "userName": "<PERSON> - <PERSON>", "realName": "", "group": ["RC2"], "car": "Ford Fiesta R5", "stageTimeMs": 843300, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 15, "stageName": "Oserian 1", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia R5", "stageTimeMs": 855900, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 15, "stageName": "Oserian 1", "country": "paraguay", "userName": "Domínguez Diego Jr. - Peñate R.", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 911600, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 15, "stageName": "Oserian 1", "country": "kenya", "userName": "<PERSON> - <PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 953300, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 15, "stageName": "Oserian 1", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["NAT", "NR4"], "car": "Mitsubishi Lancer Evo X", "stageTimeMs": 953300, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 15, "stageName": "Oserian 1", "country": "canada", "userName": "<PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 959500, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 15, "stageName": "Oserian 1", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 969200, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 15, "stageName": "Oserian 1", "country": "united kingdom", "userName": "<PERSON><PERSON><PERSON>", "realName": "", "group": ["NAT", "NR4"], "car": "Mitsubishi Lancer Evo X", "stageTimeMs": 1027200, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 15, "stageName": "Oserian 1", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON>", "realName": "", "group": ["NAT", "NR4"], "car": "Mitsubishi Lancer Evo X R4", "stageTimeMs": 1054800, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 15, "stageName": "Oserian 1", "country": "finland", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC1", "M"], "car": "Hyundai i20 N Rally1 Hybrid", "stageTimeMs": 1064200, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 16, "stageName": "Hell's Gate 1", "country": "estonia", "userName": "Tänak Ott - Järveoja Martin", "realName": "", "group": ["RC1", "M"], "car": "Ford Puma Rally1 Hybrid", "stageTimeMs": 329700, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["I am not so sure about Power Stage points, but let's see. It's tough, that's for sure, but we will try to come through."]}, {"stageNumber": 16, "stageName": "Hell's Gate 1", "country": "belgium", "userName": "Neuville Thierry - Wydaeghe M.", "realName": "", "group": ["RC1", "M"], "car": "Hyundai i20 N Rally1 Hybrid", "stageTimeMs": 330300, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["It's the brake cooling duct, it's been like that since the beginning."]}, {"stageNumber": 16, "stageName": "Hell's Gate 1", "country": "finland", "userName": "Rovanperä Kalle - Halttunen J.", "realName": "", "group": ["RC1", "M"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 330600, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["It was just one corner where I got caught out a bit in the ruts. I am not feeling good in the car, I am fighting way too much all the time. Especially here it doesn't work out, I am oversteering way too much."]}, {"stageNumber": 16, "stageName": "Hell's Gate 1", "country": "united kingdom", "userName": "<PERSON> - <PERSON>", "realName": "", "group": ["RC1", "M"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 333900, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["I'm not really committed in the fast places, but okay, we got through okay."]}, {"stageNumber": 16, "stageName": "Hell's Gate 1", "country": "france", "userName": "Ogier Sébastien - Landais V.", "realName": "", "group": ["RC1", "M"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 334200, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["Without the wing it wasn't easy at high speed"]}, {"stageNumber": 16, "stageName": "Hell's Gate 1", "country": "france", "userName": "<PERSON><PERSON> - Gilsoul N.", "realName": "", "group": ["RC1", "M"], "car": "Ford Puma Rally1 Hybrid", "stageTimeMs": 339500, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["We just took it easy and did a good recce for the powerstage."]}, {"stageNumber": 16, "stageName": "Hell's Gate 1", "country": "spain", "userName": "<PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC1", "M"], "car": "Hyundai i20 N Rally1 Hybrid", "stageTimeMs": 345500, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 16, "stageName": "Hell's Gate 1", "country": "japan", "userName": "<PERSON><PERSON><PERSON> - <PERSON>", "realName": "", "group": ["RC1"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 348400, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["All okay, just no hybrid."]}, {"stageNumber": 16, "stageName": "Hell's Gate 1", "country": "sweden", "userName": "Solberg Oliver - <PERSON><PERSON> E.", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 357600, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 16, "stageName": "Hell's Gate 1", "country": "poland", "userName": "Ka<PERSON>anowicz K. - Szczepaniak M.", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 362900, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 16, "stageName": "Hell's Gate 1", "country": "luxembourg", "userName": "Munster Grégoire - <PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Ford Fiesta Rally2", "stageTimeMs": 367700, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 16, "stageName": "Hell's Gate 1", "country": "czech", "userName": "Prokop Martin - Jůrka <PERSON>ě<PERSON>", "realName": "", "group": ["RC2"], "car": "Ford Fiesta Rally2", "stageTimeMs": 388700, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 16, "stageName": "Hell's Gate 1", "country": "germany", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 392900, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 10000, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 16, "stageName": "Hell's Gate 1", "country": "poland", "userName": "Chwist <PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia RS Rally2", "stageTimeMs": 402800, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 16, "stageName": "Hell's Gate 1", "country": "kenya", "userName": "<PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia R5", "stageTimeMs": 405400, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 16, "stageName": "Hell's Gate 1", "country": "kenya", "userName": "<PERSON> - <PERSON>", "realName": "", "group": ["RC2"], "car": "Ford Fiesta R5", "stageTimeMs": 409700, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 16, "stageName": "Hell's Gate 1", "country": "paraguay", "userName": "Domínguez Diego Jr. - Peñate R.", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 439500, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 16, "stageName": "Hell's Gate 1", "country": "finland", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC1", "M"], "car": "Hyundai i20 N Rally1 Hybrid", "stageTimeMs": 440800, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["I'm not sure what the problem is at the moment but it's only front-wheel drive. Maybe it's something with the propshaft, it might be twisted, but the rear diff isn't working at all.\""]}, {"stageNumber": 16, "stageName": "Hell's Gate 1", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia R5", "stageTimeMs": 446200, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 140000, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 16, "stageName": "Hell's Gate 1", "country": "kenya", "userName": "<PERSON> - <PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 447100, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 16, "stageName": "Hell's Gate 1", "country": "canada", "userName": "<PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 449100, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 16, "stageName": "Hell's Gate 1", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["NAT", "NR4"], "car": "Mitsubishi Lancer Evo X", "stageTimeMs": 456000, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 16, "stageName": "Hell's Gate 1", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 460100, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 16, "stageName": "Hell's Gate 1", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON>", "realName": "", "group": ["NAT", "NR4"], "car": "Mitsubishi Lancer Evo X R4", "stageTimeMs": 510500, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 300000, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 16, "stageName": "Hell's Gate 1", "country": "united kingdom", "userName": "<PERSON><PERSON><PERSON>", "realName": "", "group": ["NAT", "NR4"], "car": "Mitsubishi Lancer Evo X", "stageTimeMs": 517800, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 17, "stageName": "Malewa 2", "country": "kenya", "userName": "<PERSON> - <PERSON>", "realName": "", "group": ["RC2"], "car": "Ford Fiesta R5", "stageTimeMs": 0, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": false, "comment": []}, {"stageNumber": 17, "stageName": "Malewa 2", "country": "luxembourg", "userName": "Munster Grégoire - <PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Ford Fiesta Rally2", "stageTimeMs": 0, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": false, "comment": []}, {"stageNumber": 17, "stageName": "Malewa 2", "country": "finland", "userName": "Rovanperä Kalle - Halttunen J.", "realName": "", "group": ["RC1", "M"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 362600, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 17, "stageName": "Malewa 2", "country": "france", "userName": "Ogier Sébastien - Landais V.", "realName": "", "group": ["RC1", "M"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 363200, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 17, "stageName": "Malewa 2", "country": "japan", "userName": "<PERSON><PERSON><PERSON> - <PERSON>", "realName": "", "group": ["RC1"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 370600, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 17, "stageName": "Malewa 2", "country": "united kingdom", "userName": "<PERSON> - <PERSON>", "realName": "", "group": ["RC1", "M"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 373300, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 17, "stageName": "Malewa 2", "country": "spain", "userName": "<PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC1", "M"], "car": "Hyundai i20 N Rally1 Hybrid", "stageTimeMs": 374000, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 17, "stageName": "Malewa 2", "country": "estonia", "userName": "Tänak Ott - Järveoja Martin", "realName": "", "group": ["RC1", "M"], "car": "Ford Puma Rally1 Hybrid", "stageTimeMs": 376600, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 17, "stageName": "Malewa 2", "country": "france", "userName": "<PERSON><PERSON> - Gilsoul N.", "realName": "", "group": ["RC1", "M"], "car": "Ford Puma Rally1 Hybrid", "stageTimeMs": 377500, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 17, "stageName": "Malewa 2", "country": "belgium", "userName": "Neuville Thierry - Wydaeghe M.", "realName": "", "group": ["RC1", "M"], "car": "Hyundai i20 N Rally1 Hybrid", "stageTimeMs": 381800, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 17, "stageName": "Malewa 2", "country": "sweden", "userName": "Solberg Oliver - <PERSON><PERSON> E.", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 390800, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 17, "stageName": "Malewa 2", "country": "poland", "userName": "Chwist <PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia RS Rally2", "stageTimeMs": 399100, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": [], "nominal": true}, {"stageNumber": 17, "stageName": "Malewa 2", "country": "poland", "userName": "Ka<PERSON>anowicz K. - Szczepaniak M.", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 414800, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 17, "stageName": "Malewa 2", "country": "czech", "userName": "Prokop Martin - Jůrka <PERSON>ě<PERSON>", "realName": "", "group": ["RC2"], "car": "Ford Fiesta Rally2", "stageTimeMs": 421900, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 17, "stageName": "Malewa 2", "country": "germany", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 424800, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 60000, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 17, "stageName": "Malewa 2", "country": "finland", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC1", "M"], "car": "Hyundai i20 N Rally1 Hybrid", "stageTimeMs": 434700, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 10000, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 17, "stageName": "Malewa 2", "country": "kenya", "userName": "<PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia R5", "stageTimeMs": 436400, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 17, "stageName": "Malewa 2", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia R5", "stageTimeMs": 452300, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 17, "stageName": "Malewa 2", "country": "canada", "userName": "<PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 478300, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 17, "stageName": "Malewa 2", "country": "kenya", "userName": "<PERSON> - <PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 482500, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 17, "stageName": "Malewa 2", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["NAT", "NR4"], "car": "Mitsubishi Lancer Evo X", "stageTimeMs": 485100, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 17, "stageName": "Malewa 2", "country": "paraguay", "userName": "Domínguez Diego Jr. - Peñate R.", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 526500, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 17, "stageName": "Malewa 2", "country": "united kingdom", "userName": "<PERSON><PERSON><PERSON>", "realName": "", "group": ["NAT", "NR4"], "car": "Mitsubishi Lancer Evo X", "stageTimeMs": 543500, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 17, "stageName": "Malewa 2", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 550000, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 17, "stageName": "Malewa 2", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON>", "realName": "", "group": ["NAT", "NR4"], "car": "Mitsubishi Lancer Evo X R4", "stageTimeMs": 588000, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 18, "stageName": "Oserian 2", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["NAT", "NR4"], "car": "Mitsubishi Lancer Evo X", "stageTimeMs": 0, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": false, "comment": []}, {"stageNumber": 18, "stageName": "Oserian 2", "country": "japan", "userName": "<PERSON><PERSON><PERSON> - <PERSON>", "realName": "", "group": ["RC1"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 713600, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 18, "stageName": "Oserian 2", "country": "united kingdom", "userName": "<PERSON> - <PERSON>", "realName": "", "group": ["RC1", "M"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 724600, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["Everything's fine, just hot."]}, {"stageNumber": 18, "stageName": "Oserian 2", "country": "finland", "userName": "Rovanperä Kalle - Halttunen J.", "realName": "", "group": ["RC1", "M"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 724700, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["I didn't know we have a puncture... It was a proper Safari in there, really deep ruts. I just tried to go through."]}, {"stageNumber": 18, "stageName": "Oserian 2", "country": "spain", "userName": "<PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC1", "M"], "car": "Hyundai i20 N Rally1 Hybrid", "stageTimeMs": 725500, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["For sure, we want to fight in the Power Stage but I don't know if we have any chance. It will be difficult but we will try to do our best to bring the car home with no problems."]}, {"stageNumber": 18, "stageName": "Oserian 2", "country": "belgium", "userName": "Neuville Thierry - Wydaeghe M.", "realName": "", "group": ["RC1", "M"], "car": "Hyundai i20 N Rally1 Hybrid", "stageTimeMs": 725800, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["This one was horrible. It is so rutted and in some places it felt like we had no suspension in there."]}, {"stageNumber": 18, "stageName": "Oserian 2", "country": "france", "userName": "Ogier Sébastien - Landais V.", "realName": "", "group": ["RC1", "M"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 728500, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 18, "stageName": "Oserian 2", "country": "estonia", "userName": "Tänak Ott - Järveoja Martin", "realName": "", "group": ["RC1", "M"], "car": "Ford Puma Rally1 Hybrid", "stageTimeMs": 736000, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["We need to try for sure, let's see. Myself, I am struggling quite a bit - probably getting a bit old!"]}, {"stageNumber": 18, "stageName": "Oserian 2", "country": "sweden", "userName": "Solberg Oliver - <PERSON><PERSON> E.", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 739200, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 18, "stageName": "Oserian 2", "country": "france", "userName": "<PERSON><PERSON> - Gilsoul N.", "realName": "", "group": ["RC1", "M"], "car": "Ford Puma Rally1 Hybrid", "stageTimeMs": 761600, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["We had an alarm in the stage so I had to finish the stage in road mode."]}, {"stageNumber": 18, "stageName": "Oserian 2", "country": "finland", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC1", "M"], "car": "Hyundai i20 N Rally1 Hybrid", "stageTimeMs": 763600, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["It should be possible, but the thing is that it's not so simple to go really fast when you are doing the stage for the first time fast. Let's see if the car can take it when I push full throttle."]}, {"stageNumber": 18, "stageName": "Oserian 2", "country": "poland", "userName": "Ka<PERSON>anowicz K. - Szczepaniak M.", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 805000, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 18, "stageName": "Oserian 2", "country": "poland", "userName": "Chwist <PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia RS Rally2", "stageTimeMs": 815600, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 18, "stageName": "Oserian 2", "country": "germany", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 842800, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 18, "stageName": "Oserian 2", "country": "czech", "userName": "Prokop Martin - Jůrka <PERSON>ě<PERSON>", "realName": "", "group": ["RC2"], "car": "Ford Fiesta Rally2", "stageTimeMs": 850100, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 18, "stageName": "Oserian 2", "country": "kenya", "userName": "<PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia R5", "stageTimeMs": 853000, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 18, "stageName": "Oserian 2", "country": "paraguay", "userName": "Domínguez Diego Jr. - Peñate R.", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 992900, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 18, "stageName": "Oserian 2", "country": "kenya", "userName": "<PERSON> - <PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 1007700, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 18, "stageName": "Oserian 2", "country": "canada", "userName": "<PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 1032100, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 18, "stageName": "Oserian 2", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 1144700, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 18, "stageName": "Oserian 2", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON>", "realName": "", "group": ["NAT", "NR4"], "car": "Mitsubishi Lancer Evo X R4", "stageTimeMs": 1282100, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 50000, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 18, "stageName": "Oserian 2", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia R5", "stageTimeMs": 3484200, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 220000, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 18, "stageName": "Oserian 2", "country": "united kingdom", "userName": "<PERSON><PERSON><PERSON>", "realName": "", "group": ["NAT", "NR4"], "car": "Mitsubishi Lancer Evo X", "stageTimeMs": 3808700, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 250000, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 19, "stageName": "Hell's Gate 2", "country": "belgium", "userName": "Neuville Thierry - Wydaeghe M.", "realName": "", "group": ["RC1", "M"], "car": "Hyundai i20 N Rally1 Hybrid", "stageTimeMs": 0, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": false, "comment": []}, {"stageNumber": 19, "stageName": "Hell's Gate 2", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia R5", "stageTimeMs": 0, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": false, "comment": []}, {"stageNumber": 19, "stageName": "Hell's Gate 2", "country": "estonia", "userName": "Tänak Ott - Järveoja Martin", "realName": "", "group": ["RC1", "M"], "car": "Ford Puma Rally1 Hybrid", "stageTimeMs": 335000, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["So far it has been a bit tricky this year. We need to find a bit of speed for the next rally."]}, {"stageNumber": 19, "stageName": "Hell's Gate 2", "country": "finland", "userName": "Rovanperä Kalle - Halttunen J.", "realName": "", "group": ["RC1", "M"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 335800, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["You always want to fight for the win but we did our best starting first car on the road, so regarding that I think it's not fully bad. Good points for the season anyway."]}, {"stageNumber": 19, "stageName": "Hell's Gate 2", "country": "france", "userName": "Ogier Sébastien - Landais V.", "realName": "", "group": ["RC1", "M"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 338300, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 19, "stageName": "Hell's Gate 2", "country": "finland", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC1", "M"], "car": "Hyundai i20 N Rally1 Hybrid", "stageTimeMs": 338900, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["I really tried to give it everything I could. It wasn't easy when you don't do the first pass of a stage at a fast speed but it was a good run through."]}, {"stageNumber": 19, "stageName": "Hell's Gate 2", "country": "united kingdom", "userName": "<PERSON> - <PERSON>", "realName": "", "group": ["RC1", "M"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 341100, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["We're here, we've taken some points but I would have liked to be better."]}, {"stageNumber": 19, "stageName": "Hell's Gate 2", "country": "japan", "userName": "<PERSON><PERSON><PERSON> - <PERSON>", "realName": "", "group": ["RC1"], "car": "Toyota GR Yaris Rally1 Hybrid", "stageTimeMs": 344400, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["It's okay, we finished and first of all I want to say thanks to the team. They fixed the car every single time and they did a great job."]}, {"stageNumber": 19, "stageName": "Hell's Gate 2", "country": "spain", "userName": "<PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC1", "M"], "car": "Hyundai i20 N Rally1 Hybrid", "stageTimeMs": 344900, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["We are here but we didn't have the speed that we wanted."]}, {"stageNumber": 19, "stageName": "Hell's Gate 2", "country": "france", "userName": "<PERSON><PERSON> - Gilsoul N.", "realName": "", "group": ["RC1", "M"], "car": "Ford Puma Rally1 Hybrid", "stageTimeMs": 357100, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["We were struggling a lot on this rally but we are at the end with the experience. Thanks to the team, we keep on pushing."]}, {"stageNumber": 19, "stageName": "Hell's Gate 2", "country": "sweden", "userName": "Solberg Oliver - <PERSON><PERSON> E.", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 366500, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 120000, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 19, "stageName": "Hell's Gate 2", "country": "poland", "userName": "Ka<PERSON>anowicz K. - Szczepaniak M.", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 374600, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["It's an amazing feeling. It was so nice to be here again - it seemed to be easy but it wasn't. Thank you to my team and <PERSON><PERSON><PERSON>, I think we did quite a good job."]}, {"stageNumber": 19, "stageName": "Hell's Gate 2", "country": "czech", "userName": "Prokop Martin - Jůrka <PERSON>ě<PERSON>", "realName": "", "group": ["RC2"], "car": "Ford Fiesta Rally2", "stageTimeMs": 386000, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["It took too long, but this year we made everything to finish! It was important for us to do this because me and my co-driver are lifelong friends and our dream was to finish this rally together."]}, {"stageNumber": 19, "stageName": "Hell's Gate 2", "country": "germany", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 388000, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["It's amazing for us and thanks to my co-driver <PERSON><PERSON> and the team, it worked perfect. For us it's a big moment to win the Masters Cup and we are really happy."]}, {"stageNumber": 19, "stageName": "Hell's Gate 2", "country": "poland", "userName": "Chwist <PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia RS Rally2", "stageTimeMs": 393800, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["It's a shame we had a technical problem yesterday and are running in super rally. It was an amazing experience and it's a shame that it's over already."]}, {"stageNumber": 19, "stageName": "Hell's Gate 2", "country": "kenya", "userName": "<PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia R5", "stageTimeMs": 401700, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": ["It's been a while since I got in an R5 car, 21 rallies ticked!"]}, {"stageNumber": 19, "stageName": "Hell's Gate 2", "country": "kenya", "userName": "<PERSON> - <PERSON>", "realName": "", "group": ["RC2"], "car": "Škoda Fabia Rally2 evo", "stageTimeMs": 463700, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 19, "stageName": "Hell's Gate 2", "country": "paraguay", "userName": "Domínguez Diego Jr. - Peñate R.", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 466100, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 19, "stageName": "Hell's Gate 2", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON>", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 484800, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 19, "stageName": "Hell's Gate 2", "country": "canada", "userName": "<PERSON> - <PERSON><PERSON><PERSON>", "realName": "", "group": ["RC3"], "car": "Ford Fiesta Rally3", "stageTimeMs": 516700, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 19, "stageName": "Hell's Gate 2", "country": "kenya", "userName": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON>", "realName": "", "group": ["NAT", "NR4"], "car": "Mitsubishi Lancer Evo X R4", "stageTimeMs": 543100, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}, {"stageNumber": 19, "stageName": "Hell's Gate 2", "country": "united kingdom", "userName": "<PERSON><PERSON><PERSON>", "realName": "", "group": ["NAT", "NR4"], "car": "Mitsubishi Lancer Evo X", "stageTimeMs": 563600, "penaltyInsideStageMs": 0, "penaltyOutsideStageMs": 0, "superRally": false, "finished": true, "comment": []}]