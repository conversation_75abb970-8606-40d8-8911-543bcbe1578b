{"{\"organization\":\"com.github.ghik\",\"name\":\"zerowaste\",\"revision\":\"1.0.0\",\"configurations\":\"plugin->default(compile)\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Full\",\"prefix\":\"\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "(sbt.Classpaths.jvmBaseSettings) Defaults.scala", "startLine": 3481}, "type": "LinePosition"}, "{\"organization\":\"org.scala-lang\",\"name\":\"scala3-library\",\"revision\":\"3.6.4\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "(sbt.Classpaths.jvmBaseSettings) Defaults.scala", "startLine": 3481}, "type": "LinePosition"}}