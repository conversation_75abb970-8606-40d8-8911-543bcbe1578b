[0m[[0m[0mdebug[0m] [0m[0mCreated transactional ClassFileManager with tempDir = C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\target\scala-3.6.4\classes.bak[0m
[0m[[0m[0mdebug[0m] [0m[0mAbout to delete class files:[0m
[0m[[0m[0mdebug[0m] [0m[0mWe backup class files:[0m
[0m[[0m[0mdebug[0m] [0m[0mCreated transactional ClassFileManager with tempDir = C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\target\scala-3.6.4\classes.bak[0m
[0m[[0m[0mdebug[0m] [0m[0mAbout to delete class files:[0m
[0m[[0m[0mdebug[0m] [0m[0mWe backup class files:[0m
[0m[[0m[0mdebug[0m] [0m[0mRegistering generated classes:[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$$anon$7.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$$anon$3.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$$anon$12.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Ewrc$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	ShardedEntry.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Result$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	ResultValidator.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	SmokeRun$package$given_ResultValidator_Unit$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Logic$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$$anon$18.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$$anon$11.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$$anon$1.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$RaceAdminEntry$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Sharded$package$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	TimeResult.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Logic$RallyNotStored$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Entry$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$package$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Rsf$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Logic.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	RallyEye$package.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Telemetry$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Main$SmokeRun.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$$anon$5.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Util$package.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$$anon$17.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	PositionResult.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$$anon$21.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	SmokeRun$package$given_ResultValidator_List$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$Time$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Db$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Logic$LogicError.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$Racer$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Main$MigrateDb$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Sharded$package$given_Shardable_RallyKind_String$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Repo.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$RaceAdminEntry.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Logic$Admin$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$Rally$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Results$package.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Main$HttpServer.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Migrations$package$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$Time.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Db$Config$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Telemetry.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$$anon$16.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$$anon$10.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Model$package$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	PositionResult$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$RaceAdminResponse$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Util$package$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	RallyEye$package$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Model$package.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Db$package$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$$anon$19.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Main$SmokeRun$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	BuildInfo.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Logic$RallyInProgress$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Db.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Ewrc.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Rsf$$anon$1.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Ewrc$Retired$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Result.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Main$LoadPressAuto.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Rally.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$StageEntry.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$Racer$$anon$23.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Migrations$package.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Repo$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$Rally.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	SmokeRun$package$given_ResultValidator_RallyData$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	RallyInfo.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Logic$RefreshNotSupported$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Main$MigrateDb.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Db$package.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Telemetry$package$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	ShardedEntry$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$$anon$9.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$$anon$13.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Db$Config.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Telemetry$package.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	SmokeRun$package.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	GraalVMResourceProvider.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$$anon$2.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Rsf.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Main$LoadPressAuto$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$$anon$20.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	RallyInfo$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$$anon$15.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Rally$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Shardable.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Main$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Results$package$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	BuildInfo$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	TimeResult$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$$anon$6.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$$anon$8.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$$anon$4.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$$anon$22.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$StageEntry$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	SmokeRun$package$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$$anon$14.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$Racer.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Ewrc$Retired.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$package.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Sharded$package$$anon$1.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Main.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Main$HttpServer$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$RaceAdminResponse.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	GraalVMResourceProvider$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Entry.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	SmokeRun$package$refreshResultValidator$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Sharded$package.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$$anon$7.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$$anon$3.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$$anon$12.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Ewrc$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	ShardedEntry.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Result$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	ResultValidator.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	SmokeRun$package$given_ResultValidator_Unit$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Logic$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$$anon$18.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$$anon$11.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$$anon$1.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$RaceAdminEntry$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Sharded$package$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	TimeResult.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Logic$RallyNotStored$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Entry$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$package$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Rsf$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Logic.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	RallyEye$package.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Telemetry$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Main$SmokeRun.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$$anon$5.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Util$package.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$$anon$17.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	PositionResult.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$$anon$21.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	SmokeRun$package$given_ResultValidator_List$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$Time$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Db$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Logic$LogicError.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$Racer$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Main$MigrateDb$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Sharded$package$given_Shardable_RallyKind_String$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Repo.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$RaceAdminEntry.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Logic$Admin$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$Rally$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Results$package.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Main$HttpServer.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Migrations$package$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$Time.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Db$Config$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Telemetry.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$$anon$16.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$$anon$10.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Model$package$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	PositionResult$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$RaceAdminResponse$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Util$package$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	RallyEye$package$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Model$package.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Db$package$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$$anon$19.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Main$SmokeRun$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	BuildInfo.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Logic$RallyInProgress$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Db.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Ewrc.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Rsf$$anon$1.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Ewrc$Retired$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Result.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Main$LoadPressAuto.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Rally.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$StageEntry.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$Racer$$anon$23.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Migrations$package.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Repo$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$Rally.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	SmokeRun$package$given_ResultValidator_RallyData$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	RallyInfo.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Logic$RefreshNotSupported$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Main$MigrateDb.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Db$package.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Telemetry$package$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	ShardedEntry$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$$anon$9.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$$anon$13.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Db$Config.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Telemetry$package.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	SmokeRun$package.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	GraalVMResourceProvider.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$$anon$2.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Rsf.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Main$LoadPressAuto$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$$anon$20.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	RallyInfo$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$$anon$15.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Rally$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Shardable.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Main$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Results$package$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	BuildInfo$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	TimeResult$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$$anon$6.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$$anon$8.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$$anon$4.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$$anon$22.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$StageEntry$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	SmokeRun$package$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$$anon$14.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$Racer.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Ewrc$Retired.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$package.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Sharded$package$$anon$1.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Main.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Main$HttpServer$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	PressAuto$RaceAdminResponse.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	GraalVMResourceProvider$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Entry.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	SmokeRun$package$refreshResultValidator$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Sharded$package.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0mRemoving the temporary directory used for backing up class files: C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\target\scala-3.6.4\classes.bak[0m
