organization := "org.scalablytyped"
name := "tailwindcss__postcss"
version := "4.1.7-a8584b"
scalaVersion := "3.6.4"
enablePlugins(ScalaJSPlugin)
libraryDependencies ++= Seq(
  "com.olvind" %%% "scalablytyped-runtime" % "2.4.2",
  "org.scala-js" %%% "scalajs-dom" % "2.3.0",
  "org.scalablytyped" %%% "postcss" % "8.5.3-513014",
  "org.scalablytyped" %%% "source-map-js" % "1.2.1-26a955",
  "org.scalablytyped" %%% "std" % "5.8-c2d6aa")
publishArtifact in packageDoc := false
scalacOptions ++= List("-encoding", "utf-8", "-feature", "-language:implicitConversions", "-language:higherKinds", "-language:existentials", "-no-indent", "-source:future")
licenses += ("MIT", url("http://opensource.org/licenses/MIT"))
