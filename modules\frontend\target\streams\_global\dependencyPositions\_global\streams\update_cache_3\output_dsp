{"{\"organization\":\"org.scala-js\",\"name\":\"scala-js-macrotask-executor\",\"revision\":\"1.1.1\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"sjs1_\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\build.sbt", "startLine": 52}, "type": "LinePosition"}, "{\"organization\":\"com.lihaoyi\",\"name\":\"utest\",\"revision\":\"0.8.5\",\"configurations\":\"test\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"sjs1_\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\build.sbt", "startLine": 52}, "type": "LinePosition"}, "{\"organization\":\"com.raquo\",\"name\":\"waypoint\",\"revision\":\"9.0.0\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"sjs1_\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\build.sbt", "startLine": 52}, "type": "LinePosition"}, "{\"organization\":\"org.scala-lang\",\"name\":\"scala3-library_sjs1\",\"revision\":\"3.6.4\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\build.sbt", "startLine": 52}, "type": "LinePosition"}, "{\"organization\":\"com.softwaremill.sttp.tapir\",\"name\":\"tapir-sttp-client\",\"revision\":\"1.11.29\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"sjs1_\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\build.sbt", "startLine": 52}, "type": "LinePosition"}, "{\"organization\":\"io.bullet\",\"name\":\"borer-core\",\"revision\":\"1.16.1\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"sjs1_\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\build.sbt", "startLine": 52}, "type": "LinePosition"}, "{\"organization\":\"io.bullet\",\"name\":\"borer-derivation\",\"revision\":\"1.16.1\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"sjs1_\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\build.sbt", "startLine": 52}, "type": "LinePosition"}, "{\"organization\":\"com.github.ghik\",\"name\":\"zerowaste\",\"revision\":\"1.0.0\",\"configurations\":\"plugin->default(compile)\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Full\",\"prefix\":\"\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\build.sbt", "startLine": 52}, "type": "LinePosition"}, "{\"organization\":\"com.raquo\",\"name\":\"laminar\",\"revision\":\"17.2.1\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"sjs1_\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\build.sbt", "startLine": 52}, "type": "LinePosition"}, "{\"organization\":\"org.scala-js\",\"name\":\"scalajs-dom\",\"revision\":\"2.8.0\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"sjs1_\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\build.sbt", "startLine": 52}, "type": "LinePosition"}, "{\"organization\":\"org.scala-js\",\"name\":\"scalajs-library_2.13\",\"revision\":\"1.19.0\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Disabled\"}}": {"value": {"$fields": ["path", "startLine"], "path": "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\build.sbt", "startLine": 52}, "type": "LinePosition"}, "{\"organization\":\"io.github.cquiroz\",\"name\":\"scala-java-time\",\"revision\":\"2.6.0\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"sjs1_\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\build.sbt", "startLine": 52}, "type": "LinePosition"}, "{\"organization\":\"org.scala-js\",\"name\":\"scalajs-test-bridge_2.13\",\"revision\":\"1.19.0\",\"configurations\":\"test\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Disabled\"}}": {"value": {"$fields": ["path", "startLine"], "path": "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\build.sbt", "startLine": 52}, "type": "LinePosition"}, "{\"organization\":\"org.scala-lang\",\"name\":\"scala3-library\",\"revision\":\"3.6.4\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "(sbt.Classpaths.jvmBaseSettings) Defaults.scala", "startLine": 3481}, "type": "LinePosition"}}