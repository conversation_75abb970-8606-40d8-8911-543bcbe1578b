[0m[[0m[0mdebug[0m] [0m[0m[zinc] IncrementalCompile -----------[0m
[0m[[0m[0mdebug[0m] [0m[0mIncrementalCompile.incrementalCompile[0m
[0m[[0m[0mdebug[0m] [0m[0mprevious = Stamps for: 0 products, 0 sources, 0 libraries[0m
[0m[[0m[0mdebug[0m] [0m[0mcurrent source = Set(${BASE}/modules/backend/src/main/scala/PressAuto.scala, ${BASE}/modules/backend/src/main/scala/RallyEye.scala, ${BASE}/modules/backend/src/main/scala/storage/Migrations.scala, ${BASE}/modules/backend/src/main/scala/Telemetry.scala, ${BASE}/modules/backend/src/main/scala/storage/GraalVMResourceProvider.scala, ${BASE}/modules/backend/src/main/scala/Sharded.scala, ${BASE}/modules/backend/src/main/scala/storage/Db.scala, ${BASE}/modules/backend/src/main/scala/SmokeRun.scala, ${BASE}/modules/backend/src/main/scala/Rsf.scala, ${BASE}/modules/backend/src/main/scala/Main.scala, ${BASE}/modules/backend/src/main/scala/Util.scala, ${BASE}/modules/backend/target/scala-3.6.4/src_managed/main/sbt-buildinfo/BuildInfo.scala, ${BASE}/modules/backend/src/main/scala/Ewrc.scala, ${BASE}/modules/backend/src/main/scala/storage/Model.scala, ${BASE}/modules/backend/src/main/scala/Results.scala, ${BASE}/modules/backend/src/main/scala/storage/Repo.scala, ${BASE}/modules/backend/src/main/scala/loader/PressAuto.scala)[0m
[0m[[0m[0mdebug[0m] [0m[0m> initialChanges = InitialChanges(Changes(added = Set(${BASE}/modules/backend/src/main/scala/Telemetry.scala, ${BASE}/modules/backend/src/main/scala/storage/Db.scala, ${BASE}/modules/backend/src/main/scala/storage/Model.scala, ${BASE}/modules/backend/src/main/scala/Results.scala, ${BASE}/modules/backend/src/main/scala/Main.scala, ${BASE}/modules/backend/src/main/scala/SmokeRun.scala, ${BASE}/modules/backend/src/main/scala/Ewrc.scala, ${BASE}/modules/backend/target/scala-3.6.4/src_managed/main/sbt-buildinfo/BuildInfo.scala, ${BASE}/modules/backend/src/main/scala/RallyEye.scala, ${BASE}/modules/backend/src/main/scala/Rsf.scala, ${BASE}/modules/backend/src/main/scala/PressAuto.scala, ${BASE}/modules/backend/src/main/scala/Util.scala, ${BASE}/modules/backend/src/main/scala/storage/Repo.scala, ${BASE}/modules/backend/src/main/scala/storage/Migrations.scala, ${BASE}/modules/backend/src/main/scala/loader/PressAuto.scala, ${BASE}/modules/backend/src/main/scala/storage/GraalVMResourceProvider.scala, ${BASE}/modules/backend/src/main/scala/Sharded.scala), removed = Set(), changed = Set(), unmodified = ...),Set(),Set(),API Changes: Set())[0m
[0m[[0m[0mdebug[0m] [0m[0mFull compilation, no sources in previous analysis.[0m
[0m[[0m[0mdebug[0m] [0m[0mall 17 sources are invalidated[0m
[0m[[0m[0mdebug[0m] [0m[0mInitial set of included nodes: [0m
[0m[[0m[0mdebug[0m] [0m[0mRecompiling all sources: number of invalidated sources > 50.0 percent of all sources[0m
[0m[[0m[0mdebug[0m] [0m[0mcompilation cycle 1[0m
[0m[[0m[0minfo[0m] [0m[0mcompiling 17 Scala sources to C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\target\scala-3.6.4\classes ...[0m
[0m[[0m[0mdebug[0m] [0m[0mReturning already retrieved and compiled bridge: C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\scala-lang\scala3-sbt-bridge\3.6.4\scala3-sbt-bridge-3.6.4.jar.[0m
[0m[[0m[0mdebug[0m] [0m[0m[zinc] Running cached compiler 1adbc106 for Scala Compiler version 3.6.4[0m
[0m[[0m[0mdebug[0m] [0m[0m[zinc] The Scala compiler is invoked with:[0m
[0m[[0m[0mdebug[0m] [0m[0m	-encoding[0m
[0m[[0m[0mdebug[0m] [0m[0m	utf8[0m
[0m[[0m[0mdebug[0m] [0m[0m	-deprecation[0m
[0m[[0m[0mdebug[0m] [0m[0m	-feature[0m
[0m[[0m[0mdebug[0m] [0m[0m	-unchecked[0m
[0m[[0m[0mdebug[0m] [0m[0m	-language:experimental.macros[0m
[0m[[0m[0mdebug[0m] [0m[0m	-language:higherKinds[0m
[0m[[0m[0mdebug[0m] [0m[0m	-language:implicitConversions[0m
[0m[[0m[0mdebug[0m] [0m[0m	-Xkind-projector[0m
[0m[[0m[0mdebug[0m] [0m[0m	-Wvalue-discard[0m
[0m[[0m[0mdebug[0m] [0m[0m	-Wnonunit-statement[0m
[0m[[0m[0mdebug[0m] [0m[0m	-Wunused:implicits[0m
[0m[[0m[0mdebug[0m] [0m[0m	-Wunused:explicits[0m
[0m[[0m[0mdebug[0m] [0m[0m	-Wunused:imports[0m
[0m[[0m[0mdebug[0m] [0m[0m	-Wunused:locals[0m
[0m[[0m[0mdebug[0m] [0m[0m	-Wunused:params[0m
[0m[[0m[0mdebug[0m] [0m[0m	-Wunused:privates[0m
[0m[[0m[0mdebug[0m] [0m[0m	-Xfatal-warnings[0m
[0m[[0m[0mdebug[0m] [0m[0m	-source[0m
[0m[[0m[0mdebug[0m] [0m[0m	3.7[0m
[0m[[0m[0mdebug[0m] [0m[0m	-Xplugin:C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\github\ghik\zerowaste_3.6.4\1.0.0\zerowaste_3.6.4-1.0.0.jar[0m
[0m[[0m[0mdebug[0m] [0m[0m	-classpath[0m
[0m[[0m[0mdebug[0m] [0m[0m	C:\Users\<USER>\Documents\Projects\scalaproject\modules\backend\target\scala-3.6.4\classes;C:\Users\<USER>\Documents\Projects\scalaproject\modules\shared\.jvm\target\scala-3.6.4\classes;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\scala-lang\scala3-library_3\3.6.4\scala3-library_3-3.6.4.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\scalameta\svm-subs\101.0.0\svm-subs-101.0.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\softwaremill\sttp\tapir\tapir-http4s-server_3\1.11.29\tapir-http4s-server_3-1.11.29.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\softwaremill\sttp\tapir\tapir-http4s-client_3\1.11.29\tapir-http4s-client_3-1.11.29.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\http4s\http4s-ember-server_3\0.23.30\http4s-ember-server_3-0.23.30.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\http4s\http4s-ember-client_3\0.23.30\http4s-ember-client_3-0.23.30.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\http4s\http4s-otel4s-middleware-trace-client_3\0.10.0\http4s-otel4s-middleware-trace-client_3-0.10.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\http4s\http4s-otel4s-middleware-trace-server_3\0.10.0\http4s-otel4s-middleware-trace-server_3-0.10.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\io\bullet\borer-compat-circe_3\1.16.1\borer-compat-circe_3-1.16.1.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\ch\qos\logback\logback-classic\1.5.18\logback-classic-1.5.18.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\github\geirolz\fly4s-core_3\1.0.0\fly4s-core_3-1.0.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\xerial\sqlite-jdbc\3.49.1.0\sqlite-jdbc-3.49.1.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\tpolecat\doobie-core_3\1.0.0-RC9\doobie-core_3-1.0.0-RC9.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\io\github\arainko\ducktape_3\0.2.8\ducktape_3-0.2.8.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\monovore\decline-effect_3\2.5.0\decline-effect_3-2.5.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\io\github\iltotore\iron_3\3.0.1\iron_3-3.0.1.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\io\github\iltotore\iron-doobie_3\3.0.1\iron-doobie_3-3.0.1.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\themillhousegroup\scoup_3\1.0.0\scoup_3-1.0.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\gnieh\fs2-data-csv_3\1.12.0\fs2-data-csv_3-1.12.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\typelevel\log4cats-core_3\2.7.0\log4cats-core_3-2.7.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\typelevel\log4cats-slf4j_3\2.7.0\log4cats-slf4j_3-2.7.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\io\github\arturaz\otel4s-doobie_3\0.5.0\otel4s-doobie_3-0.5.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\typelevel\otel4s-oteljava_3\0.12.0\otel4s-oteljava_3-0.12.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\typelevel\otel4s-instrumentation-metrics_3\0.12.0\otel4s-instrumentation-metrics_3-0.12.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\io\opentelemetry\opentelemetry-exporter-otlp\1.50.0\opentelemetry-exporter-otlp-1.50.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\io\opentelemetry\opentelemetry-sdk-extension-autoconfigure\1.50.0\opentelemetry-sdk-extension-autoconfigure-1.50.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\softwaremill\sttp\tapir\tapir-core_3\1.11.29\tapir-core_3-1.11.29.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\io\bullet\borer-derivation_3\1.16.1\borer-derivation_3-1.16.1.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\io\github\iltotore\iron-borer_3\3.0.1\iron-borer_3-3.0.1.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\scala-lang\scala-library\2.13.15\scala-library-2.13.15.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\softwaremill\sttp\tapir\tapir-server_3\1.11.29\tapir-server_3-1.11.29.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\softwaremill\sttp\tapir\tapir-cats_3\1.11.29\tapir-cats_3-1.11.29.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\softwaremill\sttp\tapir\tapir-cats-effect_3\1.11.29\tapir-cats-effect_3-1.11.29.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\http4s\http4s-server_3\0.23.30\http4s-server_3-0.23.30.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\softwaremill\sttp\shared\fs2_3\1.5.0\fs2_3-1.5.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\softwaremill\sttp\tapir\tapir-client_3\1.11.29\tapir-client_3-1.11.29.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\http4s\http4s-core_3\0.23.30\http4s-core_3-0.23.30.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\http4s\http4s-ember-core_3\0.23.30\http4s-ember-core_3-0.23.30.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\http4s\http4s-client_3\0.23.30\http4s-client_3-0.23.30.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\typelevel\keypool_3\0.4.10\keypool_3-0.4.10.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\http4s\http4s-otel4s-middleware-trace-core_3\0.10.0\http4s-otel4s-middleware-trace-core_3-0.10.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\typelevel\cats-effect_3\3.6.1\cats-effect_3-3.6.1.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\typelevel\otel4s-core-common_3\0.12.0\otel4s-core-common_3-0.12.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\typelevel\otel4s-core-trace_3\0.12.0\otel4s-core-trace_3-0.12.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\typelevel\otel4s-semconv_3\0.11.0\otel4s-semconv_3-0.11.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\io\bullet\borer-core_3\1.16.1\borer-core_3-1.16.1.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\io\circe\circe-core_3\0.14.13\circe-core_3-0.14.13.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\ch\qos\logback\logback-core\1.5.18\logback-core-1.5.18.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\slf4j\slf4j-api\2.0.17\slf4j-api-2.0.17.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\typelevel\cats-core_3\2.13.0\cats-core_3-2.13.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\flywaydb\flyway-core\10.4.1\flyway-core-10.4.1.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\tpolecat\doobie-free_3\1.0.0-RC9\doobie-free_3-1.0.0-RC9.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\tpolecat\typename_3\1.1.0\typename_3-1.1.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\monovore\decline_3\2.5.0\decline_3-2.5.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\jsoup\jsoup\1.8.3\jsoup-1.8.3.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\gnieh\fs2-data-text_3\1.12.0\fs2-data-text_3-1.12.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\co\fs2\fs2-core_3\3.12.0\fs2-core_3-3.12.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\scala-lang\modules\scala-collection-compat_3\2.11.0\scala-collection-compat_3-2.11.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\portable-scala\portable-scala-reflect_2.13\1.1.3\portable-scala-reflect_2.13-1.1.3.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\typelevel\cats-effect-std_3\3.6.1\cats-effect-std_3-3.6.1.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\io\opentelemetry\instrumentation\opentelemetry-jdbc\2.15.0-alpha\opentelemetry-jdbc-2.15.0-alpha.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\typelevel\otel4s-oteljava-context-storage_3\0.12.0\otel4s-oteljava-context-storage_3-0.12.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\typelevel\otel4s-core_3\0.12.0\otel4s-core_3-0.12.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\typelevel\otel4s-oteljava-metrics_3\0.12.0\otel4s-oteljava-metrics_3-0.12.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\typelevel\otel4s-oteljava-trace_3\0.12.0\otel4s-oteljava-trace_3-0.12.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\io\opentelemetry\opentelemetry-sdk\1.50.0\opentelemetry-sdk-1.50.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\typelevel\otel4s-core-metrics_3\0.12.0\otel4s-core-metrics_3-0.12.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\io\opentelemetry\opentelemetry-sdk-trace\1.50.0\opentelemetry-sdk-trace-1.50.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\io\opentelemetry\opentelemetry-sdk-metrics\1.50.0\opentelemetry-sdk-metrics-1.50.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\io\opentelemetry\opentelemetry-sdk-logs\1.50.0\opentelemetry-sdk-logs-1.50.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\io\opentelemetry\opentelemetry-exporter-otlp-common\1.50.0\opentelemetry-exporter-otlp-common-1.50.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\io\opentelemetry\opentelemetry-exporter-sender-okhttp\1.50.0\opentelemetry-exporter-sender-okhttp-1.50.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\io\opentelemetry\opentelemetry-sdk-extension-autoconfigure-spi\1.50.0\opentelemetry-sdk-extension-autoconfigure-spi-1.50.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\softwaremill\sttp\model\core_3\1.7.14\core_3-1.7.14.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\softwaremill\sttp\shared\core_3\1.5.0\core_3-1.5.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\softwaremill\sttp\shared\ws_3\1.5.0\ws_3-1.5.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\softwaremill\magnolia1_3\magnolia_3\1.3.16\magnolia_3-1.3.16.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\co\fs2\fs2-io_3\3.11.0\fs2-io_3-3.11.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\typelevel\case-insensitive_3\1.4.2\case-insensitive_3-1.4.2.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\typelevel\cats-parse_3\1.0.0\cats-parse_3-1.0.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\http4s\http4s-crypto_3\0.2.4\http4s-crypto_3-0.2.4.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\comcast\ip4s-core_3\3.6.0\ip4s-core_3-3.6.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\typelevel\literally_3\1.1.0\literally_3-1.1.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\scodec\scodec-bits_3\1.1.38\scodec-bits_3-1.1.38.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\typelevel\vault_3\3.6.0\vault_3-3.6.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\log4s\log4s_3\1.10.0\log4s_3-1.10.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\twitter\hpack\1.0.2\hpack-1.0.2.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\http4s\http4s-otel4s-middleware-core_3\0.10.0\http4s-otel4s-middleware-core_3-0.10.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\typelevel\cats-effect-kernel_3\3.6.1\cats-effect-kernel_3-3.6.1.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\typelevel\cats-mtl_3\1.4.0\cats-mtl_3-1.4.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\io\circe\circe-numbers_3\0.14.13\circe-numbers_3-0.14.13.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\typelevel\cats-kernel_3\2.13.0\cats-kernel_3-2.13.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\fasterxml\jackson\dataformat\jackson-dataformat-toml\2.15.2\jackson-dataformat-toml-2.15.2.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\google\code\gson\gson\2.10.1\gson-2.10.1.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\typelevel\cats-free_3\2.13.0\cats-free_3-2.13.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\typelevel\cats-collections-core_3\0.9.8\cats-collections-core_3-0.9.8.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\io\opentelemetry\instrumentation\opentelemetry-instrumentation-api\2.15.0\opentelemetry-instrumentation-api-2.15.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\io\opentelemetry\instrumentation\opentelemetry-instrumentation-api-incubator\2.15.0-alpha\opentelemetry-instrumentation-api-incubator-2.15.0-alpha.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\io\opentelemetry\opentelemetry-api\1.50.0\opentelemetry-api-1.50.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\typelevel\otel4s-oteljava-common_3\0.12.0\otel4s-oteljava-common_3-0.12.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\io\opentelemetry\opentelemetry-sdk-common\1.50.0\opentelemetry-sdk-common-1.50.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\io\opentelemetry\opentelemetry-exporter-common\1.50.0\opentelemetry-exporter-common-1.50.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\squareup\okhttp3\okhttp\4.12.0\okhttp-4.12.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\fasterxml\jackson\core\jackson-databind\2.15.2\jackson-databind-2.15.2.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\fasterxml\jackson\core\jackson-core\2.15.2\jackson-core-2.15.2.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\typelevel\algebra_3\2.10.0\algebra_3-2.10.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\io\opentelemetry\opentelemetry-api-incubator\1.49.0-alpha\opentelemetry-api-incubator-1.49.0-alpha.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\io\opentelemetry\semconv\opentelemetry-semconv\1.32.0\opentelemetry-semconv-1.32.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\io\opentelemetry\opentelemetry-context\1.50.0\opentelemetry-context-1.50.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\squareup\okio\okio\3.6.0\okio-3.6.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\jetbrains\kotlin\kotlin-stdlib-jdk8\1.9.10\kotlin-stdlib-jdk8-1.9.10.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\fasterxml\jackson\core\jackson-annotations\2.15.2\jackson-annotations-2.15.2.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\squareup\okio\okio-jvm\3.6.0\okio-jvm-3.6.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\jetbrains\kotlin\kotlin-stdlib\1.9.10\kotlin-stdlib-1.9.10.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\jetbrains\kotlin\kotlin-stdlib-jdk7\1.9.10\kotlin-stdlib-jdk7-1.9.10.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\jetbrains\kotlin\kotlin-stdlib-common\1.9.10\kotlin-stdlib-common-1.9.10.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\jetbrains\annotations\13.0\annotations-13.0.jar[0m
[0m[[0m[0mdebug[0m] [0m[0mScala compilation took 37.5747207 s[0m
[0m[[0m[0mdebug[0m] [0m[0mdone compiling[0m
