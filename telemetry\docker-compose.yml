version: '3.7'
services:
  otel-collector:  # receives application metrics and traces via gRPC or HTTP protocol
    image: otel/opentelemetry-collector-contrib
    command: [--config=/etc/otel-collector-config.yml]
    volumes:
    - "./otel-collector-config.yml:/etc/otel-collector-config.yml"
    ports:
    - "8888:8888"  # Prometheus metrics exposed by the collector
    - "8889:8889"  # Prometheus exporter metrics
    - "4317:4317"  # OTLP gRPC receiver
    - "4318:4318"  # OTLP http receiver
    networks:
    - static-network

  jaeger:  # stores traces received from the OpenTelemetry Collector
    image: jaegertracing/all-in-one:latest
    volumes:
    - "./jaeger-ui.json:/etc/jaeger/jaeger-ui.json"
    command: --query.ui-config /etc/jaeger/jaeger-ui.json
    environment:
    - METRICS_STORAGE_TYPE=prometheus
    - PROMETHEUS_SERVER_URL=http://prometheus:9090
    ports:
    - "14250:14250"
    - "16685:16685"  # GRPC
    - "16686:16686"  # UI
    networks:
    - static-network

  prometheus:  # scrapes metrics from the OpenTelemetry Collector
    image: prom/prometheus:latest
    volumes:
    - "./prometheus.yml:/etc/prometheus/prometheus.yml"
    ports:
    - "9090:9090"
    networks:
    - static-network

  grafana:  # queries Jaeger and Prometheus to visualize traces and metrics
    image: grafana/grafana-oss
    restart: unless-stopped
    volumes:
    - ./grafana.ini:/etc/grafana/grafana.ini
    - ./datasource.yml:/etc/grafana/provisioning/datasources/datasource.yaml
    environment:
    - GF_AUTH_ANONYMOUS_ENABLED=true
    - GF_AUTH_ANONYMOUS_ORG_ROLE=Admin
    - GF_AUTH_DISABLE_LOGIN_FORM=true
    ports:
    - "3000:3000"
    networks:
    - static-network

networks:
  static-network:
