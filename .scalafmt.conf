version = "3.9.3"
align = more
maxColumn = 120
runner.dialect = scala3

align.tokens."+" = [
  {code = "%",  owner = "Term.ApplyInfix"},
  {code = "%%", owner = "Term.ApplyInfix"},
  {code = "%%%", owner = "Term.ApplyInfix"},
]

rewrite.rules = [RedundantBraces, RedundantParens, Imports]
rewrite.imports.sort = scalastyle
rewrite.imports.groups = [["java\\..*"],["scala\\..*"],["typings\\..*"]]
rewrite.scala3.convertToNewSyntax = yes
rewrite.scala3.removeOptionalBraces = yes

fileOverride {
  "glob:**.sbt" {
    runner.dialect = scala212source3
  }

  "glob:**/project/**.*" {
    runner.dialect = scala212source3
  }
}
