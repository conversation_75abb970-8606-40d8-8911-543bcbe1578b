#!/usr/bin/env python3

import requests
from bs4 import BeautifulSoup

def debug_tables():
    """Debug table structure for missing entries #3 and #4"""
    
    # Fetch the first stage page
    url = "https://www.ewrc-results.com/results/94276/?s=493915"
    print(f"Fetching: {url}")
    
    response = requests.get(url)
    soup = BeautifulSoup(response.content, 'html.parser')
    
    # Check what tables are found by different selectors
    tables_results = soup.select("main#main-section div#stage-results table.results")
    tables_main = soup.select("main#main-section table.results")
    tables_all_results = soup.select("table.results")
    tables_all = soup.select("main#main-section table")
    
    print(f"\nTable counts:")
    print(f"  main#main-section div#stage-results table.results: {len(tables_results)}")
    print(f"  main#main-section table.results: {len(tables_main)}")
    print(f"  table.results: {len(tables_all_results)}")
    print(f"  main#main-section table: {len(tables_all)}")
    
    # Check which tables contain #3 and #4 using the broadest selector
    print(f"\nChecking which tables contain #3 and #4...")
    for i, table in enumerate(tables_all):
        table_html = str(table)
        has_3 = '<span class="font-weight-bold text-primary fs-091">#3</span>' in table_html
        has_4 = '<span class="font-weight-bold text-primary fs-091">#4</span>' in table_html
        if has_3 or has_4:
            print(f"  Table {i+1}: Contains #3={has_3}, #4={has_4}")
            # Show table classes and structure
            classes = table.get('class', [])
            print(f"    Classes: {classes}")
            rows = table.select("tr")
            print(f"    Rows: {len(rows)}")
            
            # Show first few entry numbers in this table
            entry_numbers = []
            for row in rows[:10]:  # Check first 10 rows
                cells = row.select("td")
                if len(cells) >= 4:
                    number_elem = row.select_one("td span.font-weight-bold.text-primary")
                    if number_elem:
                        entry_num = number_elem.get_text().strip()
                        entry_numbers.append(entry_num)
            print(f"    First entry numbers: {entry_numbers}")

if __name__ == "__main__":
    debug_tables()
