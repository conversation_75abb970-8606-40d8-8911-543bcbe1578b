package typings.tailwindcssPostcss

import org.scalablytyped.runtime.StObject
import scala.scalajs.js
import scala.scalajs.js.annotation.{JSGlobalScope, JSGlobal, JSImport, JSName, JSBracketAccess}

object anon {
  
  trait Minify extends StObject {
    
    var minify: js.UndefOr[Boolean] = js.undefined
  }
  object Minify {
    
    inline def apply(): Minify = {
      val __obj = js.Dynamic.literal()
      __obj.asInstanceOf[Minify]
    }
    
    @scala.inline
    implicit open class MutableBuilder[Self <: Minify] (val x: Self) extends AnyVal {
      
      inline def setMinify(value: Boolean): Self = StObject.set(x, "minify", value.asInstanceOf[js.Any])
      
      inline def setMinifyUndefined: Self = StObject.set(x, "minify", js.undefined)
    }
  }
}
