on:
  schedule:
  - cron: '0 0 * * 0'
  workflow_dispatch:

name: Launch Scala Steward

env:
  SCALA_STEWARD: true

jobs:
  scala-steward:
    runs-on: ubuntu-22.04
    name: Launch Scala Steward
    steps:
    - name: Launch Scala Steward
      uses: scala-steward-org/scala-steward-action@v2
      with:
        github-app-id: ${{ secrets.APP_ID }}
        github-app-installation-id: ${{ secrets.APP_INSTALLATION_ID }}
        github-app-key: ${{ secrets.APP_PRIVATE_KEY }}
        other-args: '--add-labels'
