name: ci

on:
  push:
    branches:
    - main
  pull_request:

env:
  JAVA_OPTS: -Xms2048M -Xmx2048M -Xss6M -XX:ReservedCodeCacheSize=256M -Dfile.encoding=UTF-8

jobs:
  checks:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
    steps:
    - uses: actions/checkout@v4
    - uses: extractions/setup-just@v3

    - name: yaml-lint
      uses: ibiqlik/action-yamllint@v3

    - uses: actions/setup-node@v4
      with:
        node-version: 18
        cache: npm
        cache-dependency-path: modules/frontend

    - run: just install

    - uses: actions/setup-java@v4
      with:
        distribution: temurin
        java-version: 17
        cache: sbt
    - uses: sbt/setup-sbt@v1

    - run: sbt --client scalafmtCheckAll
    - run: sbt --client scalafmtSbtCheck
    - run: sbt --client headerCheckAll
    - run: sbt --client test
    - run: sbt --client frontend/publicDev
    - run: sbt --client frontend/publicProd

    - run: just build-js
