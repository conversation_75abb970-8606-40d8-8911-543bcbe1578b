# RallyEye [![ci-badge][]][ci]

Online rally results visualizer.
Fetches rally results from [RallySimFans][rallysimfans] or [ewrc-results.com][ewrc-results] and produces an interactive graph with all the drivers and stages of the rally.

As seen on [GPLaps][gplaps-channel]:

[![Watch the video][gplaps-thumb]][gplaps-video]

[ci-badge]: https://github.com/2m/rallyeye/actions/workflows/ci.yml/badge.svg
[ci]:       https://github.com/2m/rallyeye/actions/workflows/ci.yml

[rallysimfans]:   https://www.rallysimfans.hu
[ewrc-results]:   https://www.ewrc-results.com/
[rallyeye-data]:  https://github.com/2m/rallyeye-data
[gplaps-channel]: https://www.youtube.com/@GPLaps
[gplaps-thumb]:   https://img.youtube.com/vi/4NN2PSqCco8/hqdefault.jpg
[gplaps-video]:   https://www.youtube.com/watch?v=4NN2PSqCco8&t=305s

# Acknowledgements

* [RallyDataJunkie][rallydatajunkie]
* [Rally result chart sketches][chartskecthes]
* [E.J. Marey’s graphical train schedule chart][mareystrains]

[rallydatajunkie]: https://rallydatajunkie.com
[chartskecthes]:   https://blog.ouseful.info/2018/03/11/some-more-rally-result-chart-sketches/
[mareystrains]:    https://observablehq.com/@d3/mareys-trains
