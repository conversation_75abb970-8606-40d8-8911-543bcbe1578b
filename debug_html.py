#!/usr/bin/env python3

import requests
from bs4 import BeautifulSoup

def debug_html():
    """Debug HTML structure for missing entries #3 and #4"""

    # Fetch the first stage page
    url = "https://www.ewrc-results.com/results/94276/?s=493915"
    print(f"Fetching: {url}")

    response = requests.get(url)
    soup = BeautifulSoup(response.content, 'html.parser')
    page_html = str(soup)

    print(f"Page HTML length: {len(page_html)}")

    # Check what tables are found by different selectors
    tables_results = soup.select("main#main-section div#stage-results table.results")
    tables_main = soup.select("main#main-section table.results")
    tables_all_results = soup.select("table.results")
    tables_all = soup.select("main#main-section table")
    tables_any = soup.select("table")

    print(f"\nTable counts:")
    print(f"  main#main-section div#stage-results table.results: {len(tables_results)}")
    print(f"  main#main-section table.results: {len(tables_main)}")
    print(f"  table.results: {len(tables_all_results)}")
    print(f"  main#main-section table: {len(tables_all)}")
    print(f"  table (any): {len(tables_any)}")

    # Check which tables contain #3 and #4
    print(f"\nChecking which tables contain #3 and #4...")
    for i, table in enumerate(tables_any):
        table_html = str(table)
        has_3 = "#3" in table_html and '<span class="font-weight-bold text-primary fs-091">#3</span>' in table_html
        has_4 = "#4" in table_html and '<span class="font-weight-bold text-primary fs-091">#4</span>' in table_html
        if has_3 or has_4:
            print(f"  Table {i+1}: Contains #3={has_3}, #4={has_4}")
            # Show table classes and structure
            classes = table.get('class', [])
            print(f"    Classes: {classes}")
            rows = table.select("tr")
            print(f"    Rows: {len(rows)}")

    # Search for #3
    if "#3" in page_html:
        print(f"\nFound #3 in raw HTML")
        pos = 0
        for i in range(3):  # Find first 3 occurrences
            pos = page_html.find("#3", pos)
            if pos == -1:
                break
            start = max(0, pos - 150)
            end = min(len(page_html), pos + 150)
            context = page_html[start:end]
            print(f"\n#3 occurrence {i+1} (position {pos}):")
            print(f"Context: {repr(context)}")
            pos += 1
    else:
        print("#3 NOT found in raw HTML")

    # Search for #4
    if "#4" in page_html:
        print(f"\nFound #4 in raw HTML")
        pos = 0
        for i in range(3):  # Find first 3 occurrences
            pos = page_html.find("#4", pos)
            if pos == -1:
                break
            start = max(0, pos - 150)
            end = min(len(page_html), pos + 150)
            context = page_html[start:end]
            print(f"\n#4 occurrence {i+1} (position {pos}):")
            print(f"Context: {repr(context)}")
            pos += 1
    else:
        print("#4 NOT found in raw HTML")

if __name__ == "__main__":
    debug_html()
