{"{\"organization\":\"com.monovore\",\"name\":\"decline-effect\",\"revision\":\"2.5.0\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\build.sbt", "startLine": 103}, "type": "LinePosition"}, "{\"organization\":\"org.tpolecat\",\"name\":\"doobie-core\",\"revision\":\"1.0.0-RC9\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\build.sbt", "startLine": 103}, "type": "LinePosition"}, "{\"organization\":\"com.rallyhealth\",\"name\":\"scalacheck-ops_1\",\"revision\":\"2.12.0\",\"configurations\":\"test\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\build.sbt", "startLine": 103}, "type": "LinePosition"}, "{\"organization\":\"org.typelevel\",\"name\":\"otel4s-instrumentation-metrics\",\"revision\":\"0.12.0\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\build.sbt", "startLine": 103}, "type": "LinePosition"}, "{\"organization\":\"io.github.iltotore\",\"name\":\"iron\",\"revision\":\"3.0.1\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\build.sbt", "startLine": 103}, "type": "LinePosition"}, "{\"organization\":\"com.github.geirolz\",\"name\":\"fly4s-core\",\"revision\":\"1.0.0\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\build.sbt", "startLine": 103}, "type": "LinePosition"}, "{\"organization\":\"org.tpolecat\",\"name\":\"doobie-munit\",\"revision\":\"1.0.0-RC9\",\"configurations\":\"test\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\build.sbt", "startLine": 103}, "type": "LinePosition"}, "{\"organization\":\"io.bullet\",\"name\":\"borer-compat-circe\",\"revision\":\"1.16.1\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\build.sbt", "startLine": 103}, "type": "LinePosition"}, "{\"organization\":\"org.http4s\",\"name\":\"http4s-otel4s-middleware-trace-server\",\"revision\":\"0.10.0\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\build.sbt", "startLine": 103}, "type": "LinePosition"}, "{\"organization\":\"org.scalameta\",\"name\":\"munit\",\"revision\":\"1.1.1\",\"configurations\":\"test\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\build.sbt", "startLine": 103}, "type": "LinePosition"}, "{\"organization\":\"org.typelevel\",\"name\":\"scalacheck-effect-munit\",\"revision\":\"2.0.0-M2\",\"configurations\":\"test\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\build.sbt", "startLine": 103}, "type": "LinePosition"}, "{\"organization\":\"io.github.arainko\",\"name\":\"ducktape\",\"revision\":\"0.2.8\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\build.sbt", "startLine": 103}, "type": "LinePosition"}, "{\"organization\":\"io.github.iltotore\",\"name\":\"iron-doobie\",\"revision\":\"3.0.1\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\build.sbt", "startLine": 103}, "type": "LinePosition"}, "{\"organization\":\"org.xerial\",\"name\":\"sqlite-jdbc\",\"revision\":\"3.49.1.0\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Disabled\"}}": {"value": {"$fields": ["path", "startLine"], "path": "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\build.sbt", "startLine": 103}, "type": "LinePosition"}, "{\"organization\":\"io.opentelemetry\",\"name\":\"opentelemetry-sdk-extension-autoconfigure\",\"revision\":\"1.50.0\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Disabled\"}}": {"value": {"$fields": ["path", "startLine"], "path": "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\build.sbt", "startLine": 103}, "type": "LinePosition"}, "{\"organization\":\"org.typelevel\",\"name\":\"log4cats-core\",\"revision\":\"2.7.0\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\build.sbt", "startLine": 103}, "type": "LinePosition"}, "{\"organization\":\"org.typelevel\",\"name\":\"munit-cats-effect\",\"revision\":\"2.1.0\",\"configurations\":\"test\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\build.sbt", "startLine": 103}, "type": "LinePosition"}, "{\"organization\":\"org.typelevel\",\"name\":\"otel4s-oteljava\",\"revision\":\"0.12.0\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\build.sbt", "startLine": 103}, "type": "LinePosition"}, "{\"organization\":\"org.typelevel\",\"name\":\"log4cats-slf4j\",\"revision\":\"2.7.0\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\build.sbt", "startLine": 103}, "type": "LinePosition"}, "{\"organization\":\"com.github.ghik\",\"name\":\"zerowaste\",\"revision\":\"1.0.0\",\"configurations\":\"plugin->default(compile)\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Full\",\"prefix\":\"\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\build.sbt", "startLine": 103}, "type": "LinePosition"}, "{\"organization\":\"com.softwaremill.sttp.tapir\",\"name\":\"tapir-http4s-client\",\"revision\":\"1.11.29\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\build.sbt", "startLine": 103}, "type": "LinePosition"}, "{\"organization\":\"ch.qos.logback\",\"name\":\"logback-classic\",\"revision\":\"1.5.18\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Disabled\"}}": {"value": {"$fields": ["path", "startLine"], "path": "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\build.sbt", "startLine": 103}, "type": "LinePosition"}, "{\"organization\":\"io.github.iltotore\",\"name\":\"iron-scalacheck\",\"revision\":\"3.0.1\",\"configurations\":\"test\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\build.sbt", "startLine": 103}, "type": "LinePosition"}, "{\"organization\":\"io.github.arturaz\",\"name\":\"otel4s-doobie\",\"revision\":\"0.5.0\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\build.sbt", "startLine": 103}, "type": "LinePosition"}, "{\"organization\":\"io.opentelemetry\",\"name\":\"opentelemetry-exporter-otlp\",\"revision\":\"1.50.0\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Disabled\"}}": {"value": {"$fields": ["path", "startLine"], "path": "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\build.sbt", "startLine": 103}, "type": "LinePosition"}, "{\"organization\":\"com.themillhousegroup\",\"name\":\"scoup\",\"revision\":\"1.0.0\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\build.sbt", "startLine": 103}, "type": "LinePosition"}, "{\"organization\":\"org.http4s\",\"name\":\"http4s-ember-client\",\"revision\":\"0.23.30\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\build.sbt", "startLine": 103}, "type": "LinePosition"}, "{\"organization\":\"org.gnieh\",\"name\":\"fs2-data-csv\",\"revision\":\"1.12.0\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\build.sbt", "startLine": 103}, "type": "LinePosition"}, "{\"organization\":\"org.scalameta\",\"name\":\"svm-subs\",\"revision\":\"101.0.0\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Disabled\"}}": {"value": {"$fields": ["path", "startLine"], "path": "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\build.sbt", "startLine": 103}, "type": "LinePosition"}, "{\"organization\":\"org.http4s\",\"name\":\"http4s-ember-server\",\"revision\":\"0.23.30\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\build.sbt", "startLine": 103}, "type": "LinePosition"}, "{\"organization\":\"com.github.jatcwang\",\"name\":\"difflicious-munit\",\"revision\":\"0.4.3\",\"configurations\":\"test\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\build.sbt", "startLine": 103}, "type": "LinePosition"}, "{\"organization\":\"com.softwaremill.sttp.tapir\",\"name\":\"tapir-http4s-server\",\"revision\":\"1.11.29\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\build.sbt", "startLine": 103}, "type": "LinePosition"}, "{\"organization\":\"org.scala-lang\",\"name\":\"scala3-library\",\"revision\":\"3.6.4\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\build.sbt", "startLine": 103}, "type": "LinePosition"}, "{\"organization\":\"org.http4s\",\"name\":\"http4s-otel4s-middleware-trace-client\",\"revision\":\"0.10.0\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\build.sbt", "startLine": 103}, "type": "LinePosition"}, "{\"organization\":\"org.scalameta\",\"name\":\"munit-scalacheck\",\"revision\":\"1.1.0\",\"configurations\":\"test\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "C:\\Users\\<USER>\\Documents\\Projects\\scalaproject\\build.sbt", "startLine": 103}, "type": "LinePosition"}}