[0m[[0m[0mdebug[0m] [0m[0mAbout to create/update header for C:\Users\<USER>\Documents\Projects\scalaproject\modules\frontend\src\main\scala\components\ResultFilter.scala[0m
[0m[[0m[0mdebug[0m] [0m[0mAbout to create/update header for C:\Users\<USER>\Documents\Projects\scalaproject\modules\frontend\src\main\scala\components\About.scala[0m
[0m[[0m[0mdebug[0m] [0m[0mAbout to create/update header for C:\Users\<USER>\Documents\Projects\scalaproject\modules\frontend\src\main\scala\Util.scala[0m
[0m[[0m[0mdebug[0m] [0m[0mAbout to create/update header for C:\Users\<USER>\Documents\Projects\scalaproject\modules\frontend\src\main\scala\components\RallyList.scala[0m
[0m[[0m[0mdebug[0m] [0m[0mAbout to create/update header for C:\Users\<USER>\Documents\Projects\scalaproject\modules\frontend\src\main\scala\Main.scala[0m
[0m[[0m[0mdebug[0m] [0m[0mAbout to create/update header for C:\Users\<USER>\Documents\Projects\scalaproject\modules\frontend\src\main\scala\Router.scala[0m
[0m[[0m[0mdebug[0m] [0m[0mAbout to create/update header for C:\Users\<USER>\Documents\Projects\scalaproject\modules\frontend\src\main\scala\Results.scala[0m
[0m[[0m[0mdebug[0m] [0m[0mAbout to create/update header for C:\Users\<USER>\Documents\Projects\scalaproject\modules\frontend\src\main\scala\components\Header.scala[0m
[0m[[0m[0mdebug[0m] [0m[0mAbout to create/update header for C:\Users\<USER>\Documents\Projects\scalaproject\modules\frontend\src\main\scala\components\ResultLines.scala[0m
[0m[[0m[0mdebug[0m] [0m[0mAbout to create/update header for C:\Users\<USER>\Documents\Projects\scalaproject\modules\frontend\src\main\scala\components\RallyResult.scala[0m
[0m[[0m[0mdebug[0m] [0m[0mAbout to create/update header for C:\Users\<USER>\Documents\Projects\scalaproject\modules\frontend\src\main\scala\components\Alert.scala[0m
