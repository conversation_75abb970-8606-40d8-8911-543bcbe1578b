name: Auto approve and merge dependency bump PRs

on: pull_request

permissions:
  contents: write
  pull-requests: write

jobs:
  auto-approve:
    runs-on: ubuntu-latest
    if: github.event.pull_request.user.login == 'dependabot[bot]' || github.event.pull_request.user.login == 'scala-steward-2m[bot]'
    steps:
    - run: gh pr review --approve "$PR_URL"
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        PR_URL: ${{ github.event.pull_request.html_url }}
    - run: gh pr merge --auto --squash "$PR_URL"
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        PR_URL: ${{ github.event.pull_request.html_url }}
