[0m[[0m[0mdebug[0m] [0m[0m[zinc] IncrementalCompile -----------[0m
[0m[[0m[0mdebug[0m] [0m[0mIncrementalCompile.incrementalCompile[0m
[0m[[0m[0mdebug[0m] [0m[0mprevious = Stamps for: 0 products, 0 sources, 0 libraries[0m
[0m[[0m[0mdebug[0m] [0m[0mcurrent source = Set(${BASE}/modules/shared/src/main/scala/borer.scala, ${BASE}/modules/shared/src/main/scala/iron.scala, ${BASE}/modules/shared/src/main/scala/Model.scala, ${BASE}/modules/shared/src/main/scala/Results.scala)[0m
[0m[[0m[0mdebug[0m] [0m[0m> initialChanges = InitialChanges(Changes(added = Set(${BASE}/modules/shared/src/main/scala/iron.scala, ${BASE}/modules/shared/src/main/scala/Results.scala, ${BASE}/modules/shared/src/main/scala/Model.scala, ${BASE}/modules/shared/src/main/scala/borer.scala), removed = Set(), changed = Set(), unmodified = ...),Set(),Set(),API Changes: Set())[0m
[0m[[0m[0mdebug[0m] [0m[0mFull compilation, no sources in previous analysis.[0m
[0m[[0m[0mdebug[0m] [0m[0mall 4 sources are invalidated[0m
[0m[[0m[0mdebug[0m] [0m[0mInitial set of included nodes: [0m
[0m[[0m[0mdebug[0m] [0m[0mRecompiling all sources: number of invalidated sources > 50.0 percent of all sources[0m
[0m[[0m[0mdebug[0m] [0m[0mcompilation cycle 1[0m
[0m[[0m[0minfo[0m] [0m[0mcompiling 4 Scala sources to C:\Users\<USER>\Documents\Projects\scalaproject\modules\shared\.jvm\target\scala-3.6.4\classes ...[0m
[0m[[0m[0mdebug[0m] [0m[0mReturning already retrieved and compiled bridge: C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\scala-lang\scala3-sbt-bridge\3.6.4\scala3-sbt-bridge-3.6.4.jar.[0m
[0m[[0m[0mdebug[0m] [0m[0m[zinc] Running cached compiler 3588956d for Scala Compiler version 3.6.4[0m
[0m[[0m[0mdebug[0m] [0m[0m[zinc] The Scala compiler is invoked with:[0m
[0m[[0m[0mdebug[0m] [0m[0m	-encoding[0m
[0m[[0m[0mdebug[0m] [0m[0m	utf8[0m
[0m[[0m[0mdebug[0m] [0m[0m	-deprecation[0m
[0m[[0m[0mdebug[0m] [0m[0m	-feature[0m
[0m[[0m[0mdebug[0m] [0m[0m	-unchecked[0m
[0m[[0m[0mdebug[0m] [0m[0m	-language:experimental.macros[0m
[0m[[0m[0mdebug[0m] [0m[0m	-language:higherKinds[0m
[0m[[0m[0mdebug[0m] [0m[0m	-language:implicitConversions[0m
[0m[[0m[0mdebug[0m] [0m[0m	-Xkind-projector[0m
[0m[[0m[0mdebug[0m] [0m[0m	-Wvalue-discard[0m
[0m[[0m[0mdebug[0m] [0m[0m	-Wnonunit-statement[0m
[0m[[0m[0mdebug[0m] [0m[0m	-Wunused:implicits[0m
[0m[[0m[0mdebug[0m] [0m[0m	-Wunused:explicits[0m
[0m[[0m[0mdebug[0m] [0m[0m	-Wunused:imports[0m
[0m[[0m[0mdebug[0m] [0m[0m	-Wunused:locals[0m
[0m[[0m[0mdebug[0m] [0m[0m	-Wunused:params[0m
[0m[[0m[0mdebug[0m] [0m[0m	-Wunused:privates[0m
[0m[[0m[0mdebug[0m] [0m[0m	-Xfatal-warnings[0m
[0m[[0m[0mdebug[0m] [0m[0m	-Xmax-inlines[0m
[0m[[0m[0mdebug[0m] [0m[0m	64[0m
[0m[[0m[0mdebug[0m] [0m[0m	-Xplugin:C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\github\ghik\zerowaste_3.6.4\1.0.0\zerowaste_3.6.4-1.0.0.jar[0m
[0m[[0m[0mdebug[0m] [0m[0m	-classpath[0m
[0m[[0m[0mdebug[0m] [0m[0m	C:\Users\<USER>\Documents\Projects\scalaproject\modules\shared\.jvm\target\scala-3.6.4\classes;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\scala-lang\scala3-library_3\3.6.4\scala3-library_3-3.6.4.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\softwaremill\sttp\tapir\tapir-core_3\1.11.29\tapir-core_3-1.11.29.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\io\bullet\borer-derivation_3\1.16.1\borer-derivation_3-1.16.1.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\io\github\iltotore\iron_3\3.0.1\iron_3-3.0.1.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\io\github\iltotore\iron-borer_3\3.0.1\iron-borer_3-3.0.1.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\scala-lang\scala-library\2.13.15\scala-library-2.13.15.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\softwaremill\sttp\model\core_3\1.7.14\core_3-1.7.14.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\softwaremill\sttp\shared\core_3\1.5.0\core_3-1.5.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\softwaremill\sttp\shared\ws_3\1.5.0\ws_3-1.5.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\softwaremill\magnolia1_3\magnolia_3\1.3.16\magnolia_3-1.3.16.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\io\bullet\borer-core_3\1.16.1\borer-core_3-1.16.1.jar[0m
[0m[[0m[0mdebug[0m] [0m[0mScala compilation took 77.1531043 s[0m
[0m[[0m[0mdebug[0m] [0m[0mdone compiling[0m
